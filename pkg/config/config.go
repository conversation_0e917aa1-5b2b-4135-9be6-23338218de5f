package config

import (
	"log"
	"os"
)

var Environment string
var CredentialKey string
var Modules string

// redis config
var RedisDb string
var RedisUrl string
var RedisPass string
var RedisPort string

// db config
var DbHost string
var MainDbName string
var UserDbName string
var OmsDbName string

// shopee config
var ShopeePartnerId string
var ShopeePartnerKey string
var ShopeeBaseUrl string

func Init() {
	Environment = os.Getenv("ENVIRONMENT")
	CredentialKey = os.Getenv("CREDENTIAL_KEY")
	Modules = os.Getenv("MODULES")
	RedisDb = os.Getenv("REDIS_DB")
	RedisUrl = os.Getenv("REDIS_URL")
	RedisPass = os.Getenv("REDIS_PASS")
	RedisPort = os.Getenv("REDIS_PORT")
	DbHost = os.Getenv("MONGO_URL")
	MainDbName = os.Getenv("MONGO_MAIN_DATABASE")
	UserDbName = os.Getenv("MONGO_USER_DATABASE")
	OmsDbName = os.Getenv("MONGO_OMS_DATABASE")
	ShopeePartnerId = os.Getenv("SHOPEE_PARTNER_ID")
	ShopeePartnerKey = os.Getenv("SHOPEE_PARTNER_KEY")
	ShopeeBaseUrl = os.Getenv("SHOPEE_BASE_URL")

	errors := make([]string, 0)
	if CredentialKey == "" {
		errors = append(errors, "CREDENTIAL_KEY is required")
	}
	if Modules == "" {
		errors = append(errors, "MODULES is required")
	}
	if RedisDb == "" {
		errors = append(errors, "REDIS_DB is required")
	}
	if RedisUrl == "" {
		errors = append(errors, "REDIS_URL is required")
	}
	if DbHost == "" {
		errors = append(errors, "MONGO_URL is required")
	}
	if MainDbName == "" {
		errors = append(errors, "MONGO_MAIN_DATABASE is required")
	}
	if UserDbName == "" {
		errors = append(errors, "MONGO_USER_DATABASE is required")
	}
	if OmsDbName == "" {
		errors = append(errors, "MONGO_OMS_DATABASE is required")
	}
	if ShopeePartnerId == "" {
		errors = append(errors, "SHOPEE_PARTNER_ID is required")
	}
	if ShopeePartnerKey == "" {
		errors = append(errors, "SHOPEE_PARTNER_KEY is required")
	}
	if ShopeeBaseUrl == "" {
		errors = append(errors, "SHOPEE_BASE_URL is required")
	}
	if Environment == "" {
		errors = append(errors, "ENVIRONMENT is required")
	}

	if len(errors) > 0 {
		log.Println("Some required configurations missing")
		for _, msg := range errors {
			log.Println(msg)
		}
		panic("Server Shutdown")
	}
}
