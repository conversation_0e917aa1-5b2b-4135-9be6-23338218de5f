package redis

import (
	"log"
	"strconv"

	"abs-scheduler-go/pkg/config"
	"abs-scheduler-go/pkg/util"
	"github.com/redis/go-redis/v9"
)

func Connect() *redis.Client {
	redisClient, redisPass := util.GetRedisEnv()
	redisDb := config.RedisDb
	if redisDb == "" {
		redisDb = "0"
	}
	redisDbParsed, err := strconv.Atoi(redisDb)
	if err != nil {
		log.Fatal("REDIS_DB environment invalid value")
	}

	client := redis.NewClient(&redis.Options{
		Addr:     redisClient,
		Password: redisPass,
		DB:       redisDbParsed,
	})

	return client
}
