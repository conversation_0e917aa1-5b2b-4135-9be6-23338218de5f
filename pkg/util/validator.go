package util

import (
	"fmt"
	"net/http"
	"regexp"
	"strconv"

	"github.com/gorilla/mux"
)

// ValidationContext holds the context for validation
type ValidationContext struct {
	Path   string                 // Current path in the validation tree
	Data   map[string]interface{} // Data being validated
	Errors map[string]string      // Validation errors
}

// ValidateRequest validates request data against provided rules
func ValidateRequest(r *http.Request, source ValidationSource, rules []ValidationRule) (map[string]interface{}, map[string]string, error) {
	// Extract data based on source
	dataToCheck, err := extractDataFromRequest(r, source)
	if err != nil {
		return nil, nil, err
	}

	// Create validation context
	ctx := ValidationContext{
		Path:   "",
		Data:   dataToCheck,
		Errors: make(map[string]string),
	}

	// Validate data against rules
	validateData(ctx, rules)

	// Return results
	if len(ctx.Errors) > 0 {
		return dataToCheck, ctx.Errors, nil
	}
	return dataToCheck, nil, nil
}

// extractDataFromRequest extracts data from the request based on the source
func extractDataFromRequest(r *http.Request, source ValidationSource) (map[string]interface{}, error) {
	dataToCheck := make(map[string]interface{})

	switch source {
	case ValidationSourceBody:
		rawBody, err := DecodeJSON(r.Body)
		if err != nil {
			return nil, fmt.Errorf("failed to decode request body: %v", err)
		}
		dataToCheck = rawBody

	case ValidationSourceParam:
		params := mux.Vars(r)
		for k, v := range params {
			dataToCheck[k] = v
		}

	case ValidationSourceQuery:
		query := r.URL.Query()
		for k, v := range query {
			if len(v) == 1 {
				dataToCheck[k] = v[0]
			} else {
				dataToCheck[k] = v
			}
		}
	}

	return dataToCheck, nil
}

// validateData validates data against rules
func validateData(ctx ValidationContext, rules []ValidationRule) {
	for _, rule := range rules {
		fieldPath := joinPath(ctx.Path, rule.Field)
		value, exists := ctx.Data[rule.Field]

		// Apply validation rules
		applyRules(ctx, fieldPath, rule, value, exists)

		// Type validation and conversion
		if exists && value != nil && len(ctx.Errors[fieldPath]) == 0 {
			validateType(ctx, fieldPath, rule, value)
		}
	}
}

// applyRules applies validation rules to a field
func applyRules(ctx ValidationContext, fieldPath string, rule ValidationRule, value interface{}, exists bool) {
	for _, r := range rule.Rule {
		switch r.Option {
		case RuleRequired:
			if !exists || value == nil || value == "" {
				ctx.Errors[fieldPath] = getErrorMessage(r, "field is required")
				return // Skip further validation if required field is missing
			}

		case RuleMaxLength:
			if exists && value != nil {
				str, ok := value.(string)
				if ok && len(str) > r.Value.(int) {
					ctx.Errors[fieldPath] = getErrorMessage(r, fmt.Sprintf("exceeds maximum length of %v", r.Value))
				}
			}

		case RuleMinLength:
			if exists && value != nil {
				str, ok := value.(string)
				if ok && len(str) < r.Value.(int) {
					ctx.Errors[fieldPath] = getErrorMessage(r, fmt.Sprintf("below minimum length of %v", r.Value))
				}
			}

		case RulePattern:
			if exists && value != nil {
				str, ok := value.(string)
				if ok {
					ctx.Errors[fieldPath] = getErrorMessage(r, fmt.Sprintf("below minimum length of %v", r.Value))
				}

				check, err := regexp.Match(r.Value.(string), []byte(str))
				if err != nil {
					ctx.Errors[fieldPath] = getErrorMessage(r, "invalid pattern")
				} else if !check {
					ctx.Errors[fieldPath] = getErrorMessage(r, "does not match the pattern")
				}
			}

		case RuleMaxElement:
			if exists && value != nil {
				arr, ok := value.([]interface{})
				if ok && len(arr) > r.Value.(int) {
					ctx.Errors[fieldPath] = getErrorMessage(r, fmt.Sprintf("exceeds maximum number of elements of %v", r.Value))
				}
			}
		}
	}
}

// validateType validates and converts the type of a field
func validateType(ctx ValidationContext, fieldPath string, rule ValidationRule, value interface{}) {
	switch rule.Format {
	case ValidationDataTypeNumber:
		validateNumber(ctx, fieldPath, rule, value)

	case ValidationDataTypeString:
		if _, ok := value.(string); !ok {
			ctx.Errors[fieldPath] = "invalid data type, expected string"
		}

	case ValidationDataTypeBoolean:
		if _, ok := value.(bool); !ok {
			ctx.Errors[fieldPath] = "invalid data type, expected boolean"
		}

	case ValidationDataTypeArray:
		validateArray(ctx, fieldPath, rule, value)

	case ValidationDataTypeObject:
		validateObject(ctx, fieldPath, rule, value)
	}
}

// validateNumber validates and converts number types
func validateNumber(ctx ValidationContext, fieldPath string, rule ValidationRule, value interface{}) {
	switch v := value.(type) {
	case float64:
		ctx.Data[rule.Field] = v
	case int:
		ctx.Data[rule.Field] = v
	case string:
		// Try to convert string to number
		if f, err := strconv.ParseFloat(v, 64); err == nil {
			ctx.Data[rule.Field] = f
		} else {
			ctx.Errors[fieldPath] = "invalid data type, expected number"
		}
	default:
		ctx.Errors[fieldPath] = "invalid data type, expected number"
	}
}

// validateArray validates array types and their items
func validateArray(ctx ValidationContext, fieldPath string, rule ValidationRule, value interface{}) {
	items, ok := value.([]interface{})
	if !ok {
		ctx.Errors[fieldPath] = "invalid data type, expected array"
		return
	}

	// If we have nested rules, validate each item
	if rule.NestedRules != nil && len(rule.NestedRules) > 0 {
		for i, item := range items {
			itemPath := fmt.Sprintf("%s.%d", fieldPath, i)

			// For objects in the array
			if itemMap, ok := item.(map[string]interface{}); ok {
				itemCtx := ValidationContext{
					Path:   itemPath,
					Data:   itemMap,
					Errors: ctx.Errors,
				}
				validateData(itemCtx, rule.NestedRules)
			} else {
				ctx.Errors[itemPath] = "invalid data type, expected object"
			}
		}
	}
}

// validateObject validates object types and their properties
func validateObject(ctx ValidationContext, fieldPath string, rule ValidationRule, value interface{}) {
	objMap, ok := value.(map[string]interface{})
	if !ok {
		ctx.Errors[fieldPath] = "invalid data type, expected object"
		return
	}

	// If we have nested rules, validate the object properties
	if rule.NestedRules != nil && len(rule.NestedRules) > 0 {
		objCtx := ValidationContext{
			Path:   fieldPath,
			Data:   objMap,
			Errors: ctx.Errors,
		}
		validateData(objCtx, rule.NestedRules)
	}
}

// Helper functions
func joinPath(base, field string) string {
	if base == "" {
		return field
	}
	return fmt.Sprintf("%s.%s", base, field)
}

func getErrorMessage(rule Rule, defaultMsg string) string {
	if rule.Message != "" {
		return rule.Message
	}
	return defaultMsg
}

type RuleOption string

const (
	RuleRequired   RuleOption = "required"
	RuleMaxLength  RuleOption = "max_length"
	RuleMinLength  RuleOption = "min_length"
	RulePattern    RuleOption = "pattern"
	RuleMaxElement RuleOption = "max_element"
)

type Rule struct {
	Option  RuleOption
	Value   any
	Message string
}

type ValidationSource string

const (
	ValidationSourceBody  ValidationSource = "body"
	ValidationSourceParam ValidationSource = "param"
	ValidationSourceQuery ValidationSource = "query"
)

type ValidationDataType string

const (
	ValidationDataTypeNumber  ValidationDataType = "number"
	ValidationDataTypeString  ValidationDataType = "string"
	ValidationDataTypeBoolean ValidationDataType = "boolean"
	ValidationDataTypeArray   ValidationDataType = "array"
	ValidationDataTypeObject  ValidationDataType = "object"
)

// Add NestedRules to ValidationRule struct
type ValidationRule struct {
	Field       string
	Rule        []Rule
	Format      ValidationDataType
	NestedRules []ValidationRule // For validating nested objects or arrays
}
