package util

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"math"
	"reflect"
	"strings"
	"time"

	"abs-scheduler-go/pkg/config"
	"abs-scheduler-go/pkg/constant"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func Contains(arr []interface{}, search interface{}) bool {
	for _, item := range arr {
		if item == search {
			return true
		}
	}

	return false
}

func EncryptValue(data string, key string) (string, error) {
	sec := sha256.Sum256([]byte(key))
	secBase64 := base64.StdEncoding.EncodeToString(sec[:])
	secShort := secBase64[:32]

	iv := make([]byte, aes.BlockSize) // 16 bytes for AES block size
	if _, err := rand.Read(iv); err != nil {
		return "", fmt.Errorf("could not generate IV: %w", err)
	}

	block, err := aes.NewCipher([]byte(secShort))
	if err != nil {
		return "", fmt.Errorf("could not create cipher: %w", err)
	}

	mode := cipher.NewCBCEncrypter(block, iv)
	plaintext := []byte(data)

	//PKCS7 padding
	padding := aes.BlockSize - (len(plaintext) % aes.BlockSize)
	plaintext = append(plaintext, bytes.Repeat([]byte{byte(padding)}, padding)...)

	ciphertext := make([]byte, len(plaintext))
	mode.CryptBlocks(ciphertext, plaintext)

	encrypted := hex.EncodeToString(ciphertext)
	ivBase64 := base64.StdEncoding.EncodeToString(iv)

	return fmt.Sprintf("%s.%s", encrypted, ivBase64), nil
}

func DecryptValue(encryptedData string, key string) (string, error) {
	// Split encrypted data and IV
	parts := strings.Split(encryptedData, ".")
	if len(parts) != 2 {
		return "", errors.New("invalid encrypted data format")
	}

	// Decode ciphertext and IV
	ciphertext, err := hex.DecodeString(parts[0])
	if err != nil {
		return "", fmt.Errorf("failed to decode ciphertext: %w", err)
	}

	iv, err := base64.StdEncoding.DecodeString(parts[1])
	if err != nil {
		return "", fmt.Errorf("failed to decode IV: %w", err)
	}

	// Generate secret key (same as encryption)
	secShort := deriveKey(key)

	// Create AES cipher block
	block, err := aes.NewCipher([]byte(secShort))
	if err != nil {
		return "", fmt.Errorf("could not create cipher: %w", err)
	}

	// Decrypt using AES-256-CBC
	mode := cipher.NewCBCDecrypter(block, iv)
	plaintext := make([]byte, len(ciphertext))
	mode.CryptBlocks(plaintext, ciphertext)

	// Remove PKCS7 padding
	plaintext, err = removePKCS7Padding(plaintext)
	if err != nil {
		return "", err
	}

	return string(plaintext), nil
}

// Helper function: Derive key from input
func deriveKey(key string) string {
	sec := sha256.Sum256([]byte(key))
	secBase64 := base64.StdEncoding.EncodeToString(sec[:])
	return secBase64[:32]
}

// Helper function: Remove PKCS7 padding
func removePKCS7Padding(data []byte) ([]byte, error) {
	if len(data) == 0 {
		return nil, errors.New("decryption failed: empty plaintext")
	}

	padding := int(data[len(data)-1])
	if padding > len(data) || padding > aes.BlockSize {
		return nil, errors.New("decryption failed: invalid padding")
	}

	return data[:len(data)-padding], nil
}

func GetRedisEnv() (string, string) {
	redisClient := config.RedisUrl
	if redisClient == "" {
		log.Fatal("REDIS_URL environment variable not set")
	}
	if config.RedisPort != "" {
		redisClient += ":" + config.RedisPort
	}

	return redisClient, config.RedisPass
}

func StructToMap(s interface{}) (map[string]interface{}, error) {
	jsonData, err := json.Marshal(s)
	if err != nil {
		return nil, err
	}

	var result map[string]interface{}
	err = json.Unmarshal(jsonData, &result)
	return result, err
}

func IsSlice(v interface{}) bool {
	return reflect.TypeOf(v).Kind() == reflect.Slice
}

func PrintJson(data any) {
	jsonData, _ := json.MarshalIndent(data, "", "  ")
	log.Println(string(jsonData))
}

// UnmarshalJSON unmarshals the request body into a map.
func UnmarshalJSON(bodyBytes []byte) (map[string]interface{}, error) {
	var jsonData map[string]interface{}

	// Unmarshal the request body into the map
	if err := json.Unmarshal(bodyBytes, &jsonData); err != nil {
		return nil, err
	}

	return jsonData, nil
}

func DecodeJSON(bytes io.ReadCloser) (map[string]interface{}, error) {
	var jsonData map[string]interface{}

	// Unmarshal the request body into the map
	if err := json.NewDecoder(bytes).Decode(&jsonData); err != nil {
		return nil, err
	}

	return jsonData, nil
}

// validateMarketPlaceCode checks if a given marketplace code is valid
func ValidateMarketPlaceCode(code constant.MarketplaceCodeEnum) bool {
	var validMarketPlaces = map[constant.MarketplaceCodeEnum]bool{
		constant.MarketplaceCodeShopee:    true,
		constant.MarketplaceCodeTokopedia: true,
		constant.MarketplaceCodeBlibli:    true,
		constant.MarketplaceCodeTiktok:    true,
	}
	_, exists := validMarketPlaces[code]
	return exists
}

// FindRecursiveJSON searches for a given key in a nested JSON-like structure
// represented as a map[string]any. It recursively traverses the map and returns
// the first found value as a string.
func FindRecursiveJSON(key string, data map[string]any) (string, error) {
	// Iterate through the map key-value pairs
	for k, v := range data {
		// If the key matches the target key, process its value
		if k == key {
			switch val := v.(type) {
			case string:
				// If the value is a string, return it
				return val, nil
			case map[string]any:
				// If the value is a nested map, recurse into it
				return FindRecursiveJSON(key, val)
			default:
				// Convert non-string values to JSON string format
				jsonVal, err := json.Marshal(val)
				if err != nil {
					return "", errors.New("found key, but value cannot be converted to string")
				}
				return string(jsonVal), nil
			}
		}
		// If the value is a nested map, recurse into it
		if nestedMap, ok := v.(map[string]any); ok {
			if result, err := FindRecursiveJSON(key, nestedMap); err == nil {
				return result, nil
			}
		} else if nestedSlice, ok := v.([]any); ok {
			// If the value is a slice, iterate through its elements
			for _, item := range nestedSlice {
				if itemMap, ok := item.(map[string]any); ok {
					// Recursively search within each map in the slice
					if result, err := FindRecursiveJSON(key, itemMap); err == nil {
						return result, nil
					}
				}
			}
		}
	}
	// Return an error if the key is not found
	return "", errors.New("key not found")
}

func FindRecursiveShopID(marketPlaceCode constant.MarketplaceCodeEnum, jsonData map[string]any) (string, error) {
	switch marketPlaceCode {
	case constant.MarketplaceCodeShopee:
		result, err := FindRecursiveJSON("shop_id", jsonData)
		if err != nil || result == "" {
			log.Printf("Error finding shop_id: %v. Result: %s", err, result)
		}
		return result, nil
	case constant.MarketplaceCodeTiktok:
		result, err := FindRecursiveJSON("shop_id", jsonData)
		if err != nil || result == "" {
			return "", errors.New("Shop ID not found.")
		}
		return result, nil
	case constant.MarketplaceCodeTokopedia:
		result, err := FindRecursiveJSON("shop_id", jsonData)
		if err != nil || result == "" {
			return "", errors.New("Shop ID not found.")
		}
		return result, nil
	case constant.MarketplaceCodeBlibli:
		if value, exist := jsonData["store"]; exist {
			if value, ok := value.(map[string]any); ok {
				result, err := FindRecursiveJSON("code", value)
				if err != nil || result == "" {
					return "", errors.New("Shop ID not found.")
				}
				return result, nil
			}
		} else {
			result, err := FindRecursiveJSON("storeCode", jsonData)
			if err != nil || result == "" {
				return "", errors.New("Shop ID not found.")
			}
			return result, nil
		}
	}

	return "", errors.New("Shop ID not found.")
}

func ProcessUpdateFields(data map[string]interface{}) map[string]interface{} {
	result := make(map[string]interface{})
	FlattenJSON("", data, result)
	return result
}

func FlattenJSON(prefix string, data map[string]interface{}, result map[string]interface{}) {
	for key, value := range data {
		fullKey := key
		if prefix != "" {
			fullKey = prefix + "." + key
		}

		if vMap, ok := value.(map[string]interface{}); ok {
			// If value is a map, recursively flatten it
			FlattenJSON(fullKey, vMap, result)
		} else if !IsEmpty(value) {
			// Only add non-empty values to the result
			result[fullKey] = value
		}
	}
}

func IsEmpty(value interface{}) bool {
	if value == nil {
		return true
	}

	// Handle empty string, zero value, empty slices, and empty maps
	switch v := value.(type) {
	case string:
		return v == ""
	case []interface{}:
		return len(v) == 0
	case map[string]interface{}:
		return len(v) == 0
	default:
		return reflect.DeepEqual(value, reflect.Zero(reflect.TypeOf(value)).Interface())
	}
}

func AddTime(baseTime time.Time, addValue uint, addUnit string) time.Time {
	if addUnit == "SECOND" {
		return baseTime.Add(time.Duration(addValue) * time.Second)
	}
	if addUnit == "MINUTE" {
		return baseTime.Add(time.Duration(addValue) * time.Minute)
	}
	if addUnit == "HOUR" {
		return baseTime.Add(time.Duration(addValue) * time.Hour)
	}
	if addUnit == "DAY" {
		return baseTime.Add(time.Duration(addValue) * time.Hour * 24)
	}
	if addUnit == "WEEK" {
		return baseTime.Add(time.Duration(addValue) * time.Hour * 24 * 7)
	}
	if addUnit == "MONTH" {
		return baseTime.Add(time.Duration(addValue) * time.Hour * 24 * 30)
	}
	if addUnit == "YEAR" {
		return baseTime.Add(time.Duration(addValue) * time.Hour * 24 * 365)
	}

	return baseTime
}

func IntPow(base, exp int) int {
	result := 1
	for i := 0; i < exp; i++ {
		result *= base
	}
	return result
}

func MergeMap(map1, map2 map[string]interface{}) map[string]interface{} {
	merged := make(map[string]interface{})

	for k, v := range map1 {
		merged[k] = v
	}

	for k, v := range map2 {
		if vMap, ok := v.(map[string]interface{}); ok {
			if existing, found := merged[k].(map[string]interface{}); found {
				merged[k] = MergeMap(existing, vMap)
			} else {
				merged[k] = vMap
			}
		} else {
			merged[k] = v
		}
	}

	return merged
}

func SplitIntoBatches(items []map[string]interface{}, batchSize int) [][]map[string]interface{} {
	var batches [][]map[string]interface{}

	for i := 0; i < len(items); i += batchSize {
		end := i + batchSize
		if end > len(items) {
			end = len(items)
		}
		batches = append(batches, items[i:end])
	}

	return batches
}

func CombineData[T any](exist *T, newData *T) (T, error) {
	var zero T

	if exist == nil {
		if reflect.ValueOf(newData).IsNil() {
			return zero, fmt.Errorf("both existing and new data are nil")
		}

		// If the type has an Id field, try to set it to a new ObjectID
		newVal := reflect.ValueOf(newData).Elem()
		idField := newVal.FieldByName("Id")
		if idField.IsValid() && idField.CanSet() && idField.Type() == reflect.TypeOf(primitive.ObjectID{}) {
			idField.Set(reflect.ValueOf(primitive.NewObjectID()))
		}

		return *newData, nil
	}

	existVal := reflect.ValueOf(exist).Elem()
	newVal := reflect.ValueOf(newData).Elem()

	for i := 0; i < newVal.NumField(); i++ {
		existField := existVal.Field(i)
		newField := newVal.Field(i)

		if !existField.CanSet() {
			continue
		}

		if !newField.IsZero() && !reflect.DeepEqual(existField.Interface(), newField.Interface()) {
			existField.Set(newField)
		}
	}

	return *exist, nil
}

func ConvertToMapSlice(items []interface{}) ([]map[string]interface{}, error) {
	result := make([]map[string]interface{}, 0, len(items))
	for _, item := range items {
		itemMap, ok := item.(map[string]interface{})
		if !ok {
			return nil, errors.New("contains a non-object item")
		}
		result = append(result, itemMap)
	}
	return result, nil
}

// Splice replicates JavaScript's array.splice() functionality
// It modifies the slice in place and returns the removed elements
// - start: index at which to start changing the array
// - deleteCount: number of elements to remove
// - elements: elements to add at the start position
func Splice[T any](s *[]T, start int, deleteCount int, elements ...T) []T {
	arr := *s

	// Handle negative start index (count from end)
	if start < 0 {
		start = len(arr) + start
		if start < 0 {
			start = 0
		}
	}

	// Bound check
	if start > len(arr) {
		start = len(arr)
	}

	// Calculate how many elements to actually delete
	if deleteCount < 0 {
		deleteCount = 0
	}
	if start+deleteCount > len(arr) {
		deleteCount = len(arr) - start
	}

	// Store removed elements to return later
	removed := make([]T, deleteCount)
	copy(removed, arr[start:start+deleteCount])

	// Create the result slice
	result := make([]T, 0, len(arr)-deleteCount+len(elements))

	// Add elements before the splice point
	result = append(result, arr[:start]...)

	// Add new elements
	result = append(result, elements...)

	// Add elements after the splice point
	result = append(result, arr[start+deleteCount:]...)

	// Update the original slice
	*s = result

	return removed
}

func ObjectKeys(obj map[string]interface{}) []string {
	keys := make([]string, 0, len(obj))
	for k := range obj {
		keys = append(keys, k)
	}
	return keys
}

func ObjectValues(obj map[string]interface{}) []interface{} {
	values := make([]interface{}, 0, len(obj))
	for _, v := range obj {
		values = append(values, v)
	}
	return values
}

func RoundTo2DecimalPlaces(val float64) float32 {
	rounded := math.Round(val*100) / 100
	return float32(rounded)
}
