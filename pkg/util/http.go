package util

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"reflect"
	"strconv"
	"time"

	"abs-scheduler-go/internal/company"
	"abs-scheduler-go/pkg/database"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var redisClient *redis.Client
var companyServ *company.Repository

func InitRedisClient() {
	redisAddr, redisPass := GetRedisEnv()
	// Use the existing Connect function from your redis package
	redisClient = redis.NewClient(&redis.Options{
		Addr:     redisAddr,
		Password: redisPass,
		DB:       0,
	})
}

func InitCompanyService() {
	db := database.Init()
	companyServ = company.NewCompanyRepository(db)
}

type SendRequestOpts struct {
	Timeout time.Duration
	Url     string
	Method  string
	Params  map[string]interface{}
	Body    map[string]interface{}
	Headers map[string]interface{}
}

func SendRequest(ctx context.Context, opts SendRequestOpts) (*http.Response, error) {
	client := &http.Client{}

	if opts.Timeout == 0 {
		opts.Timeout = time.Second * 30
	}

	parsedUrl, err := url.Parse(opts.Url)
	if err != nil {
		log.Printf("failed to parse url: %v", err)
		return nil, err
	}

	if opts.Params != nil {
		query := parsedUrl.Query()
		for key, value := range opts.Params {
			if IsSlice(value) {
				values := reflect.ValueOf(value)
				for i := 0; i < values.Len(); i++ {
					query.Add(key, fmt.Sprintf("%v", values.Index(i).Interface()))
				}
			} else {
				str := fmt.Sprintf("%v", value)
				query.Set(key, str)
			}
		}
		parsedUrl.RawQuery = query.Encode()
	}

	var requestBody io.Reader
	if opts.Body != nil {
		jsonBody, err := json.Marshal(opts.Body)
		if err != nil {
			log.Printf("failed to marshal body: %v", err)
			return nil, err
		}
		requestBody = bytes.NewBuffer(jsonBody)
	}

	req, err := http.NewRequestWithContext(ctx, opts.Method, parsedUrl.String(), requestBody)
	if err != nil {
		log.Printf("failed to create request: %v", err)
		return nil, err
	}

	for key, value := range opts.Headers {
		str := fmt.Sprintf("%v", value)
		req.Header.Set(key, str)
	}

	res, err := client.Do(req)

	if err != nil {
		log.Printf("failed to send request: %v", err)
		return nil, err
	}

	return res, nil
}

func SendResponse(w http.ResponseWriter, statusCode int, message string, reason string, data interface{}) {
	w.WriteHeader(statusCode)

	if statusCode == http.StatusPreconditionFailed {
		data = []interface{}{data}
	}
	if data == nil {
		data = make([]interface{}, 0)
	}

	json.NewEncoder(w).Encode(map[string]interface{}{
		"message": message,
		"reason":  reason,
		"data":    data,
	})
}

type Metadata struct {
	Status      int     `json:"status"`
	ProcessTime float64 `json:"process_time"`
	Message     string  `json:"message"`
	Reason      string  `json:"reason"`
	RequestId   string  `json:"request_id"`
}

// StandardResponse defines the response format
type StandardResponse struct {
	Meta Metadata `json:"meta"`
	//Status  int         `json:"status"`
	//Message string      `json:"message"`
	Data interface{} `json:"data"`
}

// ResponseWriterInterceptor captures the response body
type ResponseWriterInterceptor struct {
	http.ResponseWriter
	Body       *bytes.Buffer
	StatusCode int
}

// Write captures the response body without sending it immediately
func (rw *ResponseWriterInterceptor) Write(b []byte) (int, error) {
	return rw.Body.Write(b) // Store response in buffer, don't write to original ResponseWriter
}

// WriteHeader captures the status code
func (rw *ResponseWriterInterceptor) WriteHeader(statusCode int) {
	rw.StatusCode = statusCode
}

// InterceptResponseMiddleware formats the response
func InterceptResponseMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()

		ctx := context.WithValue(r.Context(), "startTime", start)
		ctx = context.WithValue(r.Context(), "requestId", uuid.NewString())

		// Wrap the response writer
		interceptor := &ResponseWriterInterceptor{
			ResponseWriter: w,
			Body:           &bytes.Buffer{},
			StatusCode:     http.StatusOK, // Default to 200
		}

		// Call the next handler
		next.ServeHTTP(interceptor, r.WithContext(ctx))

		formattedResponse := StandardResponse{
			Meta: Metadata{
				Status:      interceptor.StatusCode,
				ProcessTime: time.Since(start).Seconds(),
				Message:     "Failed",
				Reason:      "",
				RequestId:   ctx.Value("requestId").(string),
			},
			Data: interceptor.Body.Bytes(),
		}

		// Try to decode the response as JSON
		var originalData map[string]interface{}
		err := json.Unmarshal(interceptor.Body.Bytes(), &originalData)
		if err == nil {
			// Wrap response in a structured format
			formattedResponse.Meta.Message = originalData["message"].(string)
			formattedResponse.Meta.Reason = originalData["reason"].(string)
			formattedResponse.Data = originalData["data"]
			//formattedResponse := StandardResponse{
			//	Meta: Metadata{
			//		Status:      interceptor.statusCode,
			//		ProcessTime: time.Since(start).Seconds(),
			//		Message:     ,
			//		Reason:      "",
			//	},
			//	Status:  interceptor.statusCode,
			//	Message: "Success",
			//	Data:    originalData,
			//}

			// Clear original response and send formatted response
			w.Header().Set("Content-Type", "application/json")
			w.WriteHeader(interceptor.StatusCode)
			json.NewEncoder(w).Encode(formattedResponse)
			return
		}

		// If response is not JSON, send the original raw data
		w.WriteHeader(interceptor.StatusCode)
		w.Write(interceptor.Body.Bytes())
	})
}

func ValidateToken(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		path := r.URL.Path
		ctx := context.WithValue(r.Context(), "company", map[string]interface{}{})

		header := r.Header.Get("x-api-token")
		if header == "" {
			SendResponse(w, http.StatusUnauthorized, "unauthorized", "token not found", nil)
			return
		}

		companyHeader := r.Header.Get("x-api-company")
		if companyHeader == "" {
			SendResponse(w, http.StatusUnauthorized, "unauthorized", "company not found", nil)
			return
		}

		timestamp := r.Header.Get("x-api-timestamp")
		if timestamp == "" {
			SendResponse(w, http.StatusUnauthorized, "unauthorized", "timestamp not found", nil)
			return
		}

		timestampInt, err := strconv.ParseInt(timestamp, 10, 64)
		if err != nil {
			SendResponse(w, http.StatusUnauthorized, "unauthorized", "invalid timestamp", nil)
			return
		}

		if time.Since(time.Unix(timestampInt, 0)) > 5*time.Minute {
			SendResponse(w, http.StatusUnauthorized, "unauthorized", "token expired", nil)
			return
		}

		if redisClient == nil {
			InitRedisClient()
		}

		cache, err := redisClient.Get(context.Background(), fmt.Sprintf("private-key-%s", companyHeader)).Result()
		if err != nil {
			if !errors.Is(err, redis.Nil) {
				SendResponse(w, http.StatusInternalServerError, "internal server error", "cannot connect to redis", nil)
				return
			}
		}

		if companyServ == nil {
			InitCompanyService()
		}

		privateKey := ""
		if cache == "" {
			parse, err := primitive.ObjectIDFromHex(companyHeader)
			if err != nil {
				SendResponse(w, http.StatusUnauthorized, "unauthorized", "company not found", nil)
				return
			}

			companyData, err := companyServ.FindById(parse)
			if err != nil {
				SendResponse(w, http.StatusUnauthorized, "unauthorized", "company not found", nil)
				return
			}

			privateKey = companyData.PrivateKey
			convert, err := StructToMap(companyData)
			if err != nil {
				SendResponse(w, http.StatusInternalServerError, "internal server error", "cannot convert company data to map", nil)
				return
			}

			redisClient.Set(context.Background(), fmt.Sprintf("private-key-%s", companyHeader), convert, 0)
			ctx = context.WithValue(r.Context(), "company", convert)
		} else {
			decoded, err := UnmarshalJSON([]byte(privateKey))
			if err != nil {
				SendResponse(w, http.StatusInternalServerError, "internal server error", "cannot decode private cache", nil)
				return
			}
			ctx = context.WithValue(r.Context(), "company", decoded)
			privateKey = decoded["private_key"].(string)
		}

		if privateKey == "" {
			SendResponse(w, http.StatusUnauthorized, "unauthorized", "invalid token", nil)
			return
		}

		sign, err := generateSign(privateKey, fmt.Sprintf("%s%s%s", path, timestamp, companyHeader))
		if err != nil {
			SendResponse(w, http.StatusInternalServerError, "internal server error", "cannot generate sign", nil)
			return
		}

		if sign != header {
			SendResponse(w, http.StatusUnauthorized, "unauthorized", "invalid token", nil)
			return
		}

		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func GetCompanyData(r *http.Request) map[string]interface{} {
	if value, ok := r.Context().Value("company").(map[string]interface{}); ok {
		return value
	}
	return nil
}

func GetRequestIdData(r *http.Request) string {
	if value, ok := r.Context().Value("requestId").(string); ok {
		return value
	}
	return ""
}

func generateSign(partnerKey string, data string) (string, error) {
	h := hmac.New(sha256.New, []byte(partnerKey))
	h.Write([]byte(data))

	return hex.EncodeToString(h.Sum(nil)), nil
}
