package database

import (
	"log"

	"abs-scheduler-go/pkg/config"
	"github.com/joho/godotenv"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func Init() map[string]*mongo.Database {
	// Load environment variables from the .env file
	if err := godotenv.Load(); err != nil {
		log.Fatal("Error loading .env file")
	}

	//cmdMonitor := &event.CommandMonitor{
	//	Started: func(_ context.Context, evt *event.CommandStartedEvent) {
	//		log.Print(evt.Command)
	//	},
	//}

	if config.DbHost == "" {
		log.Fatal("MONGO_URL environment variable not set")
	}
	if config.MainDbName == "" {
		log.Fatal("MONGO_MAIN_DATABASE environment variable not set")
	}
	if config.UserDbName == "" {
		log.Fatal("MONGO_USER_DATABASE environment variable not set")
	}
	if config.OmsDbName == "" {
		log.Fatal("MONGO_OMS_DATABASE environment variable not set")
	}

	clientOptions := options.Client().ApplyURI("mongodb://" + config.DbHost)
	//clientOptions.SetMonitor(cmdMonitor)
	client, err := mongo.Connect(nil, clientOptions)
	if err != nil {
		log.Fatal(err)
	}

	return map[string]*mongo.Database{
		"main": client.Database(config.MainDbName),
		"user": client.Database(config.UserDbName),
		"oms":  client.Database(config.OmsDbName),
	}
}
