package constant

type HttpMethodEnum string

const (
	HttpMethodGet    HttpMethodEnum = "GET"
	HttpMethodPost   HttpMethodEnum = "POST"
	HttpMethodPut    HttpMethodEnum = "PUT"
	HttpMethodPatch  HttpMethodEnum = "PATCH"
	HttpMethodDelete HttpMethodEnum = "DELETE"
)

type MarketplaceCodeEnum string

const (
	MarketplaceCodeShopee    MarketplaceCodeEnum = "SHOPEE"
	MarketplaceCodeTokopedia MarketplaceCodeEnum = "TOKOPEDIA"
	MarketplaceCodeBlibli    MarketplaceCodeEnum = "BLIBLI"
	MarketplaceCodeTiktok    MarketplaceCodeEnum = "TIKTOK"
	MarketplaceCodeLazada    MarketplaceCodeEnum = "LAZADA"
	MarketplaceCodeZalora    MarketplaceCodeEnum = "ZALORA"
)

type ShopeeOrderStatusType map[string]int

var ShopeeOrderStatusPriority = ShopeeOrderStatusType{
	"PENDING PAYMENT":    1,
	"OPEN":               2,
	"NOT SHIPPED":        3,
	"READY TO SHIP":      4,
	"SHIPPED":            5,
	"DELIVERED":          6,
	"TO_CONFIRM_RECEIVE": 7,
	"CANCELLED":          8,
	"COMPLETE":           9,
}

type OrderStatusEnum string

const (
	OrderStatusPendingPayment OrderStatusEnum = "PENDING PAYMENT"
	OrderStatusOpen           OrderStatusEnum = "OPEN"
	OrderStatusReadyToShip    OrderStatusEnum = "READY TO SHIP"
	OrderStatusShipped        OrderStatusEnum = "SHIPPED"
	OrderStatusDelivered      OrderStatusEnum = "DELIVERED"
	OrderStatusCompleted      OrderStatusEnum = "COMPLETED"
	OrderStatusCancel         OrderStatusEnum = "CANCEL"
)

type ShopeeOrderStatusEnum map[string]OrderStatusEnum

var ShopeeOrderStatus = ShopeeOrderStatusEnum{
	"UNPAID":             OrderStatusPendingPayment,
	"READY_TO_SHIP":      OrderStatusOpen,
	"PROCESSED":          OrderStatusOpen,
	"RETRY_SHIP":         OrderStatusReadyToShip,
	"SHIPPED":            OrderStatusShipped,
	"TO_CONFIRM_RECEIVE": OrderStatusDelivered,
	"IN_CANCEL":          OrderStatusCancel,
	"CANCELLED":          OrderStatusCancel,
	"TO_RETURN":          OrderStatusCancel,
	"COMPLETED":          OrderStatusCompleted,
}

type ShopeeTrackingStatusEnum string

const (
	ShopeeTrackingPickup    ShopeeTrackingStatusEnum = "PICKED_UP"
	ShopeeTrackingDelivered ShopeeTrackingStatusEnum = "DELIVERED"
)
