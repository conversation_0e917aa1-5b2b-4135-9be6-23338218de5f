package shopee

import (
	"sync"
	"time"

	"abs-scheduler-go/internal/common/interfaces"
	schemas4 "abs-scheduler-go/internal/orders/schemas"
	schemas5 "abs-scheduler-go/internal/products/schemas"
	schemas2 "abs-scheduler-go/internal/scheduler_tasks/schemas"
	"abs-scheduler-go/internal/stores/schemas"
	schemas3 "abs-scheduler-go/internal/task_executions/schemas"
	"abs-scheduler-go/pkg/queue"
	"abs-scheduler-go/pkg/util"
	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type FetchOrderListOpts struct {
	Schedule schemas2.SchedulerTask
	Store    schemas.Store
	Service  interfaces.TaskExecutionsRepository
}

type genDetailTaskExecutionOpts struct {
	ids      []string
	service  interfaces.TaskExecutionsRepository
	existing schemas3.TaskExecution
	key      string
}

type genTranslateExecutionOpts struct {
	data     []map[string]interface{}
	service  interfaces.TaskExecutionsRepository
	existing schemas3.TaskExecution
	escrows  map[string]map[string]interface{}
}

type genProductTranslateExecutionOpts struct {
	data     []map[string]interface{}
	service  interfaces.TaskExecutionsRepository
	existing schemas3.TaskExecution
}

type fetchOrderEscrowOpts struct {
	access       string
	refresh      string
	shopId       string
	orderSn      []string
	partnerKey   string
	serviceStore interfaces.StoresRepository
}

type TranslateItemOpts struct {
	Item           map[string]interface{}
	Escrow         map[string]interface{}
	OrderItems     *[]schemas4.OrderItems
	TotalWeight    *float64
	Pricing        *map[string]float32
	ProratedDisc   float64
	ProductService interfaces.ProductRepository
	DiscBreakdown  []schemas4.OrderDiscount
}

type MapEscrowItemsOpts struct {
	Escrow map[string]interface{}
	Result *map[string]interface{}
}

type getListOpts struct {
	data          schemas3.TaskExecution
	service       interfaces.TaskExecutionsRepository
	storeService  interfaces.StoresRepository
	failedService interfaces.DeadLetterQueuesRepository
	queueServ     *queue.Service
	redis         *redis.Client
	list          []map[string]interface{}
	result        *map[string][]string
	key           string
	mu            sync.Mutex
}

type genNextPageOrderListOpts struct {
	existing         schemas3.TaskExecution
	serv             interfaces.TaskExecutionsRepository
	operation        schemas2.SchedulerTaskOperationEnum
	additionalParams map[string]interface{}
}

type RefreshTokenOpts struct {
	Refresh    string
	PartnerKey string
	PartnerId  string
	ShopId     string
	BaseUrl    string
	Service    interfaces.StoresRepository
}

type TranslateOrderOpts struct {
	Service         interfaces.OrderRepository
	Data            schemas3.TaskExecution
	StoreService    interfaces.StoresRepository
	ProductService  interfaces.ProductRepository
	LogisticService interfaces.LogisticRepository
}

type translateProductOpts struct {
	data        schemas3.TaskExecution
	taskServ    interfaces.TaskExecutionsRepository
	productServ interfaces.ProductRepository
	storeServ   interfaces.StoresRepository
}

type TranslateOrderCbOpts struct {
	Service         interfaces.OrderRepository
	Data            schemas3.TaskExecution
	OrderId         string
	StoreService    interfaces.StoresRepository
	TaskExecService interfaces.TaskExecutionsRepository
	ProductService  interfaces.ProductRepository
	LogisticService interfaces.LogisticRepository
}

type proceedVariantOpts struct {
	product     map[string]interface{}
	data        map[string]interface{}
	header      map[string]interface{}
	productServ interfaces.ProductRepository
}

type proceedTranslateOpts struct {
	product     map[string]interface{}
	header      map[string]interface{}
	productServ interfaces.ProductRepository
}

type wrapWithRefreshOpts struct {
	SendOpts     *util.SendRequestOpts
	RefreshOpt   RefreshTokenOpts
	StoreService interfaces.StoresRepository
	hasRetry     bool
}

type checkRateLimitOpts struct {
	redis          *redis.Client
	data           schemas3.TaskExecution
	key            string
	siteConfigRepo interfaces.SiteConfigRepository
	qty            int
}

type decrRateLimitOpts struct {
	redis *redis.Client
	key   string
	qty   int
}

type parsePricingAndSetBreakdownOpts struct {
	order         map[string]interface{}
	orderIncome   map[string]interface{}
	result        *map[string]float32
	feeBreakdown  *[]schemas4.OrderFee
	discBreakdown *[]schemas4.OrderDiscount
}

type getDetailOpts struct {
	storeService interfaces.StoresRepository
	data         schemas3.TaskExecution
	service      interfaces.TaskExecutionsRepository
	list         *[]map[string]interface{}
	escrowMap    *map[string]map[string]interface{}
	accessToken  string
	refreshToken string
}

type getVariantOpts struct {
	storeService interfaces.StoresRepository
	data         *schemas3.TaskExecution
	accessToken  string
	refreshToken string
}

type getTrackingDataOpts struct {
	orderId      string
	result       *schemas3.TaskExecution
	accessToken  string
	refreshToken string
	storeService interfaces.StoresRepository
}

type getTokenOpts struct {
	storeService interfaces.StoresRepository
	Data         *schemas3.TaskExecution
}

type incrementQueueJobOpts struct {
	data        schemas3.TaskExecution
	redis       *redis.Client
	insertedIds map[string][]string
}

type addCallbackOpts struct {
	id             primitive.ObjectID
	shopId         string
	orderId        string
	queueServ      *queue.Service
	callbackRepo   interfaces.CallbackRepository
	orderRepo      interfaces.OrderRepository
	siteConfigRepo interfaces.SiteConfigRepository
}

type productResult struct {
	product             *schemas5.ProductSchema
	productVariant      *schemas5.ProductVariantSchema
	productVariantChild *schemas5.ProductVariantChildrenSchema
	productStock        *schemas5.ProductStockSchema
	err                 error
}

type cleanupResourceOpts struct {
	startTime      time.Time
	productServ    interfaces.ProductRepository
	product        primitive.ObjectID
	productChild   primitive.ObjectID
	productVariant primitive.ObjectID
	productStock   primitive.ObjectID
}

type fetchExistingProductOpts struct {
	wg              *sync.WaitGroup
	itemId          string
	productChildren *schemas5.ProductChildrenSchema
	productServ     interfaces.ProductRepository
	resultCh        *chan productResult
}

type proceedProductOpts struct {
	product     map[string]interface{}
	images      []schemas5.ProductImage
	exist       *schemas5.ProductSchema
	productServ interfaces.ProductRepository
}

type proceedProductChildrenOpts struct {
	productId   primitive.ObjectID
	exist       *schemas5.ProductChildrenSchema
	data        map[string]interface{}
	header      map[string]interface{}
	images      []schemas5.ProductImage
	productServ interfaces.ProductRepository
	startTime   time.Time
}

type proceedProductVariantOpts struct {
	itemId            string
	productId         primitive.ObjectID
	productChildrenId primitive.ObjectID
	exist             *schemas5.ProductVariantSchema
	data              map[string]interface{}
	images            []schemas5.ProductImage
	productServ       interfaces.ProductRepository
	startTime         time.Time
}

type proceedProductStockOpts struct {
	productServ       interfaces.ProductRepository
	sku               string
	data              map[string]interface{}
	exist             *schemas5.ProductStockSchema
	companyId         primitive.ObjectID
	productId         primitive.ObjectID
	productChildrenId primitive.ObjectID
	productVariantId  primitive.ObjectID
	startTime         time.Time
}

type proceedProductVariantChildrenOpts struct {
	itemId            string
	sku               string
	shopId            string
	productServ       interfaces.ProductRepository
	exist             *schemas5.ProductVariantChildrenSchema
	data              map[string]interface{}
	companyId         primitive.ObjectID
	productId         primitive.ObjectID
	productChildrenId primitive.ObjectID
	productVariantId  primitive.ObjectID
	productStockId    primitive.ObjectID
	startTime         time.Time
}

type ValidateRequiredFieldsOpts struct {
	ShopId       string
	StoreService interfaces.StoresRepository
	OrderId      string
}

type StockAdjustmentOpts struct {
	Data      schemas3.TaskExecution
	Service   interfaces.TaskExecutionsRepository
	StoreServ interfaces.StoresRepository
}

type SyncLogisticsOpts struct {
	ShopId       string
	StoreServ    interfaces.StoresRepository
	LogisticServ interfaces.LogisticRepository
}
