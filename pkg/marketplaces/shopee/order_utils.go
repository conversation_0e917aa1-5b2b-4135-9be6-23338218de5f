package shopee

import (
	"context"
	"errors"
	"fmt"
	"log"
	"maps"
	"strings"
	"sync"
	"time"

	schemas2 "abs-scheduler-go/internal/scheduler_tasks/schemas"
	schemas3 "abs-scheduler-go/internal/task_executions/schemas"
	"abs-scheduler-go/pkg/config"
	"abs-scheduler-go/pkg/constant"
	"abs-scheduler-go/pkg/util"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func extractOrderList(res map[string]interface{}) ([]map[string]interface{}, error) {
	response, ok := res["response"].(map[string]interface{})
	if !ok {
		return nil, errors.New("field 'response' not found or not a JSON object")
	}

	orderListRaw, ok := response["order_list"].([]interface{})
	if !ok {
		return nil, errors.New("field 'order_list' not found or not a JSON object")
	}

	var orderList []map[string]interface{}

	// Convert each item in orderListRaw to map[string]interface{}
	for _, item := range orderListRaw {
		orderMap, ok := item.(map[string]interface{})
		if !ok {
			return nil, errors.New("order_list contains a non-object item")
		}
		orderList = append(orderList, orderMap)
	}

	return orderList, nil
}

func checkHasMoreOrder(res map[string]interface{}) (bool, string, error) {
	response, ok := res["response"].(map[string]interface{})
	if !ok {
		return false, "", fmt.Errorf("field 'response' not found or not a JSON object")
	}

	more, ok := response["more"].(bool)
	if !ok || !more {
		return false, "", fmt.Errorf("field 'more' not found or not a JSON object")
	}

	nextCursor := fmt.Sprintf("%v", response["next_cursor"])
	return true, nextCursor, nil
}

func handleOrderPagination(opts getListOpts, cursor string) error {
	insertedId, err := genNextPageList(genNextPageOrderListOpts{
		existing:  opts.data,
		serv:      opts.service,
		operation: schemas2.SchedulerTaskOperationFetchOrderList,
		additionalParams: map[string]interface{}{
			"time_range_field": opts.data.RequestParam["time_range_field"],
			"time_from":        opts.data.RequestParam["time_from"],
			"time_to":          opts.data.RequestParam["time_to"],
			"cursor":           cursor,
		},
	})

	if err != nil {
		(*opts.result)["order_list"] = append((*opts.result)["order_list"], insertedId)

		deletedIds := []string{insertedId}
		if len((*opts.result)["order_detail"]) > 0 {
			deletedIds = append(deletedIds, (*opts.result)["order_detail"]...)
		}

		if _, err2 := opts.service.DeleteMany(deletedIds); err2 != nil {
			log.Printf("failed to delete tasks after pagination error: %v", err2)
		}

		return fmt.Errorf("failed to generate next page: %w", err)
	}

	if insertedId != "" {
		(*opts.result)["order_list"] = append((*opts.result)["order_list"], insertedId)
	}
	return nil
}

func genNextPageList(opts genNextPageOrderListOpts) (string, error) {
	requestParam := map[string]interface{}{
		"access_token":  opts.existing.RequestParam["access_token"],
		"refresh_token": opts.existing.RequestParam["refresh_token"],
		"partner_id":    opts.existing.RequestParam["partner_id"],
		"shop_id":       opts.existing.RequestParam["shop_id"],
		"page_size":     opts.existing.RequestParam["page_size"],
	}

	if opts.additionalParams != nil {
		requestParam = util.MergeMap(requestParam, opts.additionalParams)
	}

	find, err := opts.serv.GetMany(bson.M{
		"requestParam.shop_id":          requestParam["shop_id"],
		"requestParam.time_range_field": requestParam["time_range_field"],
		"requestParam.time_from":        requestParam["time_from"],
		"requestParam.time_to":          requestParam["time_to"],
		"requestParam.cursor":           requestParam["cursor"],
		"batch":                         opts.existing.Batch,
	})
	if err != nil {
		return "", err
	}
	if len(find) > 0 {
		return "", nil
	}

	insert, err := opts.serv.Insert(schemas3.TaskExecution{
		SchedulerTask:   opts.existing.SchedulerTask,
		Batch:           opts.existing.Batch,
		Status:          schemas3.TaskExecutionStatusWaiting,
		Trigger:         schemas3.TaskExecutionTriggerCron,
		Operation:       opts.operation,
		MarketplaceCode: opts.existing.MarketplaceCode,
		StartTime:       primitive.NewDateTimeFromTime(time.Now()),
		EndTime:         primitive.DateTime(0),
		RetryCount:      0,
		MaxRetries:      opts.existing.MaxRetries,
		Company:         opts.existing.Company,
		Store:           opts.existing.Store,
		RequestParam:    requestParam,
		RequestHeader: map[string]interface{}{
			"Content-Type": "application/json",
		},
		RequestBody:   nil,
		RequestUrl:    opts.existing.RequestUrl,
		RequestMethod: string(constant.HttpMethodGet),
	})
	if err != nil {
		return "", err
	}

	return insert, nil
}

func processOrderBatches(opts getListOpts) error {
	var batches [][]map[string]interface{}
	for i := 0; i < len(opts.list); i += 50 {
		end := i + 50
		if end > len(opts.list) {
			end = len(opts.list)
		}
		batches = append(batches, opts.list[i:end])
	}

	for _, batch := range batches {
		var ids []string
		for _, item := range batch {
			ids = append(ids, item["order_sn"].(string))
		}
		execution, err := genOrderDetailTaskExecution(genDetailTaskExecutionOpts{
			ids:      ids,
			service:  opts.service,
			existing: opts.data,
		})
		if err != nil {
			return err
		}

		(*opts.result)["order_detail"] = append((*opts.result)["order_detail"], execution)
	}

	return nil
}

func genOrderDetailTaskExecution(opts genDetailTaskExecutionOpts) (string, error) {
	path := "/api/v2/order/get_order_detail"

	insert, err := opts.service.Insert(schemas3.TaskExecution{
		SchedulerTask:   opts.existing.SchedulerTask,
		Batch:           opts.existing.Batch,
		Status:          schemas3.TaskExecutionStatusWaiting,
		Trigger:         schemas3.TaskExecutionTriggerCron,
		Operation:       schemas2.SchedulerTaskOperationFetchOrderDetail,
		MarketplaceCode: opts.existing.MarketplaceCode,
		StartTime:       primitive.NewDateTimeFromTime(time.Now()),
		EndTime:         primitive.DateTime(0),
		RetryCount:      0,
		MaxRetries:      opts.existing.MaxRetries,
		Company:         opts.existing.Company,
		Store:           opts.existing.Store,
		RequestParam: map[string]interface{}{
			"access_token":             opts.existing.RequestParam["access_token"],
			"refresh_token":            opts.existing.RequestParam["refresh_token"],
			"partner_id":               config.ShopeePartnerId,
			"shop_id":                  opts.existing.RequestParam["shop_id"],
			"order_sn_list":            strings.Join(opts.ids, ","),
			"response_optional_fields": "buyer_user_id,buyer_username,estimated_shipping_fee,recipient_address,actual_shipping_fee ,goods_to_declare,note,note_update_time,item_list,pay_time,dropshipper, dropshipper_phone,split_up,buyer_cancel_reason,cancel_by,cancel_reason,actual_shipping_fee_confirmed,buyer_cpf_id,fulfillment_flag,pickup_done_time,package_list,shipping_carrier,payment_method,total_amount,buyer_username,invoice_data,no_plastic_packing,order_chargeable_weight_gram,return_request_due_date,edt",
		},
		RequestHeader: map[string]interface{}{
			"Content-Type": "application/json",
		},
		RequestBody:   nil,
		RequestUrl:    config.ShopeeBaseUrl + path,
		RequestMethod: string(constant.HttpMethodGet),
	})
	if err != nil {
		return "", err
	}

	return insert, nil
}

func genOrderTranslateTaskExecution(opts genTranslateExecutionOpts) ([]string, error) {
	var mock []schemas3.TaskExecution
	for _, data := range opts.data {
		sn, ok := data["order_sn"].(string)
		if !ok {
			return []string{}, fmt.Errorf("order_sn is not a string")
		}

		data["escrow"] = opts.escrows[sn]

		mock = append(mock, schemas3.TaskExecution{
			SchedulerTask:   opts.existing.SchedulerTask,
			Batch:           opts.existing.Batch,
			Status:          schemas3.TaskExecutionStatusWaiting,
			Trigger:         schemas3.TaskExecutionTriggerCron,
			Operation:       schemas2.SchedulerTaskOperationTranslateOrder,
			MarketplaceCode: opts.existing.MarketplaceCode,
			StartTime:       primitive.NewDateTimeFromTime(time.Now()),
			EndTime:         primitive.DateTime(0),
			RetryCount:      0,
			MaxRetries:      opts.existing.MaxRetries,
			Company:         opts.existing.Company,
			Store:           opts.existing.Store,
			RequestBody:     data,
			RequestParam: map[string]interface{}{
				"shop_id":    opts.existing.RequestParam["shop_id"],
				"partner_id": opts.existing.RequestParam["partner_id"],
			},
		})
	}

	insert, err := opts.service.InsertMany(mock)
	if err != nil {
		return []string{}, err
	}

	return insert, nil
}

func fetchOrderEscrow(opts fetchOrderEscrowOpts) ([]interface{}, error) {
	path := "/api/v2/payment/get_escrow_detail_batch"

	timestamp := generateTimestamp()
	sign, err := generateSign(opts.partnerKey, config.ShopeePartnerId+path+timestamp+opts.access+opts.shopId)

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	res, err := wrapWithRefresh(ctx, wrapWithRefreshOpts{
		SendOpts: &util.SendRequestOpts{
			Url:    config.ShopeeBaseUrl + path,
			Method: string(constant.HttpMethodPost),
			Params: map[string]interface{}{
				"partner_id":   config.ShopeePartnerId,
				"timestamp":    timestamp,
				"access_token": opts.access,
				"shop_id":      opts.shopId,
				"sign":         sign,
			},
			Body: map[string]interface{}{
				"order_sn_list": opts.orderSn,
			},
		},
		RefreshOpt: RefreshTokenOpts{
			Refresh:    opts.refresh,
			PartnerKey: opts.partnerKey,
			PartnerId:  config.ShopeePartnerId,
			ShopId:     opts.shopId,
			BaseUrl:    config.ShopeeBaseUrl,
			Service:    opts.serviceStore,
		},
		StoreService: nil,
	})

	if err != nil {
		if errors.Is(ctx.Err(), context.DeadlineExceeded) {
			return nil, fmt.Errorf("API request timed out: %w", err)
		}
		return nil, err
	}

	return res["response"].([]interface{}), nil
}

func getOrderDetail(opts getDetailOpts) error {
	path := strings.Replace(opts.data.RequestUrl, config.ShopeeBaseUrl, "", -1)
	timestamp := generateTimestamp()
	sign, err := generateSign(config.ShopeePartnerKey, config.ShopeePartnerId+path+timestamp+opts.accessToken+opts.data.RequestParam["shop_id"].(string))

	if err != nil {
		return err
	}

	opts.data.RequestParam["sign"] = sign
	opts.data.RequestParam["timestamp"] = timestamp
	opts.data.RequestParam["access_token"] = opts.accessToken
	opts.data.RequestParam["refresh_token"] = opts.refreshToken

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	res, err := wrapWithRefresh(ctx, wrapWithRefreshOpts{
		SendOpts: &util.SendRequestOpts{
			Url:     opts.data.RequestUrl,
			Method:  opts.data.RequestMethod,
			Params:  opts.data.RequestParam,
			Body:    opts.data.RequestBody,
			Headers: opts.data.RequestHeader,
		},
		RefreshOpt: RefreshTokenOpts{
			Refresh:    opts.refreshToken,
			PartnerKey: config.ShopeePartnerKey,
			PartnerId:  config.ShopeePartnerId,
			ShopId:     opts.data.RequestParam["shop_id"].(string),
			BaseUrl:    config.ShopeeBaseUrl,
			Service:    opts.storeService,
		},
		StoreService: nil,
	})

	if err != nil {
		opts.data.RetryCount++
		_, err1 := opts.service.SetError(opts.data.ID, err.Error(), opts.data.RetryCount, opts.data.MaxRetries)
		if err1 != nil {
			log.Printf("\nerror in setError %v", err1)
			return err1
		}

		log.Printf("err: %v", err)
		if errors.Is(ctx.Err(), context.DeadlineExceeded) {
			return fmt.Errorf("API request timed out: %w", err)
		}
		return err
	}

	response, ok := res["response"].(map[string]interface{})
	if !ok {
		log.Println("Field 'response' not found or not a JSON object")
		return errors.New("field 'response' not found or not a JSON object")
	}

	orderListRaw, ok := response["order_list"].([]interface{})
	if !ok {
		log.Println("Field 'order_list' not found or not a JSON object")
		return errors.New("field 'order_list' not found or not a JSON object")
	}

	// Convert each item in orderListRaw to map[string]interface{}
	for _, item := range orderListRaw {
		orderMap, ok := item.(map[string]interface{})
		if !ok {
			log.Println("Error: order_list contains a non-object item")
			return errors.New("order_list contains a non-object item")
		}
		*opts.list = append(*opts.list, orderMap)
	}

	var wg sync.WaitGroup
	wg.Add(len(*opts.list))

	var orderSn []string

	for _, order := range *opts.list {
		orderSn = append(orderSn, order["order_sn"].(string))
	}

	escrow, err := fetchOrderEscrow(fetchOrderEscrowOpts{
		orderSn:      orderSn,
		access:       opts.accessToken,
		refresh:      opts.refreshToken,
		shopId:       opts.data.RequestParam["shop_id"].(string),
		partnerKey:   config.ShopeePartnerKey,
		serviceStore: opts.storeService,
	})
	if err != nil {
		return err
	}

	for _, item := range escrow {
		parse, ok := item.(map[string]interface{})
		if !ok {
			return fmt.Errorf("field 'escrow_detail' not found or not a JSON object")
		}
		detail, ok := parse["escrow_detail"].(map[string]interface{})
		if !ok {

		}

		sn, ok := detail["order_sn"].(string)
		if !ok {
			return err
		}

		(*opts.escrowMap)[sn] = detail
	}

	return nil
}

func getTrackingData(opts getTrackingDataOpts) (map[string]interface{}, error) {
	timestamp := generateTimestamp()

	params1 := make(map[string]interface{})
	params2 := make(map[string]interface{})

	for k, v := range opts.result.RequestParam {
		params1[k] = v
		params2[k] = v
	}

	params1["timestamp"] = timestamp
	params1["access_token"] = opts.accessToken
	params1["refresh_token"] = opts.refreshToken
	params1["partner_id"] = config.ShopeePartnerId
	params1["order_sn"] = opts.orderId

	params2["timestamp"] = timestamp
	params2["access_token"] = opts.accessToken
	params2["refresh_token"] = opts.refreshToken
	params2["partner_id"] = config.ShopeePartnerId
	params2["order_sn"] = opts.orderId

	var mu sync.Mutex
	var wg sync.WaitGroup
	errChan := make(chan error, 2)

	wg.Add(2)

	result := make(map[string]interface{})

	go func() {
		defer wg.Done()
		path := "/api/v2/logistics/get_tracking_number"
		sign, err := generateSign(config.ShopeePartnerKey, config.ShopeePartnerId+path+timestamp+opts.accessToken+params1["shop_id"].(string))

		if err != nil {
			errChan <- err
			return
		}

		params1["sign"] = sign

		processTime := time.Now()

		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		res, err := wrapWithRefresh(ctx, wrapWithRefreshOpts{
			SendOpts: &util.SendRequestOpts{
				Url:     config.ShopeeBaseUrl + path,
				Method:  string(constant.HttpMethodGet),
				Params:  params1,
				Body:    opts.result.RequestBody,
				Headers: opts.result.RequestHeader,
			},
			RefreshOpt: RefreshTokenOpts{
				Refresh:    opts.refreshToken,
				PartnerKey: config.ShopeePartnerKey,
				PartnerId:  config.ShopeePartnerId,
				ShopId:     params1["shop_id"].(string),
				BaseUrl:    config.ShopeeBaseUrl,
				Service:    opts.storeService,
			},
			StoreService: opts.storeService,
		})
		log.Printf("complete get tracking number %s in %v\n", opts.orderId, time.Since(processTime))

		if err != nil {
			if errors.Is(ctx.Err(), context.DeadlineExceeded) {
				errChan <- fmt.Errorf("API request timed out: %w", err)
				return
			}
			errChan <- err
			return
		}

		response, ok := res["response"].(map[string]interface{})
		if !ok {
			errChan <- errors.New("field 'response' not found or not a JSON object")
			return
		}

		mu.Lock()
		maps.Copy(result, response)
		mu.Unlock()
	}()

	go func() {
		defer wg.Done()
		path := "/api/v2/logistics/get_tracking_info"
		sign, err := generateSign(config.ShopeePartnerKey, config.ShopeePartnerId+path+timestamp+opts.accessToken+params2["shop_id"].(string))

		if err != nil {
			errChan <- err
			return
		}

		params2["sign"] = sign

		processTime := time.Now()

		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		res, err := wrapWithRefresh(ctx, wrapWithRefreshOpts{
			SendOpts: &util.SendRequestOpts{
				Url:     config.ShopeeBaseUrl + path,
				Method:  string(constant.HttpMethodGet),
				Params:  params2,
				Body:    opts.result.RequestBody,
				Headers: opts.result.RequestHeader,
			},
			RefreshOpt: RefreshTokenOpts{
				Refresh:    opts.refreshToken,
				PartnerKey: config.ShopeePartnerKey,
				PartnerId:  config.ShopeePartnerId,
				ShopId:     params2["shop_id"].(string),
				BaseUrl:    config.ShopeeBaseUrl,
				Service:    opts.storeService,
			},
			StoreService: opts.storeService,
		})
		log.Printf("complete get tracking info %s in %v\n", opts.orderId, time.Since(processTime))

		if err != nil {
			if errors.Is(ctx.Err(), context.DeadlineExceeded) {
				errChan <- fmt.Errorf("API request timed out: %w", err)
				return
			}
			errChan <- err
			return
		}

		response, ok := res["response"].(map[string]interface{})
		if !ok {
			errChan <- errors.New("field 'response' not found or not a JSON object")
			return
		}

		mu.Lock()
		result["tracking_info"] = response["tracking_info"]
		mu.Unlock()
	}()

	wg.Wait()
	close(errChan)

	select {
	case err := <-errChan:
		return nil, err
	default:
		return result, nil
	}
}
