package shopee

import (
	"context"
	"errors"
	"fmt"
	"log"
	"strconv"
	"strings"
	"time"

	schemas3 "abs-scheduler-go/internal/callbacks/schemas"
	"abs-scheduler-go/internal/common/interfaces"
	schemas4 "abs-scheduler-go/internal/orders/schemas"
	schemas2 "abs-scheduler-go/internal/scheduler_tasks/schemas"
	"abs-scheduler-go/internal/task_executions/schemas"
	"abs-scheduler-go/pkg/constant"
	"abs-scheduler-go/pkg/queue"
	"abs-scheduler-go/pkg/util"
	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/mongo"
)

type Executor struct {
	Db                map[string]*mongo.Database
	Redis             *redis.Client
	TaskExecutionRepo interfaces.TaskExecutionsRepository
	StoreRepo         interfaces.StoresRepository
	SiteConfigRepo    interfaces.SiteConfigRepository
	ServiceFailed     interfaces.DeadLetterQueuesRepository
	QueueService      *queue.Service
	OrderRepo         interfaces.OrderRepository
	ProductRepo       interfaces.ProductRepository
	CallbackRepo      interfaces.CallbackRepository
	LogisticRepo      interfaces.LogisticRepository
}

func NewShopeeExecutor(opts Executor) *Executor {
	return &opts
}

func (s Executor) Execute(data schemas.TaskExecution) (string, error) {
	handlers := map[schemas2.SchedulerTaskOperationEnum]func() (string, error){
		schemas2.SchedulerTaskOperationFetchOrderList: func() (string, error) {
			return s.handleRateLimit(data, 2, func() error {
				err := GetOrderList(getListOpts{
					data:          data,
					service:       s.TaskExecutionRepo,
					storeService:  s.StoreRepo,
					failedService: s.ServiceFailed,
					queueServ:     s.QueueService,
					redis:         s.Redis,
				})
				if err != nil {
					return err
				}
				return nil
			})
		},
		schemas2.SchedulerTaskOperationFetchOrderDetail: func() (string, error) {
			return s.handleRateLimit(data, 2, func() error {
				err := GetOrderDetail(getListOpts{
					data:          data,
					storeService:  s.StoreRepo,
					failedService: s.ServiceFailed,
					queueServ:     s.QueueService,
					redis:         s.Redis,
					service:       s.TaskExecutionRepo,
				})

				if err != nil {
					return err
				}
				return nil
			})
		},
		schemas2.SchedulerTaskOperationTranslateOrder: func() (string, error) {
			cbData := false
			if isCb, ok := data.RequestBody["callbackData"]; ok && isCb != nil {
				cbData = isCb.(bool)
			}

			shopId := ""
			orderSn := ""
			if cbData {
				body, ok := data.RequestBody["data"].(map[string]interface{})
				if !ok {
					log.Printf("field 'data' is nil or missing")
					return "", errors.New("field 'data' is nil or missing")
				}

				orderSn = body["ordersn"].(string)
				shopId = strconv.Itoa(int(data.RequestBody["shop_id"].(float64)))
			} else {
				orderSn = data.RequestBody["order_sn"].(string)
				shopId = data.RequestParam["shop_id"].(string)
			}

			log.Printf("translating order %s", orderSn)

			lockKey := "order-" + orderSn
			ctx := context.Background()

			running, err := s.Redis.Get(ctx, lockKey).Result()
			if err != nil {
				if !errors.Is(err, redis.Nil) {
					log.Printf("err: %v", err)
					return "", err
				}
			}

			if running != "" {
				return "order data is already running", nil
			}

			err = s.Redis.SetNX(ctx, lockKey, orderSn, 5*time.Second).Err()
			if err != nil {
				log.Printf("err lock order: %v", err)
				return "", err
			}

			ctxExtend, cancel := context.WithCancel(context.Background())
			go queue.ExtendLock(queue.ExtendLockOpts{
				Key:             lockKey,
				Client:          s.Redis,
				Ctx:             ctxExtend,
				Value:           orderSn,
				ExtendTime:      5 * time.Second,
				ExtendCheckTime: 2 * time.Second,
			})

			defer func() {
				cancel()
				if delErr := s.Redis.Del(ctx, lockKey).Err(); delErr != nil {
					log.Printf("failed to delete lock %s: %v", lockKey, delErr)
				}
			}()

			var res *schemas4.Order
			if cbData {
				return s.handleRateLimit(data, 2, func() error {
					res, err = TranslateOrderCb(TranslateOrderCbOpts{
						Service:         s.OrderRepo,
						Data:            data,
						OrderId:         orderSn,
						StoreService:    s.StoreRepo,
						TaskExecService: s.TaskExecutionRepo,
						ProductService:  s.ProductRepo,
						LogisticService: s.LogisticRepo,
					})
					log.Printf("translated order id %s", orderSn)

					if err != nil {
						return err
					}

					if res != nil && shouldAddCallback(res) {
						addCallback(addCallbackOpts{
							id:             data.Company,
							shopId:         shopId,
							orderId:        orderSn,
							queueServ:      s.QueueService,
							callbackRepo:   s.CallbackRepo,
							orderRepo:      s.OrderRepo,
							siteConfigRepo: s.SiteConfigRepo,
						})
					}
					return nil
				})
			} else {
				res, err = TranslateOrder(TranslateOrderOpts{
					Data:            data,
					Service:         s.OrderRepo,
					StoreService:    s.StoreRepo,
					ProductService:  s.ProductRepo,
					LogisticService: s.LogisticRepo,
				})

				if shouldAddCallback(res) {
					defer addCallback(addCallbackOpts{
						id:             data.Company,
						shopId:         shopId,
						orderId:        orderSn,
						queueServ:      s.QueueService,
						callbackRepo:   s.CallbackRepo,
						orderRepo:      s.OrderRepo,
						siteConfigRepo: s.SiteConfigRepo,
					})
				}

				if err != nil {
					log.Printf("err while translate data %s: %v", data.ID, err)
					return "", err
				}
			}

			log.Printf("complete translating order %s", orderSn)
			return "", nil
		},
		schemas2.SchedulerTaskOperationFetchProductList: func() (string, error) {
			return s.handleRateLimit(data, 1, func() error {
				err := GetProductList(getListOpts{
					data:          data,
					service:       s.TaskExecutionRepo,
					storeService:  s.StoreRepo,
					failedService: s.ServiceFailed,
					queueServ:     s.QueueService,
					redis:         s.Redis,
				})
				if err != nil {
					return err
				}
				return nil
			})
		},
		schemas2.SchedulerTaskOperationFetchProductDetail: func() (string, error) {
			return s.handleRateLimit(data, 1, func() error {
				err := GetProductDetail(getListOpts{
					data:          data,
					service:       s.TaskExecutionRepo,
					storeService:  s.StoreRepo,
					failedService: s.ServiceFailed,
					queueServ:     s.QueueService,
					redis:         s.Redis,
				})
				if err != nil {
					return err
				}
				return nil
			})
		},
		schemas2.SchedulerTaskOperationTranslateProduct: func() (string, error) {
			cbData := false
			if isCb, ok := data.RequestBody["callbackData"]; ok && isCb != nil {
				cbData = isCb.(bool)
			}

			itemId := ""
			if cbData {
				body, ok := data.RequestBody["data"].(map[string]interface{})
				if !ok {
					log.Printf("field 'data' is nil or missing")
					return "", errors.New("field 'data' is nil or missing")
				}

				itemId = strconv.Itoa(int(body["item_id"].(float64)))
			} else {
				itemId = strconv.Itoa(int(data.RequestBody["item_id"].(float64)))
			}

			store, err := s.StoreRepo.GetByMpId(data.RequestParam["shop_id"].(string), constant.MarketplaceCodeShopee)
			if err != nil {
				return "", errors.New("failed when get store detail")
			}
			data.RequestBody["company"] = store.Company

			log.Printf("translating product %s", itemId)

			lockKey := "product-" + itemId
			ctx := context.Background()

			running, err := s.Redis.Get(ctx, lockKey).Result()
			if err != nil {
				if !errors.Is(err, redis.Nil) {
					log.Printf("err: %v", err)
					return "", err
				}
			}

			if running != "" {
				return "product data is already running", nil
			}

			err = s.Redis.SetNX(ctx, lockKey, itemId, 5*time.Second).Err()
			if err != nil {
				log.Printf("err lock product: %v", err)
				return "", err
			}

			ctxExtend, cancel := context.WithCancel(context.Background())
			queueLockMock := queue.ExtendLockOpts{
				Key:             lockKey,
				Client:          s.Redis,
				Ctx:             ctxExtend,
				Value:           itemId,
				ExtendTime:      5 * time.Second,
				ExtendCheckTime: 2 * time.Second,
			}
			go queue.ExtendLock(queueLockMock)

			defer cancel()

			if cbData {
				return s.handleRateLimit(data, 2, func() error {
					err = translateProduct(translateProductOpts{
						data:        data,
						taskServ:    s.TaskExecutionRepo,
						productServ: s.ProductRepo,
						storeServ:   s.StoreRepo,
					})
					log.Printf("translated product id %s", itemId)

					if err != nil {
						return err
					}
					return nil
				})
			} else {
				err = translateProduct(translateProductOpts{
					data:        data,
					taskServ:    s.TaskExecutionRepo,
					productServ: s.ProductRepo,
					storeServ:   s.StoreRepo,
				})
				if err != nil {
					log.Printf("err while translate data %s: %v", data.ID, err)
					errRedisDel := s.Redis.Del(ctx, lockKey).Err()
					if errRedisDel != nil {
						return "", errRedisDel
					}
					return "", err
				}
			}

			log.Printf("complete translating product %s", itemId)

			err = s.Redis.Del(ctx, lockKey).Err()
			if err != nil {
				return "", err
			}

			return "", nil
		},
	}

	if handler, exist := handlers[data.Operation]; exist {
		res, err := handler()
		if err != nil {
			return "", err
		}

		_, err = s.TaskExecutionRepo.UpdateStatus(data.ID, schemas.TaskExecutionStatusSuccess)
		if err != nil {
			return "", err
		}

		return res, nil
	}

	log.Printf("no handler found for operation %s", data.Operation)

	return "", nil
}

func (s Executor) handleRateLimit(data schemas.TaskExecution, qty int, callback func() error) (string, error) {
	limitKey := fmt.Sprintf("shopee_%s_%s", strings.ToLower(string(data.Operation)), data.Store.Hex())
	skip, err := checkRateLimit(checkRateLimitOpts{
		redis:          s.Redis,
		data:           data,
		key:            limitKey,
		siteConfigRepo: s.SiteConfigRepo,
	})
	if skip != "" {
		return skip, err
	}

	defer func() {
		err = decrRateLimit(decrRateLimitOpts{
			redis: s.Redis,
			key:   limitKey,
			qty:   qty,
		})
		if err != nil {
			log.Printf("error decrement rate limit %s: %v", data.ID, err)
		}
	}()

	err = callback()
	if err != nil {
		return "", err
	}
	return "", nil
}

func checkRateLimit(opts checkRateLimitOpts) (string, error) {
	ctx := context.Background()
	currRateLimitStr, err := opts.redis.Get(ctx, opts.key).Result()
	if err != nil {
		if !errors.Is(err, redis.Nil) {
			return "", err
		}
	}
	if currRateLimitStr == "" {
		currRateLimitStr = "0"
	}

	rateLimit, err := opts.siteConfigRepo.GetByKey(interfaces.GetByKeyOpts{Key: opts.key})
	if err != nil {
		return "", err
	}

	var limit int

	if rateLimit.Value != "" {
		limit, err = strconv.Atoi(rateLimit.Value)
		log.Printf("err: %v", err)
		if err != nil {
			return "", err
		}
	}

	currRateLimit, err := strconv.Atoi(currRateLimitStr)

	if limit > 0 && currRateLimit > limit {
		return "rate limit reached", nil
	}

	script := redis.NewScript(`
		local current = redis.call("INCR", KEYS[1])
		if current == 1 then
			redis.call("EXPIRE", KEYS[1], ARGV[1])
		end
		return current
	`)

	err = script.Run(ctx, opts.redis, []string{opts.key}, opts.qty).Err()

	return "", nil
}

func decrRateLimit(opts decrRateLimitOpts) error {
	script := redis.NewScript(`
		local exist = redis.call("EXISTS", KEYS[1])
		if exist == 1 then
			redis.call("INCR", KEYS[1])
		end
		return exist
	`)

	err := script.Run(context.Background(), opts.redis, []string{opts.key}, opts.qty*-1).Err()

	return err
}

func addCallback(opts addCallbackOpts) {
	order, err := opts.orderRepo.FindByOrderId(opts.orderId, constant.MarketplaceCodeShopee)
	if err != nil {
		log.Printf("err while finding order by id %s: %v", opts.orderId, err)
	}

	callbacks, err := opts.callbackRepo.GetUrlByCompany(opts.id)
	if err != nil {
		log.Printf("get callback err: %v", err)
	}

	for _, callback := range *callbacks {
		parse, err := util.StructToMap(order)
		if err != nil {
			log.Printf("parse err: %v", err)
		}

		insertCallback, err := opts.callbackRepo.InsertCallback(&schemas3.Callback{
			Company:         opts.id,
			Store:           opts.shopId,
			MarketplaceCode: constant.MarketplaceCodeShopee,
			CallbackUrl:     callback.ID,
			Data:            parse,
			Response:        nil,
			Status:          schemas3.CallbackStatusWaiting,
		})
		if err != nil {
			log.Printf("insert callback err: %v", err)
		}

		siteConfig, err := opts.siteConfigRepo.GetByKey(interfaces.GetByKeyOpts{Key: "callback.max-retry"})
		if err != nil {
			log.Printf("get callback max-retry err: %v", err)
			return
		}

		if siteConfig.Value == "" {
			siteConfig.Value = "3"
		}

		maxRetry, err := strconv.Atoi(siteConfig.Value)
		if err != nil {
			log.Printf("err while casting siteConfig: %v", err)
			return
		}

		err = opts.queueServ.AddJob(queue.CallbackStoreKey, queue.Job{
			Key: queue.CallbackStoreKey,
			Value: queue.JobValue{
				Event:     queue.EventWaiting,
				Id:        insertCallback,
				PrevEvent: "",
				Name:      "send-callback",
				Platform:  constant.MarketplaceCodeShopee,
				Retries:   0,
				MaxRetry:  maxRetry,
				Error:     "",
			},
			Priority: 2,
		})
		if err != nil {
			log.Printf("add callback err: %v", err)
		}
	}
}

func shouldAddCallback(res *schemas4.Order) bool {
	if res == nil {
		return false
	}

	if res.IncompleteData == nil {
		return true
	}

	return len(res.IncompleteData) == 0
}
