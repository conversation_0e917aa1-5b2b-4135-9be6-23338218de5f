package shopee

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	schemas2 "abs-scheduler-go/internal/logistics/schemas"
	"abs-scheduler-go/internal/task_executions/schemas"
	"abs-scheduler-go/pkg/constant"
	"abs-scheduler-go/pkg/util"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func ValidateRequiredFields(opts ValidateRequiredFieldsOpts) ([]string, error) {
	baseUrl, partnerId, partner<PERSON>ey, _, err := getBaseURLAndPartnerId()
	if err != nil {
		return nil, fmt.Errorf("failed to get base URL and partner ID: %w", err)
	}

	parsed, err := primitive.ObjectIDFromHex(opts.ShopId)
	if err != nil {
		return nil, fmt.Errorf("failed to parse shop ID: %w", err)
	}

	store, err := opts.StoreService.GetById(parsed)
	if err != nil {
		return nil, fmt.<PERSON><PERSON><PERSON>("failed to get store: %w", err)
	}

	mock := schemas.TaskExecution{
		RequestParam: map[string]interface{}{
			"shop_id":    store.MarketplaceId,
			"partner_id": partnerId,
			"order_sn":   opts.OrderId,
		},
		RequestHeader: map[string]interface{}{
			"Content-Type": "application/json",
		},
		RequestBody:   map[string]interface{}{},
		RequestUrl:    baseUrl,
		RequestMethod: string(constant.HttpMethodPost),
	}

	tokens, err := getToken(getTokenOpts{
		storeService: opts.StoreService,
		Data:         &mock,
	})

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	path := "/api/v2/logistics/get_shipping_parameter"
	timestamp := generateTimestamp()
	sign, err := generateSign(partnerKey, partnerId+path+timestamp+tokens["access"]+store.MarketplaceId)
	if err != nil {
		return nil, fmt.Errorf("failed to generate sign: %w", err)
	}

	mock.RequestParam["sign"] = sign
	mock.RequestParam["timestamp"] = timestamp
	mock.RequestParam["access_token"] = tokens["access"]
	mock.RequestParam["refresh_token"] = tokens["refresh"]

	res, err := wrapWithRefresh(ctx, wrapWithRefreshOpts{
		SendOpts: &util.SendRequestOpts{
			Url:     baseUrl + path,
			Method:  string(constant.HttpMethodGet),
			Params:  mock.RequestParam,
			Body:    mock.RequestBody,
			Headers: mock.RequestHeader,
		},
		RefreshOpt: RefreshTokenOpts{
			Refresh:    tokens["refresh"],
			PartnerKey: partnerKey,
			PartnerId:  partnerId,
			ShopId:     mock.RequestParam["shop_id"].(string),
			BaseUrl:    baseUrl,
			Service:    opts.StoreService,
		},
		StoreService: nil,
	})

	if err != nil {
		if errors.Is(ctx.Err(), context.DeadlineExceeded) {
			return nil, fmt.Errorf("API request timed out: %w", err)
		}
		return nil, fmt.Errorf("failed to get required fields: %w", err)
	}

	if len(res) == 0 {
		return nil, fmt.Errorf("failed to get required fields: %w", err)
	}

	response, ok := res["response"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("field 'response' not found or not a JSON object")
	}

	infoNeeded, ok := response["info_needed"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("field 'info_needed' not found or not a JSON object")
	}

	pickup := make([]string, 0)

	for _, field := range infoNeeded["pickup"].([]interface{}) {
		parsed, ok := field.(string)
		if !ok {
			return nil, fmt.Errorf("error parsing pickup to string, type: %T", field)
		}

		if parsed == "address_id" {
			pickup = append(pickup, "pickup_address_id")
		}
		if parsed == "pickup_time_id" {
			pickup = append(pickup, "pickup_timeslot_id")
		}
	}

	return pickup, nil
}

func SyncLogistic(opts SyncLogisticsOpts) (map[string]interface{}, error) {
	baseUrl, partnerId, partnerKey, _, err := getBaseURLAndPartnerId()
	if err != nil {
		return nil, fmt.Errorf("failed to get base URL and partner ID: %w", err)
	}

	store, err := opts.StoreServ.GetByMpId(opts.ShopId, constant.MarketplaceCodeShopee)
	if err != nil {
		return nil, fmt.Errorf("failed to get store: %w", err)
	}

	mock := schemas.TaskExecution{
		RequestParam: map[string]interface{}{
			"shop_id":    store.MarketplaceId,
			"partner_id": partnerId,
		},
		RequestHeader: map[string]interface{}{
			"Content-Type": "application/json",
		},
		RequestBody:   map[string]interface{}{},
		RequestUrl:    baseUrl,
		RequestMethod: string(constant.HttpMethodPost),
	}

	tokens, err := getToken(getTokenOpts{
		storeService: opts.StoreServ,
		Data:         &mock,
	})

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	path := "/api/v2/logistics/get_channel_list"
	timestamp := generateTimestamp()
	sign, err := generateSign(partnerKey, partnerId+path+timestamp+tokens["access"]+store.MarketplaceId)
	if err != nil {
		return nil, fmt.Errorf("failed to generate sign: %w", err)
	}

	mock.RequestParam["sign"] = sign
	mock.RequestParam["timestamp"] = timestamp
	mock.RequestParam["access_token"] = tokens["access"]
	mock.RequestParam["refresh_token"] = tokens["refresh"]

	res, err := wrapWithRefresh(ctx, wrapWithRefreshOpts{
		SendOpts: &util.SendRequestOpts{
			Url:     baseUrl + path,
			Method:  string(constant.HttpMethodGet),
			Params:  mock.RequestParam,
			Body:    mock.RequestBody,
			Headers: mock.RequestHeader,
		},
		RefreshOpt: RefreshTokenOpts{
			Refresh:    tokens["refresh"],
			PartnerKey: partnerKey,
			PartnerId:  partnerId,
			ShopId:     mock.RequestParam["shop_id"].(string),
			BaseUrl:    baseUrl,
			Service:    opts.StoreServ,
		},
		StoreService: nil,
	})

	if err != nil {
		if errors.Is(ctx.Err(), context.DeadlineExceeded) {
			return nil, fmt.Errorf("API request timed out: %w", err)
		}
		return nil, fmt.Errorf("failed to get required fields: %w", err)
	}

	if len(res) == 0 {
		return nil, fmt.Errorf("failed to get required fields: %w", err)
	}

	response, ok := res["response"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("field 'response' not found or not a JSON object")
	}

	channels, ok := response["logistics_channel_list"].([]interface{})
	channelMap := make(map[float64]map[string]interface{})
	for _, channel := range channels {
		channelMap[channel.(map[string]interface{})["logistics_channel_id"].(float64)] = channel.(map[string]interface{})
	}

	for _, channel := range channels {
		parsed, ok := channel.(map[string]interface{})
		if !ok {
			return nil, fmt.Errorf("error parsing channel to string, type: %T", channel)
		}

		id := parsed["logistics_channel_id"].(float64)

		exist, err := opts.LogisticServ.GetByMpId(strconv.Itoa(int(id)), constant.MarketplaceCodeShopee)
		if err != nil {
			return nil, fmt.Errorf("failed to get logistic: %w", err)
		}

		maskChannelId := parsed["mask_channel_id"].(float64)
		serviceName := ""
		if maskChannelId > 0 {
			serviceName = channelMap[maskChannelId]["logistics_channel_name"].(string)
		}

		weightLimit, ok := parsed["weight_limit"].(map[string]interface{})
		if !ok {
			return nil, fmt.Errorf("field 'weight_limit' not found or not a JSON object")
		}
		dimensionLimit, ok := parsed["item_max_dimension"].(map[string]interface{})
		if !ok {
			return nil, fmt.Errorf("field 'item_max_dimension' not found or not a JSON object")
		}
		volumeLimit, ok := parsed["volume_limit"].(map[string]interface{})
		if !ok {
			return nil, fmt.Errorf("field 'volume_limit' not found or not a JSON object")
		}

		//util.PrintJson(parsed)

		mock := schemas2.Logistic{
			MpId:            strconv.Itoa(int(id)),
			Name:            parsed["logistics_channel_name"].(string),
			ServiceName:     serviceName,
			CodEnabled:      parsed["cod_enabled"].(bool),
			Enabled:         parsed["enabled"].(bool),
			FeeType:         parsed["fee_type"].(string),
			MarketplaceCode: constant.MarketplaceCodeShopee,
			Limitations: []schemas2.Limitation{
				{
					MaxWeight:     float32(weightLimit["item_max_weight"].(float64)),
					MinWeight:     float32(weightLimit["item_min_weight"].(float64)),
					Length:        float32(dimensionLimit["length"].(float64)),
					Height:        float32(dimensionLimit["height"].(float64)),
					Width:         float32(dimensionLimit["width"].(float64)),
					DimensionUnit: dimensionLimit["unit"].(string),
					DimensionSum:  float32(dimensionLimit["dimension_sum"].(float64)),
					MaxVolume:     float32(volumeLimit["item_max_volume"].(float64)),
					MinVolume:     float32(volumeLimit["item_min_volume"].(float64)),
				},
			},
		}

		if exist != nil {
			combined, err := util.CombineData(exist, &mock)
			if err != nil {
				return nil, fmt.Errorf("failed to combine data: %w", err)
			}

			_, err = opts.LogisticServ.Update(combined)
			if err != nil {
				return nil, fmt.Errorf("failed to update logistic: %w", err)
			}

			continue
		}

		_, err = opts.LogisticServ.Insert(mock)
		if err != nil {
			return nil, fmt.Errorf("failed to insert logistic: %w", err)
		}
	}

	return response, nil
}
