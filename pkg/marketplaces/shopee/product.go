package shopee

import (
	"context"
	"errors"
	"fmt"
	"log"
	"strconv"
	"strings"
	"sync"
	"time"

	schemas2 "abs-scheduler-go/internal/scheduler_tasks/schemas"
	schemas3 "abs-scheduler-go/internal/task_executions/schemas"
	"abs-scheduler-go/pkg/config"
	"abs-scheduler-go/pkg/constant"
	"abs-scheduler-go/pkg/queue"
	"abs-scheduler-go/pkg/util"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func GenerateProductTaskList(payload FetchOrderListOpts) (string, error) {
	baseUrl, partnerId, _, _, err := getBaseURLAndPartnerId()
	if err != nil {
		return "", err
	}

	path := "/api/v2/product/get_item_list"

	insert, err := payload.Service.Insert(schemas3.TaskExecution{
		SchedulerTask:   payload.Schedule.ID,
		Batch:           payload.Schedule.LastBatch,
		Status:          schemas3.TaskExecutionStatusWaiting,
		Trigger:         schemas3.TaskExecutionTriggerCron,
		Operation:       schemas2.SchedulerTaskOperationFetchProductList,
		MarketplaceCode: payload.Store.MarketplaceCode,
		StartTime:       primitive.NewDateTimeFromTime(time.Now()),
		EndTime:         primitive.DateTime(0),
		RetryCount:      0,
		MaxRetries:      payload.Schedule.MaxRetries,
		Company:         payload.Store.Company,
		Store:           payload.Store.ID,
		RequestParam: map[string]interface{}{
			"partner_id":  partnerId,
			"shop_id":     payload.Store.MarketplaceId,
			"offset":      0,
			"page_size":   100,
			"item_status": []string{"NORMAL", "UNLIST", "REVIEWING"},
		},
		RequestHeader: map[string]interface{}{
			"Content-Type": "application/json",
		},
		RequestBody:   nil,
		RequestUrl:    baseUrl + path,
		RequestMethod: string(constant.HttpMethodGet),
	})
	if err != nil {
		return "", err
	}

	return insert, nil
}

func GetProductList(opts getListOpts) error {
	// initialize required params
	baseUrl, partnerId, partnerKey, _, err := getBaseURLAndPartnerId()
	if err != nil {
		return fmt.Errorf("failed to get base URL and partner ID: %w", err)
	}

	tokens, err := getToken(getTokenOpts{
		storeService: opts.storeService,
		Data:         &opts.data,
	})
	if err != nil {
		return fmt.Errorf("failed to get tokens: %w", err)
	}

	// prepare request params
	path := strings.Replace(opts.data.RequestUrl, baseUrl, "", -1)
	timestamp := generateTimestamp()
	shopId := opts.data.RequestParam["shop_id"].(string)
	sign, err := generateSign(partnerKey, partnerId+path+timestamp+tokens["access"]+shopId)
	if err != nil {
		return fmt.Errorf("failed to generate sign: %w", err)
	}

	// set auth request params
	opts.data.RequestParam["sign"] = sign
	opts.data.RequestParam["timestamp"] = timestamp
	opts.data.RequestParam["access_token"] = tokens["access"]
	opts.data.RequestParam["refresh_token"] = tokens["refresh"]

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// send request
	res, err := wrapWithRefresh(ctx, wrapWithRefreshOpts{
		SendOpts: &util.SendRequestOpts{
			Url:     opts.data.RequestUrl,
			Method:  opts.data.RequestMethod,
			Params:  opts.data.RequestParam,
			Body:    opts.data.RequestBody,
			Headers: opts.data.RequestHeader,
		},
		RefreshOpt: RefreshTokenOpts{
			Refresh:    tokens["refresh"],
			PartnerKey: partnerKey,
			PartnerId:  partnerId,
			ShopId:     opts.data.RequestParam["shop_id"].(string),
			BaseUrl:    baseUrl,
			Service:    opts.storeService,
		},
		StoreService: nil,
	})

	if err != nil {
		opts.data.RetryCount++
		_, err1 := opts.service.SetError(opts.data.ID, err.Error(), opts.data.RetryCount, opts.data.MaxRetries)
		if err1 != nil {
			return fmt.Errorf("error while set error message to db: %w", err)
		}

		if errors.Is(ctx.Err(), context.DeadlineExceeded) {
			return fmt.Errorf("API request timed out: %w", err)
		}

		return fmt.Errorf("request failed: %w", err)
	}

	// set encrypted token
	opts.data.RequestParam["access_token"] = tokens["encryptedAccess"]
	opts.data.RequestParam["refresh_token"] = tokens["encryptedRefresh"]

	itemList, err := extractProductList(res)
	if err != nil {
		return err
	}

	opts.list = itemList
	opts.key = "product"
	opts.result = &map[string][]string{
		"product_detail": nil,
		"product_list":   nil,
	}

	err = processProductBatches(&opts)
	if err != nil {
		return err
	}

	more, offset, err := checkHasMoreProduct(res)
	if err != nil {
		more = false
		//return err
	}
	if more {
		err := handleProductPagination(&opts, offset)
		if err != nil {
			return err
		}
	}

	err = queueJobs(&opts)
	if err != nil {
		return err
	}

	// decrement queue job count for scheduler batches
	if !opts.data.SchedulerTask.IsZero() {
		incrementQueueJobCount(incrementQueueJobOpts{
			data:        opts.data,
			redis:       opts.redis,
			insertedIds: *opts.result,
		})
	}

	return nil
}

func GetProductDetail(opts getListOpts) error {
	var itemList []map[string]interface{}

	tokens, err := getToken(getTokenOpts{
		storeService: opts.storeService,
		Data:         &opts.data,
	})
	if err != nil {
		return err
	}

	err = getProductDetail(getDetailOpts{
		storeService: opts.storeService,
		data:         opts.data,
		service:      opts.service,
		list:         &itemList,
		accessToken:  tokens["access"],
		refreshToken: tokens["refresh"],
	})
	if err != nil {
		return err
	}

	insertedIds, err := genProductTranslateTaskExecution(genProductTranslateExecutionOpts{
		data:     itemList,
		service:  opts.service,
		existing: opts.data,
	})
	if err != nil {
		return err
	}

	for _, id := range insertedIds {
		err := opts.queueServ.AddJob(queue.JobStoreKey, queue.Job{
			Key:      "scheduler:task_execution",
			Priority: int(opts.data.Priority + 1),
			Value: queue.JobValue{
				Event:     queue.EventWaiting,
				Id:        id,
				PrevEvent: "",
				Name:      "translate_product",
				Platform:  constant.MarketplaceCodeShopee,
				MaxRetry:  int(opts.data.MaxRetries),
			},
		})
		if err != nil {
			return err
		}
	}

	if !opts.data.SchedulerTask.IsZero() {
		err = opts.redis.IncrBy(
			context.Background(),
			"scheduler-"+opts.data.SchedulerTask.Hex()+"-batch-"+opts.data.Batch,
			int64(len(insertedIds)),
		).Err()
		if err != nil {
			log.Printf("error while add job count at job %v: %v", opts.data, err.Error())
		}
	}

	return nil
}

func StockAdjustment(opts StockAdjustmentOpts) (map[string][]string, error) {
	tokens, err := getToken(getTokenOpts{
		storeService: opts.StoreServ,
		Data:         &opts.Data,
	})
	if err != nil {
		return nil, err
	}

	skus := opts.Data.RequestBody["skus"].([]interface{})
	baseUrl, partnerId, partnerKey, _, err := getBaseURLAndPartnerId()
	if err != nil {
		return nil, fmt.Errorf("failed to get base URL and partner ID: %w", err)
	}

	groupingProduct := make(map[string]interface{})
	for _, item := range skus {
		parsed, ok := item.(map[string]interface{})
		if !ok {
			return nil, fmt.Errorf("failed to parse item")
		}

		sku := parsed["sku"].(string)
		shopId := parsed["shop_id"].(string)
		indexKey := sku + "~" + shopId

		if shopId != opts.Data.RequestParam["shop_id"].(string) {
			continue
		}
		if exist := groupingProduct[indexKey]; exist == nil {
			groupingProduct[indexKey] = map[string]interface{}{
				"model":   make([]map[string]interface{}, 0),
				"sku":     sku,
				"shop_id": parsed["shop_id"],
			}
		}

		product, _ := groupingProduct[indexKey].(map[string]interface{})
		existingModels, _ := product["model"].([]map[string]interface{})
		product["model"] = append(existingModels, parsed)
		groupingProduct[indexKey] = product
	}

	opts.Data.RequestParam["partner_id"] = partnerId

	result := map[string][]string{"success": make([]string, 0), "failed": make([]string, 0)}

	lists := util.ObjectValues(groupingProduct)
	for len(lists) > 0 {
		items := util.Splice(&lists, 0, 30)

		var wg sync.WaitGroup
		var mu sync.Mutex

		wg.Add(len(items))

		for _, item := range items {
			go func(item interface{}, taskExecution schemas3.TaskExecution, result map[string][]string) {
				ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)

				defer wg.Done()
				defer cancel()

				parsed, ok := item.(map[string]interface{})
				if !ok {
					mu.Lock()
					log.Printf("error while parsing item: %v", item)
					result["failed"] = append(result["failed"], fmt.Sprintf("%v", item))
					mu.Unlock()
					return
				}

				paramsCopy := make(map[string]interface{})
				for k, v := range taskExecution.RequestParam {
					paramsCopy[k] = v
				}
				paramsCopy["shop_id"] = parsed["shop_id"]

				models, ok := parsed["model"].([]map[string]interface{})
				if !ok {
					mu.Lock()
					log.Printf("error while parsing model: %v", item)
					result["failed"] = append(result["failed"], fmt.Sprintf("%v", item))
					mu.Unlock()
					return
				}
				itemId, err := strconv.ParseUint(models[0]["product_id"].(string), 10, 64)
				if err != nil {
					mu.Lock()
					log.Printf("error while parsing item id: %v", item)
					result["failed"] = append(result["failed"], fmt.Sprintf("%v", item))
					mu.Unlock()
					return
				}

				body := map[string]interface{}{"item_id": itemId, "stock_list": make([]map[string]interface{}, 0)}
				stockList := body["stock_list"].([]map[string]interface{})
				for _, model := range models {
					var modelId uint64 = 0

					productId, err := strconv.ParseUint(model["product_id"].(string), 10, 64)
					if err != nil {
						mu.Lock()
						log.Printf("error while parsing product id: %v", item)
						result["failed"] = append(result["failed"], fmt.Sprintf("%v", item))
						mu.Unlock()
						return
					}
					variantId, err := strconv.ParseUint(model["variant_id"].(string), 10, 64)
					if err != nil {
						mu.Lock()
						log.Printf("error while parsing variant id: %v", item)
						result["failed"] = append(result["failed"], fmt.Sprintf("%v", item))
						mu.Unlock()
						return
					}

					if variantId != productId {
						modelId = variantId
					}

					stockList = append(stockList, map[string]interface{}{"model_id": modelId, "seller_stock": []map[string]interface{}{
						{
							"stock": model["qty"],
						},
					}})
				}
				body["stock_list"] = stockList

				timestamp := generateTimestamp()
				sign, err := generateSign(config.ShopeePartnerKey, config.ShopeePartnerId+taskExecution.RequestUrl+timestamp+tokens["access"]+opts.Data.RequestParam["shop_id"].(string))

				opts.Data.RequestParam["sign"] = sign
				opts.Data.RequestParam["timestamp"] = timestamp
				opts.Data.RequestParam["access_token"] = tokens["refresh"]
				opts.Data.RequestParam["refresh_token"] = tokens["refresh"]

				res, err := wrapWithRefresh(ctx, wrapWithRefreshOpts{
					SendOpts: &util.SendRequestOpts{
						Url:     baseUrl + taskExecution.RequestUrl,
						Method:  taskExecution.RequestMethod,
						Params:  taskExecution.RequestParam,
						Body:    body,
						Headers: taskExecution.RequestHeader,
					},
					RefreshOpt: RefreshTokenOpts{
						Refresh:    tokens["refresh"],
						PartnerKey: partnerKey,
						PartnerId:  partnerId,
						ShopId:     taskExecution.RequestParam["shop_id"].(string),
						BaseUrl:    baseUrl,
						Service:    opts.StoreServ,
					},
					StoreService: nil,
				})
				if err != nil {
					mu.Lock()
					if errors.Is(ctx.Err(), context.DeadlineExceeded) {
						result["failed"] = append(result["failed"], parsed["sku"].(string))
						log.Printf("API request timed out: %v", err)
						mu.Unlock()
						return
					}
					result["failed"] = append(result["failed"], parsed["sku"].(string))
					mu.Unlock()
					log.Printf("error while send request: %v", err)
					return
				}

				response, ok := res["response"].(map[string]interface{})
				if !ok {
					mu.Lock()
					result["failed"] = append(result["failed"], parsed["sku"].(string))
					mu.Unlock()
					log.Printf("error while parsing response: %v", err)
					return
				}

				if response["error"] != nil || response["error"] == "" || response["error"] == "-" {
					mu.Lock()
					result["failed"] = append(result["failed"], parsed["sku"].(string))
					mu.Unlock()
					log.Printf("error while parsing response: %v", err)
					return
				}

				mu.Lock()
				result["success"] = append(result["success"], parsed["sku"].(string))
				mu.Unlock()
			}(item, opts.Data, result)
		}

		wg.Wait()
	}

	return result, nil
}
