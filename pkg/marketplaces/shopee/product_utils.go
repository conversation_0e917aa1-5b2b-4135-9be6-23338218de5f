package shopee

import (
	"context"
	"errors"
	"fmt"
	"log"
	"math"
	"strconv"
	"strings"
	"sync"
	"time"

	schemas4 "abs-scheduler-go/internal/products/schemas"
	schemas2 "abs-scheduler-go/internal/scheduler_tasks/schemas"
	schemas3 "abs-scheduler-go/internal/task_executions/schemas"
	"abs-scheduler-go/pkg/config"
	"abs-scheduler-go/pkg/constant"
	"abs-scheduler-go/pkg/util"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var (
	ErrFieldNotFound = errors.New("field not found or not a JSON object")
	ErrNonObjectItem = errors.New("contains a non-object item")
)

func extractProductList(res map[string]interface{}) ([]map[string]interface{}, error) {
	response, ok := res["response"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("%w: response", ErrFieldNotFound)
	}

	itemListRaw, ok := response["item"].([]interface{})
	if !ok {
		return nil, fmt.<PERSON><PERSON><PERSON>("%w: response.item", ErrFieldNotFound)
	}

	return util.ConvertToMapSlice(itemListRaw)
}

func extractProductDetailsFromResponse(res map[string]interface{}, list *[]map[string]interface{}) error {
	response, ok := res["response"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("%w: response", ErrFieldNotFound)
	}

	itemListRaw, ok := response["item_list"].([]interface{})
	if !ok {
		return fmt.Errorf("%w: response.item_list", ErrFieldNotFound)
	}

	items, err := util.ConvertToMapSlice(itemListRaw)
	if err != nil {
		return err
	}

	*list = append(*list, items...)
	return nil
}

func checkHasMoreProduct(res map[string]interface{}) (more bool, offset string, err error) {
	response, ok := res["response"].(map[string]interface{})
	if !ok {
		return false, "", fmt.Errorf("%w: response", ErrFieldNotFound)
	}

	more, ok = response["has_next_page"].(bool)
	if !ok || !more {
		return false, "", fmt.Errorf("%w: has_next_page", ErrFieldNotFound)
	}

	nextCursor := fmt.Sprintf("%v", response["next_offset"])
	return true, nextCursor, nil
}

func handleProductPagination(opts *getListOpts, offset string) error {
	insertedId, err := genNextPageList(genNextPageOrderListOpts{
		existing:  opts.data,
		serv:      opts.service,
		operation: schemas2.SchedulerTaskOperationFetchProductList,
		additionalParams: map[string]interface{}{
			"item_status": opts.data.RequestParam["item_status"],
			"offset":      offset,
		},
	})

	if err != nil {
		(*opts.result)["product_list"] = append((*opts.result)["product_list"], insertedId)

		deletedIds := []string{insertedId}
		if len((*opts.result)["product_detail"]) > 0 {
			deletedIds = append(deletedIds, (*opts.result)["product_detail"]...)
		}

		if _, err2 := opts.service.DeleteMany(deletedIds); err2 != nil {
			log.Printf("failed to delete tasks after pagination error: %v", err2)
		}

		return fmt.Errorf("failed to generate next page: %w", err)
	}

	(*opts.result)["product_list"] = append((*opts.result)["product_list"], insertedId)
	return nil
}

func processProductBatches(opts *getListOpts) error {
	const batchSize = 50
	batches := util.SplitIntoBatches(opts.list, batchSize)

	var wg sync.WaitGroup
	errCh := make(chan error, len(batches))

	for _, batch := range batches {
		wg.Add(1)
		go func(batch []map[string]interface{}) {
			defer wg.Done()

			ids := extractItemIds(batch)
			execution, err := genProductDetailTaskExecution(genDetailTaskExecutionOpts{
				ids:      ids,
				service:  opts.service,
				existing: opts.data,
			})

			if err != nil {
				errCh <- fmt.Errorf("failed to generate product detail task: %w", err)
				return
			}

			// Use mutex to safely append to the result map
			opts.mu.Lock()
			(*opts.result)["product_detail"] = append((*opts.result)["product_detail"], execution)
			opts.mu.Unlock()
		}(batch)
	}

	// Wait for all goroutines to complete
	wg.Wait()
	close(errCh)

	// Return the first error encountered, if any
	for err := range errCh {
		if err != nil {
			return err
		}
	}

	return nil
}

func extractItemIds(batch []map[string]interface{}) []string {
	ids := make([]string, 0, len(batch))
	for _, item := range batch {
		if itemID, ok := item["item_id"].(float64); ok {
			ids = append(ids, strconv.Itoa(int(itemID)))
		}
	}
	return ids
}

func genProductDetailTaskExecution(opts genDetailTaskExecutionOpts) (string, error) {
	const path = "/api/v2/product/get_item_base_info"

	insert, err := opts.service.Insert(schemas3.TaskExecution{
		SchedulerTask:   opts.existing.SchedulerTask,
		Batch:           opts.existing.Batch,
		Status:          schemas3.TaskExecutionStatusWaiting,
		Trigger:         schemas3.TaskExecutionTriggerCron,
		Operation:       schemas2.SchedulerTaskOperationFetchProductDetail,
		MarketplaceCode: opts.existing.MarketplaceCode,
		StartTime:       primitive.NewDateTimeFromTime(time.Now()),
		EndTime:         primitive.DateTime(0),
		RetryCount:      0,
		MaxRetries:      opts.existing.MaxRetries,
		Company:         opts.existing.Company,
		Store:           opts.existing.Store,
		RequestParam: map[string]interface{}{
			"access_token":  opts.existing.RequestParam["access_token"],
			"refresh_token": opts.existing.RequestParam["refresh_token"],
			"partner_id":    config.ShopeePartnerId,
			"shop_id":       opts.existing.RequestParam["shop_id"],
			"item_id_list":  strings.Join(opts.ids, ","),
		},
		RequestHeader: map[string]interface{}{
			"Content-Type": "application/json",
		},
		RequestBody:   nil,
		RequestUrl:    config.ShopeeBaseUrl + path,
		RequestMethod: string(constant.HttpMethodGet),
	})

	return insert, err
}

// getProductDetail fetches product details with improved error handling
func getProductDetail(opts getDetailOpts) error {
	path := strings.Replace(opts.data.RequestUrl, config.ShopeeBaseUrl, "", -1)
	timestamp := generateTimestamp()
	sign, err := generateSign(config.ShopeePartnerKey, config.ShopeePartnerId+path+timestamp+opts.accessToken+opts.data.RequestParam["shop_id"].(string))

	if err != nil {
		return fmt.Errorf("failed to generate signature: %w", err)
	}

	// Prepare request parameters
	opts.data.RequestParam["sign"] = sign
	opts.data.RequestParam["timestamp"] = timestamp
	opts.data.RequestParam["access_token"] = opts.accessToken
	opts.data.RequestParam["refresh_token"] = opts.refreshToken

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Make API request with token refresh handling
	res, err := wrapWithRefresh(ctx, wrapWithRefreshOpts{
		SendOpts: &util.SendRequestOpts{
			Url:     opts.data.RequestUrl,
			Method:  opts.data.RequestMethod,
			Params:  opts.data.RequestParam,
			Body:    opts.data.RequestBody,
			Headers: opts.data.RequestHeader,
		},
		RefreshOpt: RefreshTokenOpts{
			Refresh:    opts.refreshToken,
			PartnerKey: config.ShopeePartnerKey,
			PartnerId:  config.ShopeePartnerId,
			ShopId:     opts.data.RequestParam["shop_id"].(string),
			BaseUrl:    config.ShopeeBaseUrl,
			Service:    opts.storeService,
		},
	})

	if err != nil {
		opts.data.RetryCount++
		if _, updateErr := opts.service.SetError(opts.data.ID, err.Error(), opts.data.RetryCount, opts.data.MaxRetries); updateErr != nil {
			return fmt.Errorf("failed to update error status: %w (original error: %v)", updateErr, err)
		}

		if errors.Is(ctx.Err(), context.DeadlineExceeded) {
			return fmt.Errorf("API request timed out: %w", err)
		}
		return fmt.Errorf("API request failed: %w", err)
	}

	// Process response
	return extractProductDetailsFromResponse(res, opts.list)
}

// Get product variant from Shopee API
func getProductVariant(opts getVariantOpts) error {
	const path = "/api/v2/product/get_model_list"

	timestamp := generateTimestamp()
	sign, err := generateSign(config.ShopeePartnerKey, config.ShopeePartnerId+path+timestamp+opts.accessToken+opts.data.RequestParam["shop_id"].(string))
	if err != nil {
		return err
	}

	// Prepare request parameters
	opts.data.RequestUrl = config.ShopeeBaseUrl + path
	opts.data.RequestParam["sign"] = sign
	opts.data.RequestParam["timestamp"] = timestamp
	opts.data.RequestParam["access_token"] = opts.accessToken
	opts.data.RequestParam["refresh_token"] = opts.refreshToken
	opts.data.RequestParam["item_id"] = int(opts.data.RequestBody["item_id"].(float64))

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Make API request with token refresh handling
	res, err := wrapWithRefresh(ctx, wrapWithRefreshOpts{
		SendOpts: &util.SendRequestOpts{
			Url:     opts.data.RequestUrl,
			Method:  opts.data.RequestMethod,
			Params:  opts.data.RequestParam,
			Body:    opts.data.RequestBody,
			Headers: opts.data.RequestHeader,
		},
		RefreshOpt: RefreshTokenOpts{
			Refresh:    opts.refreshToken,
			PartnerKey: config.ShopeePartnerKey,
			PartnerId:  config.ShopeePartnerId,
			ShopId:     opts.data.RequestParam["shop_id"].(string),
			BaseUrl:    config.ShopeeBaseUrl,
			Service:    opts.storeService,
		},
	})

	if err != nil {
		if errors.Is(ctx.Err(), context.DeadlineExceeded) {
			return fmt.Errorf("API request get variant timed out: %w", err)
		}
		return fmt.Errorf("error while get product variant %w", err)
	}

	// Extract variant data from response
	response, ok := res["response"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("%w: response", ErrFieldNotFound)
	}

	// Store variant data in request body
	opts.data.RequestBody["variant"] = response
	return nil
}

// Generate task execution for product translation
func genProductTranslateTaskExecution(opts genProductTranslateExecutionOpts) ([]string, error) {
	var tasks []schemas3.TaskExecution
	for _, data := range opts.data {
		tasks = append(tasks, schemas3.TaskExecution{
			SchedulerTask:   opts.existing.SchedulerTask,
			Batch:           opts.existing.Batch,
			Status:          schemas3.TaskExecutionStatusWaiting,
			Trigger:         schemas3.TaskExecutionTriggerCron,
			Operation:       schemas2.SchedulerTaskOperationTranslateProduct,
			MarketplaceCode: opts.existing.MarketplaceCode,
			StartTime:       primitive.NewDateTimeFromTime(time.Now()),
			EndTime:         primitive.DateTime(0),
			RetryCount:      0,
			MaxRetries:      opts.existing.MaxRetries,
			Company:         opts.existing.Company,
			Store:           opts.existing.Store,
			RequestBody:     data,
			RequestParam: map[string]interface{}{
				"shop_id":    opts.existing.RequestParam["shop_id"],
				"partner_id": opts.existing.RequestParam["partner_id"],
			},
		})
	}

	return opts.service.InsertMany(tasks)
}

func parseProduct(product map[string]interface{}) (map[string]interface{}, error) {
	priceInfo := make(map[string]interface{})
	if exist, ok := product["price_info"]; ok && exist != nil {
		parsed, ok := exist.(primitive.A)
		if !ok {
			return nil, fmt.Errorf("%w: product.price_info", ErrFieldNotFound)
		}
		priceInfo = parsed[0].(map[string]interface{})
	}

	currPrice := 0.0
	if exist, ok := priceInfo["current_price"]; ok && exist != nil {
		currPrice = exist.(float64)
	}
	oriPrice := 0.0
	if exist, ok := priceInfo["original_price"]; ok && exist != nil {
		oriPrice = exist.(float64)
	}
	currency := ""
	if exist, ok := priceInfo["currency"]; ok && exist != nil {
		currency = exist.(string)
	}

	dimension, ok := product["dimension"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("%w: product.dimension", ErrFieldNotFound)
	}

	po, ok := product["pre_order"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("%w: product.pre_order", ErrFieldNotFound)
	}

	itemStatus, ok := product["item_status"].(string)
	if !ok {
		return nil, fmt.Errorf("%w: product.item_status", ErrFieldNotFound)
	}

	productStatus := schemas4.ProductStatusInactive
	if itemStatus == "NORMAL" {
		productStatus = schemas4.ProductStatusActive
	}

	description, err := extractDescription(product)
	if err != nil {
		return nil, err
	}

	images, err := extractImages(product)
	if err != nil {
		return nil, err
	}

	attributes, err := extractAttributes(product)
	if err != nil {
		return nil, err
	}

	logistics, err := extractLogistics(product)
	if err != nil {
		return nil, err
	}

	stock, err := extractStock(product)
	if err != nil {
		return nil, err
	}

	availableStock := 0
	if exist, ok := stock["total_available_stock"]; ok && exist != nil {
		availableStock = int(exist.(float64))
	}
	reservedStock := 0
	if exist, ok := stock["total_reserved_stock"]; ok && exist != nil {
		reservedStock = int(exist.(float64))
	}

	weight, err := strconv.ParseFloat(product["weight"].(string), 64)
	if err != nil {
		return nil, errors.New("error parsing weight to float")
	}

	productMock := map[string]interface{}{
		"item_id":         strconv.Itoa(int(product["item_id"].(float64))),
		"category_id":     strconv.Itoa(int(product["category_id"].(float64))),
		"item_name":       product["item_name"].(string),
		"currency":        currency,
		"current_price":   currPrice,
		"original_price":  oriPrice,
		"description":     description,
		"images":          images,
		"height":          int(math.Ceil(dimension["package_height"].(float64))),
		"length":          int(math.Ceil(dimension["package_length"].(float64))),
		"width":           int(math.Ceil(dimension["package_width"].(float64))),
		"weight":          int(math.Ceil(weight)),
		"sku":             product["item_sku"].(string),
		"is_visible":      true,
		"is_main":         false,
		"attributes":      attributes,
		"logistics":       logistics,
		"available_stock": availableStock,
		"reversed_stock":  reservedStock,
		"status":          productStatus,
		"is_pre_order":    po["is_pre_order"].(bool),
		"days_to_ship":    int(po["days_to_ship"].(float64)),
		"tier_variations": make([]map[string]interface{}, 0),
		"variants":        make(map[string]interface{}),
	}
	productMock["variants"] = productMock

	return productMock, nil
}

func parseVariant(variant map[string]interface{}) (map[string]interface{}, error) {
	model, ok := variant["model"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("%w: variant.model", ErrFieldNotFound)
	}

	prices, ok := model["price_info"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("%w: variant.price_info, type: %T", ErrFieldNotFound, model["price_info"])
	}

	po, ok := model["pre_order"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("%w: variant.pre_order", ErrFieldNotFound)
	}

	stock := make(map[string]interface{})
	if exist, ok := model["stock_info_v2"]; ok && exist != nil {
		stock = exist.(map[string]interface{})
	}

	stockSummary := make(map[string]interface{})
	if exist, ok := stock["summary_info"]; ok && exist != nil {
		stockSummary = exist.(map[string]interface{})
	}

	dimension, ok := model["dimension"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("%w: variant.dimension", ErrFieldNotFound)
	}

	price, ok := prices[0].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("%w: variant.price_info", ErrFieldNotFound)
	}

	currPrice := 0.0
	if exist, ok := price["current_price"]; ok && exist != nil {
		currPrice = exist.(float64)
	}
	oriPrice := 0.0
	if exist, ok := price["original_price"]; ok && exist != nil {
		oriPrice = exist.(float64)
	}

	tierVariation, ok := variant["tier_variation"].([]interface{})
	if !ok {
		return nil, fmt.Errorf("%w: variant.tier_variation", ErrFieldNotFound)
	}

	itemStatus, ok := model["model_status"].(string)
	if !ok {
		return nil, fmt.Errorf("%w: product.item_status", ErrFieldNotFound)
	}

	productStatus := schemas4.ProductStatusInactive
	if itemStatus == "MODEL_NORMAL" {
		productStatus = schemas4.ProductStatusActive
	}

	weight, err := strconv.ParseFloat(model["weight"].(string), 64)
	if err != nil {
		return nil, errors.New("error parsing weight to float")
	}

	return map[string]interface{}{
		"item_id":         strconv.Itoa(int(model["model_id"].(float64))),
		"sku":             model["model_sku"].(string),
		"status":          productStatus,
		"currency":        price["currency"].(string),
		"current_price":   currPrice,
		"original_price":  oriPrice,
		"available_stock": int(stockSummary["total_available_stock"].(float64)),
		"reversed_stock":  int(stockSummary["total_reserved_stock"].(float64)),
		"weight":          weight,
		"height":          int(math.Ceil(dimension["package_height"].(float64))),
		"length":          int(math.Ceil(dimension["package_length"].(float64))),
		"width":           int(math.Ceil(dimension["package_width"].(float64))),
		"is_pre_order":    po["is_pre_order"].(bool),
		"days_to_ship":    int(po["days_to_ship"].(float64)),
		"tier_variations": tierVariation,
	}, nil
}

func extractStock(data map[string]interface{}) (map[string]interface{}, error) {
	stock, ok := data["stock_info_v2"].(map[string]interface{})
	if !ok {
		return nil, nil
	}

	stockSummary, ok := stock["summary_info"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("%w: variant.summary_info", ErrFieldNotFound)
	}

	return stockSummary, nil
}

func extractDescription(data map[string]interface{}) ([]map[string]string, error) {
	var description []map[string]string
	if data["description_type"].(string) == "extended" {
		parsed, ok := data["description_info"].(map[string]interface{})
		if !ok {
			return nil, fmt.Errorf("%w: product.description_info", ErrFieldNotFound)
		}
		desc, ok := parsed["extended_description"].(map[string]interface{})
		if !ok {
			return nil, fmt.Errorf("%w: product.description_info.extended_description, %T", ErrFieldNotFound, parsed["extended_description"])
		}
		parsedDesc, ok := desc["field_list"].(primitive.A)
		if !ok {
			return nil, fmt.Errorf("%w: product.description_info.extended_description.field_list", ErrFieldNotFound)
		}

		for _, item := range parsedDesc {
			parse, ok := item.(map[string]interface{})
			if !ok {
				return nil, fmt.Errorf("%w: product.description_info.extended_description", ErrNonObjectItem)
			}

			fieldType, ok := parse["field_type"].(string)
			if !ok {
				return nil, fmt.Errorf("%w: product.description_info.extended_description.field_list.field_type", ErrNonObjectItem)
			}

			value := ""
			if fieldType == "text" {
				value = parse["text"].(string)
			}
			if fieldType == "image" {
				image, ok := parse["image_info"].(map[string]interface{})
				if !ok {
					return nil, fmt.Errorf("%w: product.description_info.extended_description.field_list.image_info", ErrNonObjectItem)
				}
				value = image["image_url"].(string)
			}

			description = append(description, map[string]string{
				"type":  fieldType,
				"value": value,
			})
		}
	}
	return description, nil
}

// Translate product data
func translateProduct(opts translateProductOpts) error {
	// Check if product has models (variants)
	hasModel := false
	if check, ok := opts.data.RequestBody["has_model"]; ok && check != nil {
		hasModel = opts.data.RequestBody["has_model"].(bool)
	}

	// If product has models, fetch variant data
	if hasModel {
		// Get store information
		store, err := opts.storeServ.GetByMpId(opts.data.RequestParam["shop_id"].(string), constant.MarketplaceCodeShopee)
		if err != nil {
			return fmt.Errorf("failed to get store: %w", err)
		}

		// Set access and refresh tokens
		opts.data.RequestParam["access_token"] = store.MarketplaceCredential.EncryptedAccessValue
		opts.data.RequestParam["refresh_token"] = store.MarketplaceCredential.EncryptedRefreshValue

		// Decrypt tokens
		access, refresh, err := DecryptTokens(opts.data.RequestParam)
		if err != nil {
			return fmt.Errorf("failed to decrypt tokens: %w", err)
		}

		// Get variant data
		err = getProductVariant(getVariantOpts{
			storeService: opts.storeServ,
			data:         &opts.data,
			accessToken:  access,
			refreshToken: refresh,
		})
		if err != nil {
			return fmt.Errorf("get product variant %w", err)
		}

		// Update task body with variant data
		_, err = opts.taskServ.UpdateBody(opts.data.ID, opts.data.RequestBody)
		if err != nil {
			return fmt.Errorf("update product variant %s error: %w", opts.data.ID, err)
		}
	}

	// Parse product data
	parsedProduct, err := parseProduct(opts.data.RequestBody)
	if err != nil {
		return fmt.Errorf("parse product error: %w", err)
	}
	parsedProduct["company"] = opts.data.RequestBody["company"].(primitive.ObjectID)

	// Process variant or regular product
	if variant, ok := opts.data.RequestBody["variant"].(map[string]interface{}); ok && variant != nil {
		err := proceedVariantProduct(proceedVariantOpts{
			product:     parsedProduct,
			header:      opts.data.RequestParam,
			data:        variant,
			productServ: opts.productServ,
		})
		if err != nil {
			return fmt.Errorf("proceed variant failed: %w", err)
		}
	} else {
		err := proceedTranslate(proceedTranslateOpts{
			product:     parsedProduct,
			header:      opts.data.RequestParam,
			productServ: opts.productServ,
		})
		if err != nil {
			return fmt.Errorf("proceed product failed: %w", err)
		}
	}

	return nil
}

func fetchExistingProducts(opts fetchExistingProductOpts) {
	go func() {
		defer opts.wg.Done()
		if opts.productChildren == nil {
			*opts.resultCh <- productResult{product: nil, err: nil}
			return
		}
		product, err := opts.productServ.FindProduct(opts.productChildren.Product)
		if err != nil {
			*opts.resultCh <- productResult{product: nil, err: fmt.Errorf("error finding product: %w", err)}
			return
		}
		*opts.resultCh <- productResult{product: product, err: nil}
	}()

	go func() {
		defer opts.wg.Done()
		if opts.productChildren == nil {
			*opts.resultCh <- productResult{productVariant: nil, err: nil}
			return
		}
		productVariant, err := opts.productServ.FindProductVariant(opts.productChildren.Product)
		if err != nil {
			*opts.resultCh <- productResult{productVariant: nil, err: fmt.Errorf("error finding product variant: %w", err)}
			return
		}
		*opts.resultCh <- productResult{productVariant: productVariant, err: nil}
	}()

	go func() {
		defer opts.wg.Done()
		if opts.productChildren == nil {
			*opts.resultCh <- productResult{productVariantChild: nil, err: nil}
			return
		}
		productVariantChildren, err := opts.productServ.FindProductVariantChildren(opts.itemId, constant.MarketplaceCodeShopee)
		if err != nil {
			*opts.resultCh <- productResult{productVariantChild: nil, err: fmt.Errorf("error finding product variant children: %w", err)}
			return
		}
		*opts.resultCh <- productResult{productVariantChild: productVariantChildren, err: nil}
	}()
}

// Process translation data
func proceedTranslate(opts proceedTranslateOpts) error {
	variant, ok := opts.product["variants"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("error parsing variant data")
	}

	productChildrenPointer, err := opts.productServ.FindProductChildren(opts.product["item_id"].(string), constant.MarketplaceCodeShopee)
	if err != nil {
		return fmt.Errorf("error finding product when proceed translate: %w", err)
	}

	var wg sync.WaitGroup
	resultCh := make(chan productResult, 3)
	wg.Add(3)
	fetchExistingProducts(fetchExistingProductOpts{
		wg:              &wg,
		itemId:          variant["item_id"].(string),
		productChildren: productChildrenPointer,
		productServ:     opts.productServ,
		resultCh:        &resultCh,
	})

	wg.Wait()
	close(resultCh)

	product := schemas4.ProductSchema{}
	productVariant := schemas4.ProductVariantSchema{}
	productVariantChildren := schemas4.ProductVariantChildrenSchema{}
	productStock := schemas4.ProductStockSchema{}
	productChildren := schemas4.ProductChildrenSchema{}

	if productChildrenPointer != nil {
		productChildren = *productChildrenPointer
	}

	for result := range resultCh {
		if result.err != nil {
			return fmt.Errorf("error in parallel data fetching: %w", result.err)
		}

		if result.product != nil {
			product = *result.product
		}
		if result.productVariant != nil {
			productVariant = *result.productVariant
		}
		if result.productVariantChild != nil {
			productVariantChildren = *result.productVariantChild
		}
	}

	startTime := time.Now()

	err = proceedProduct(proceedProductOpts{
		product:     opts.product,
		images:      opts.product["images"].([]schemas4.ProductImage),
		exist:       &product,
		productServ: opts.productServ,
	})
	if err != nil {
		return err
	}

	err = proceedProductChildren(proceedProductChildrenOpts{
		productId:   product.Id,
		exist:       &productChildren,
		data:        opts.product,
		header:      opts.header,
		images:      opts.product["images"].([]schemas4.ProductImage),
		productServ: opts.productServ,
		startTime:   startTime,
	})
	if err != nil {
		return err
	}

	err = proceedProductVariant(proceedProductVariantOpts{
		itemId:            opts.product["item_id"].(string),
		productId:         product.Id,
		productChildrenId: productChildren.Id,
		exist:             &productVariant,
		data:              variant,
		images:            opts.product["images"].([]schemas4.ProductImage),
		productServ:       opts.productServ,
		startTime:         startTime,
	})
	if err != nil {
		return err
	}

	err = proceedProductStock(proceedProductStockOpts{
		productServ:       opts.productServ,
		sku:               productVariant.Sku,
		data:              variant,
		exist:             &productStock,
		companyId:         opts.product["company"].(primitive.ObjectID),
		productId:         product.Id,
		productChildrenId: productChildren.Id,
		productVariantId:  productVariant.Id,
		startTime:         startTime,
	})
	if err != nil {
		return err
	}

	err = proceedProductVariantChildren(proceedProductVariantChildrenOpts{
		itemId:            opts.product["item_id"].(string),
		sku:               productVariant.Sku,
		shopId:            opts.header["shop_id"].(string),
		productServ:       opts.productServ,
		exist:             &productVariantChildren,
		data:              variant,
		companyId:         opts.product["company"].(primitive.ObjectID),
		productId:         product.Id,
		productChildrenId: productChildren.Id,
		productVariantId:  productVariant.Id,
		productStockId:    productStock.Id,
		startTime:         startTime,
	})
	if err != nil {
		return err
	}

	return nil
}

// proceedVariantProduct processes variant products concurrently
func proceedVariantProduct(opts proceedVariantOpts) error {
	model, ok := opts.data["model"].([]interface{})
	if !ok {
		return fmt.Errorf("error parsing model to string or not found, type: %T", opts.data["model"])
	}

	variation, ok := opts.data["standardise_tier_variation"].([]interface{})
	if !ok {
		return fmt.Errorf("error parsing standardise_tier_variation to string or not found, type: %T", variation)
	}

	productChildrenPointer, err := opts.productServ.FindProductChildren(opts.product["item_id"].(string), constant.MarketplaceCodeShopee)
	if err != nil {
		return fmt.Errorf("error finding product when proceed translate: %w", err)
	}

	product := schemas4.ProductSchema{}
	productChildren := schemas4.ProductChildrenSchema{}

	if productChildrenPointer != nil {
		productChildren = *productChildrenPointer
	}

	startTime := time.Now()

	err = proceedProduct(proceedProductOpts{
		product:     opts.product,
		images:      opts.product["images"].([]schemas4.ProductImage),
		exist:       &product,
		productServ: opts.productServ,
	})
	if err != nil {
		return err
	}

	err = proceedProductChildren(proceedProductChildrenOpts{
		productId:   product.Id,
		exist:       &productChildren,
		data:        opts.product,
		header:      opts.header,
		images:      opts.product["images"].([]schemas4.ProductImage),
		productServ: opts.productServ,
		startTime:   startTime,
	})
	if err != nil {
		return err
	}

	// Process models concurrently with worker pool
	var wg sync.WaitGroup
	errCh := make(chan error, len(model))
	connCh := make(chan struct{}, 5) // Limit concurrent processing to 5 models at a time
	for _, data := range model {
		wg.Add(1)
		connCh <- struct{}{} // Acquire connCh

		go func(modelData interface{}) {
			defer wg.Done()
			defer func() { <-connCh }() // Release connCh

			parse, ok := modelData.(map[string]interface{})
			if !ok {
				errCh <- fmt.Errorf("error parsing model to string or not found, type: %T", modelData)
				return
			}

			variant, err := parseVariant(map[string]interface{}{
				"model":          parse,
				"tier_variation": variation,
			})
			if err != nil {
				errCh <- fmt.Errorf("error parsing variant: %w", err)
				return
			}

			// Create a deep copy of the product to avoid race conditions
			productCopy := make(map[string]interface{})
			for k, v := range opts.product {
				productCopy[k] = v
			}
			productCopy["variants"] = variant

			err = proceedTranslate(proceedTranslateOpts{
				product:     productCopy,
				header:      opts.header,
				productServ: opts.productServ,
			})
			if err != nil {
				errCh <- err
				return
			}
		}(data)
	}

	// Wait for all goroutines to complete
	wg.Wait()
	close(errCh)

	// Return the first error encountered, if any
	for err := range errCh {
		if err != nil {
			return err
		}
	}

	return nil
}

func proceedProduct(opts proceedProductOpts) error {
	productSchema := schemas4.ProductSchema{
		Name:         opts.product["item_name"].(string),
		Description:  opts.product["description"].([]map[string]string),
		ImageUrl:     opts.images[0].Url,
		Height:       opts.product["height"].(int),
		Width:        opts.product["width"].(int),
		Length:       opts.product["length"].(int),
		Weight:       opts.product["weight"].(int),
		Company:      opts.product["company"].(primitive.ObjectID),
		LastSyncedAt: primitive.NewDateTimeFromTime(time.Now()),
		Status:       opts.product["status"].(schemas4.ProductStatus),
	}

	if opts.exist.Id == primitive.NilObjectID {
		sku, err := opts.productServ.GetSku("product")
		if err != nil {
			return fmt.Errorf("error while generating sku: %w", err)
		}
		productSchema.Sku = sku

		productID, err := opts.productServ.InsertProduct(productSchema)
		if err != nil {
			return fmt.Errorf("error while inserting product: %w", err)
		}

		productObjID, err := primitive.ObjectIDFromHex(productID)
		if err != nil {
			return fmt.Errorf("error parsing product ID: %w", err)
		}
		opts.exist.Id = productObjID
	} else {
		updatedProduct, err := util.CombineData(opts.exist, &productSchema)
		if err != nil {
			return fmt.Errorf("error combining product data: %w", err)
		}

		_, err = opts.productServ.UpdateProduct(updatedProduct)
		if err != nil {
			return fmt.Errorf("error updating product: %w", err)
		}
	}
	return nil
}

func proceedProductChildren(opts proceedProductChildrenOpts) error {
	productChildSchema := schemas4.ProductChildrenSchema{
		Product:         opts.productId,
		SkuMarketplace:  opts.data["sku"].(string),
		MpProductId:     opts.data["item_id"].(string),
		MpCategoryId:    opts.data["category_id"].(string),
		Name:            opts.data["item_name"].(string),
		Images:          opts.images,
		Description:     opts.data["description"].([]map[string]string),
		MarketplaceCode: constant.MarketplaceCodeShopee,
		Store:           opts.header["shop_id"].(string),
		Company:         opts.data["company"].(primitive.ObjectID),
		IsVisible:       true,
		IsMain:          true,
		Attributes:      opts.data["attributes"].([]schemas4.Attribute),
		Logistics:       opts.data["logistics"].([]schemas4.Logistic),
		Height:          opts.data["height"].(int),
		Width:           opts.data["width"].(int),
		Length:          opts.data["length"].(int),
		Weight:          opts.data["weight"].(int),
	}
	if opts.exist.Id == primitive.NilObjectID {
		productChildID, err := opts.productServ.InsertProductChildren(productChildSchema)
		if err != nil {
			cleanupErr := opts.productServ.DeleteProduct(opts.productId)
			if cleanupErr != nil {
				return fmt.Errorf("error inserting product children and cleanup failed: %w (cleanup: %w)", err, cleanupErr)
			}
			return fmt.Errorf("error inserting product children: %w", err)
		}

		productChildObjID, err := primitive.ObjectIDFromHex(productChildID)
		if err != nil {
			cleanupErr := cleanupResources(cleanupResourceOpts{
				productServ: opts.productServ,
				product:     opts.productId,
				startTime:   opts.startTime,
			})
			if cleanupErr != nil {
				return fmt.Errorf("error converting product child ID and cleanup failed: %w (cleanup: %w)", err, cleanupErr)
			}
			return fmt.Errorf("error converting product child ID: %w", err)
		}
		opts.exist = &schemas4.ProductChildrenSchema{Id: productChildObjID}
	} else {
		updatedProductChildren, err := util.CombineData(opts.exist, &productChildSchema)
		if err != nil {
			return fmt.Errorf("error combining product children data: %w", err)
		}

		_, err = opts.productServ.UpdateProductChildren(updatedProductChildren)
		if err != nil {
			return fmt.Errorf("error updating product children: %w", err)
		}
	}
	return nil
}

func proceedProductVariant(opts proceedProductVariantOpts) error {
	variantSchema := schemas4.ProductVariantSchema{
		Product:   opts.productId,
		Price:     opts.data["original_price"].(float64),
		DiscPrice: opts.data["current_price"].(float64),
		Image:     opts.images[0].Url,
	}

	if opts.exist.Id == primitive.NilObjectID {
		skuVariant, err := opts.productServ.GetSku("productvariant")
		if err != nil {
			cleanupErr := cleanupResources(cleanupResourceOpts{
				productServ:  opts.productServ,
				product:      opts.productId,
				productChild: opts.productChildrenId,
				startTime:    opts.startTime,
			})
			if cleanupErr != nil {
				return fmt.Errorf("error generating variant SKU and cleanup failed: %w (cleanup: %w)", err, cleanupErr)
			}
			return fmt.Errorf("error generating variant SKU: %w", err)
		}

		variantSchema.Sku = skuVariant

		productVariantID, err := opts.productServ.InsertProductVariant(variantSchema)
		if err != nil {
			cleanupErr := cleanupResources(cleanupResourceOpts{
				productServ:  opts.productServ,
				product:      opts.productId,
				productChild: opts.productChildrenId,
				startTime:    opts.startTime,
			})
			if cleanupErr != nil {
				return fmt.Errorf("error inserting product variant and cleanup failed: %w (cleanup: %w)", err, cleanupErr)
			}
			return fmt.Errorf("error inserting product variant: %w", err)
		}

		productVariantObjID, err := primitive.ObjectIDFromHex(productVariantID)
		if err != nil {
			cleanupErr := cleanupResources(cleanupResourceOpts{
				productServ:  opts.productServ,
				product:      opts.productId,
				productChild: opts.productChildrenId,
				startTime:    opts.startTime,
			})
			if cleanupErr != nil {
				return fmt.Errorf("error converting product variant ID and cleanup failed: %w (cleanup: %w)", err, cleanupErr)
			}
			return fmt.Errorf("error converting product variant ID: %w", err)
		}
		opts.exist.Id = productVariantObjID
		opts.exist.Sku = skuVariant
		_, err = opts.productServ.AppendProductChildren(opts.productId, productVariantObjID)
		if err != nil {
			return fmt.Errorf("error appending product variant: %w", err)
		}
	} else {
		updatedProductVariant, err := util.CombineData(opts.exist, &variantSchema)
		if err != nil {
			return fmt.Errorf("error combining product variant data: %w", err)
		}

		_, err = opts.productServ.UpdateProductVariant(updatedProductVariant)
		if err != nil {
			return fmt.Errorf("error updating product variant: %w", err)
		}
	}
	return nil
}

func proceedProductStock(opts proceedProductStockOpts) error {
	productStock, err := opts.productServ.FindProductStock(opts.sku)
	if err != nil {
		return fmt.Errorf("error finding product stock: %w", err)
	}

	stockSchema := schemas4.ProductStockSchema{
		Product:     opts.productId,
		Sku:         opts.sku,
		Batch:       "",
		ExpiredDate: 0,
		Quantity:    int32(opts.data["available_stock"].(int)),
		Reserved:    int32(opts.data["reversed_stock"].(int)),
		Company:     opts.companyId,
	}
	if productStock == nil {
		productStockID, err := opts.productServ.InsertProductStock(stockSchema)
		if err != nil {
			cleanupErr := cleanupResources(cleanupResourceOpts{
				productServ:    opts.productServ,
				product:        opts.productId,
				productChild:   opts.productChildrenId,
				productVariant: opts.productVariantId,
				startTime:      opts.startTime,
			})
			if cleanupErr != nil {
				return fmt.Errorf("error inserting product variant and cleanup failed: %w (cleanup: %w)", err, cleanupErr)
			}
			return fmt.Errorf("error inserting product variant: %w", err)
		}

		productStockObjID, err := primitive.ObjectIDFromHex(productStockID)
		if err != nil {
			cleanupErr := cleanupResources(cleanupResourceOpts{
				productServ:    opts.productServ,
				product:        opts.productId,
				productChild:   opts.productChildrenId,
				productVariant: opts.productVariantId,
				startTime:      opts.startTime,
			})
			if cleanupErr != nil {
				return fmt.Errorf("error converting product variant ID and cleanup failed: %w (cleanup: %w)", err, cleanupErr)
			}
			return fmt.Errorf("error converting product variant ID: %w", err)
		}
		opts.exist = &schemas4.ProductStockSchema{Id: productStockObjID}
	} else {
		updatedProductStock, err := util.CombineData(productStock, &stockSchema)
		if err != nil {
			return fmt.Errorf("error combining product stock data: %w", err)
		}

		*opts.exist = *productStock

		_, err = opts.productServ.UpdateProductStock(updatedProductStock)
		if err != nil {
			return fmt.Errorf("error updating product stock: %w", err)
		}
	}
	return nil
}

func proceedProductVariantChildren(opts proceedProductVariantChildrenOpts) error {
	tierVariations := make([]map[string]interface{}, 0)
	if exist, ok := opts.data["tier_variations"]; ok && exist != nil {
		switch exist.(type) {
		case []interface{}:
			parse, ok := exist.([]interface{})
			if !ok {
				return fmt.Errorf("%w: tier_variations", ErrFieldNotFound)
			}
			for _, item := range parse {
				mapData, ok := item.(map[string]interface{})
				if !ok {
					return fmt.Errorf("%w: tier_variations", ErrFieldNotFound)
				}
				tierVariations = append(tierVariations, mapData)
			}
		case []map[string]interface{}:
			parse, ok := exist.([]map[string]interface{})
			if !ok {
				return fmt.Errorf("%w: tier_variations", ErrFieldNotFound)
			}
			for _, item := range parse {
				tierVariations = append(tierVariations, item)
			}
		}
	}
	variantChildSchema := schemas4.ProductVariantChildrenSchema{
		ProductId:                opts.itemId,
		Sku:                      opts.sku,
		Store:                    opts.shopId,
		ProductVariantId:         opts.data["item_id"].(string),
		Url:                      fmt.Sprintf("product/%v/%v/", opts.shopId, opts.itemId),
		Price:                    opts.data["original_price"].(float64),
		DiscPrice:                opts.data["current_price"].(float64),
		VariantOptionCombination: tierVariations,
		MpSku:                    opts.data["sku"].(string),
		MarketplaceCode:          constant.MarketplaceCodeShopee,
		PreOrder: schemas4.PreOrder{
			IsPreOrder: opts.data["is_pre_order"].(bool),
			DaysToShip: opts.data["days_to_ship"].(int),
		},
	}

	if opts.exist.Id == primitive.NilObjectID {
		_, err := opts.productServ.InsertProductVariantChildren(variantChildSchema)
		if err != nil {
			cleanupErr := cleanupResources(cleanupResourceOpts{
				productServ:    opts.productServ,
				product:        opts.productId,
				productChild:   opts.productChildrenId,
				productVariant: opts.productVariantId,
				productStock:   opts.productStockId,
				startTime:      opts.startTime,
			})
			if cleanupErr != nil {
				return fmt.Errorf("error inserting product variant children and cleanup failed: %w (cleanup: %w)", err, cleanupErr)
			}
			return fmt.Errorf("error inserting product variant children: %w", err)
		}
	} else {
		updatedProductVariantChildren, err := util.CombineData(opts.exist, &variantChildSchema)
		if err != nil {
			return fmt.Errorf("error combining product variant children data: %w", err)
		}

		_, err = opts.productServ.UpdateProductVariantChildren(updatedProductVariantChildren)
		if err != nil {
			return fmt.Errorf("error updating product variant children: %w", err)
		}
	}
	return nil
}

func extractImages(data map[string]interface{}) ([]schemas4.ProductImage, error) {
	images := make([]schemas4.ProductImage, 0)

	parseImage, ok := data["image"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("error parsing image to string or not found, type: %T", data["image"])
	}

	ids, ok := parseImage["image_id_list"].(primitive.A)
	if !ok {
		return nil, fmt.Errorf("error parsing image_id_list to string or not found, type: %T", parseImage["image_id_list"])
	}

	urls, ok := parseImage["image_url_list"].(primitive.A)
	if !ok {
		return nil, fmt.Errorf("error parsing image_url_list to string or not found, type: %T", parseImage["image_url_list"])
	}

	// Process each image
	for i, id := range ids {
		if i >= len(urls) {
			break
		}

		url, ok := urls[i].(string)
		if !ok {
			return nil, fmt.Errorf("error parsing url map, type: %T", urls[i])
		}
		images = append(images, schemas4.ProductImage{
			MpImageId:   id.(string),
			Url:         url,
			MpProductId: strconv.Itoa(int(data["item_id"].(float64))),
		})
	}

	return images, nil
}

func extractAttributes(data map[string]interface{}) ([]schemas4.Attribute, error) {
	attributes := make([]schemas4.Attribute, 0)

	exist, ok := data["attribute_list"]

	if exist == nil {
		return nil, nil
	}

	var parseAttribute primitive.A
	if ok {
		parsed, ok := exist.(primitive.A)
		if !ok {
			return nil, fmt.Errorf("error parsing attribute to string or not found, type: %T", data["attribute_list"])
		}
		parseAttribute = parsed
	}

	// Process each attribute
	for _, item := range parseAttribute {
		parse, ok := item.(map[string]interface{})
		if !ok {
			return nil, fmt.Errorf("error parsing attribute to string or not found, type: %T", item)
		}

		attrList := make([]schemas4.AttributeList, 0)
		parseList, ok := parse["attribute_value_list"].(primitive.A)
		if !ok {
			return nil, fmt.Errorf("error parsing attribute_value_list, type: %T", parse["attribute_value_list"])
		}

		// Process each attribute value
		for _, list := range parseList {
			parsed, ok := list.(map[string]interface{})
			if !ok {
				return nil, fmt.Errorf("error parsing attribute value, type: %T", list)
			}

			attrList = append(attrList, schemas4.AttributeList{
				ValueId:           strconv.Itoa(int(parsed["value_id"].(float64))),
				OriginalValueName: parsed["original_value_name"].(string),
				ValueUnit:         parsed["value_unit"].(string),
			})
		}

		attributes = append(attributes, schemas4.Attribute{
			AttributeId:           strconv.Itoa(int(parse["attribute_id"].(float64))),
			OriginalAttributeName: parse["original_attribute_name"].(string),
			IsMandatory:           parse["is_mandatory"].(bool),
			Lists:                 attrList,
		})
	}

	return attributes, nil
}

func extractLogistics(data map[string]interface{}) ([]schemas4.Logistic, error) {
	logistic := make([]schemas4.Logistic, 0)

	logistics, ok := data["logistic_info"].(primitive.A)
	if !ok {
		return nil, fmt.Errorf("error parsing logistic_info to string or not found, type: %T", data["logistic_info"])
	}

	// Process each logistic option
	for _, data := range logistics {
		parsed, ok := data.(map[string]interface{})
		if !ok {
			return nil, fmt.Errorf("error parsing logistic_info to string or not found, type: %T", data)
		}

		logistic = append(logistic, schemas4.Logistic{
			LogisticId: strconv.Itoa(int(parsed["logistic_id"].(float64))),
			Enabled:    parsed["enabled"].(bool),
			IsFree:     parsed["is_free"].(bool),
			UniqueId:   fmt.Sprintf("%v-%s", int(parsed["logistic_id"].(float64)), constant.MarketplaceCodeShopee),
		})
	}

	return logistic, nil
}

func cleanupResources(opts cleanupResourceOpts) error {
	var errs []error

	now := time.Now()

	if !opts.productStock.IsZero() {
		if err := opts.productServ.DeleteProductStock(opts.productStock, []time.Time{opts.startTime, now}); err != nil {
			errs = append(errs, fmt.Errorf("failed to delete product stock: %w", err))
		}
	}

	if !opts.productVariant.IsZero() {
		if err := opts.productServ.DeleteProductVariant(opts.productVariant, []time.Time{opts.startTime, now}); err != nil {
			errs = append(errs, fmt.Errorf("failed to delete product variant: %w", err))
		}
	}

	if !opts.productChild.IsZero() {
		if err := opts.productServ.DeleteProductChildren(opts.productChild, []time.Time{opts.startTime, now}); err != nil {
			errs = append(errs, fmt.Errorf("failed to delete product children: %w", err))
		}
	}

	if !opts.product.IsZero() {
		if err := opts.productServ.DeleteProduct(opts.product); err != nil {
			errs = append(errs, fmt.Errorf("failed to delete product: %w", err))
		}
	}

	if len(errs) > 0 {
		return fmt.Errorf("cleanup errors: %v", errs)
	}

	return nil
}
