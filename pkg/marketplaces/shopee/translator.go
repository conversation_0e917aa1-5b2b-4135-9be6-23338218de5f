package shopee

import (
	"errors"
	"fmt"
	"log"
	"strconv"
	"strings"

	"abs-scheduler-go/internal/orders/schemas"
	"abs-scheduler-go/pkg/constant"
	"abs-scheduler-go/pkg/util"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func TranslateItems(opts TranslateItemOpts) error {
	variantName, ok := opts.Item["model_name"].(string)
	if !ok {
		variantName = ""
	}
	variantId, ok := opts.Item["model_id"].(float64)
	if !ok {
		variantId = 0.0
	}
	variantSku, ok := opts.Item["model_sku"].(string)
	if !ok {
		variantSku = ""
	}
	itemName, ok := opts.Item["item_name"].(string)
	if !ok {
		itemName = ""
	}
	itemId, ok := opts.Item["item_id"].(float64)
	if !ok {
		itemId = 0.0
	}
	itemSku, ok := opts.Item["item_sku"].(string)
	if !ok {
		itemSku = ""
	}
	qty, ok := opts.Item["model_quantity_purchased"].(float64)
	if !ok {
		qty = 0
	}
	promotionId, ok := opts.Item["promotion_id"].(float64)
	if !ok {
		promotionId = 0.0
	}
	promotionType, ok := opts.Item["promotion_type"].(string)
	if !ok {
		promotionType = ""
	}
	promotionGroupId, ok := opts.Item["promotion_group_id"].(float64)
	if !ok {
		promotionGroupId = 0.0
	}
	addOnDeal, ok := opts.Item["add_on_deal"].(string)
	if !ok {
		addOnDeal = ""
	}
	addOnDealId, ok := opts.Item["add_on_deal_id"].(float64)
	if !ok {
		addOnDealId = 0.0
	}
	weight, ok := opts.Item["weight"].(float64)
	if !ok {
		weight = 0
	}
	image, ok := opts.Item["image_info"].(map[string]interface{})
	if !ok {
		image = map[string]interface{}{}
	}

	imgUrls := []string{image["image_url"].(string)}

	var id string
	if variantId != 0 {
		id = strconv.FormatFloat(variantId, 'f', 0, 64)
	} else {
		id = strconv.FormatFloat(itemId, 'f', 0, 64)
	}

	if promotionId != 0.0 {
		id += "_" + strconv.FormatFloat(promotionId, 'f', 0, 64)
	}

	escrowItem, ok := opts.Escrow[id].(map[string]interface{})
	if !ok {
		escrowItem = map[string]interface{}{}
	}
	shopeeDisc, ok := escrowItem["shopee_discount"].(float64)
	if !ok {
		shopeeDisc = 0
	}
	sellerDisc, ok := escrowItem["seller_discount"].(float64)
	if !ok {
		sellerDisc = 0
	}
	sellerVoucherDisc, ok := escrowItem["discount_from_voucher_seller"].(float64)
	if !ok {
		sellerVoucherDisc = 0
	}
	coinDiscMp, ok := escrowItem["discount_from_coin"].(float64)
	if !ok {
		coinDiscMp = 0
	}
	voucherDiscMp, ok := escrowItem["discount_from_voucher_shopee"].(float64)
	if !ok {
		voucherDiscMp = 0
	}
	grandTotal, ok := escrowItem["discounted_price"].(float64)
	if !ok {
		grandTotal = 0
	}
	subTotal, ok := escrowItem["selling_price"].(float64)
	if !ok {
		subTotal = 0
	}

	grandTotal -= sellerVoucherDisc

	*opts.TotalWeight += weight
	(*opts.Pricing)["subTotal"] += float32(subTotal)
	(*opts.Pricing)["grandTotal"] += float32(grandTotal)

	id = strings.Split(id, "_")[0]

	find, err := opts.ProductService.FindProductVariantChildren(id, constant.MarketplaceCodeShopee)
	if err != nil {
		log.Printf("error finding product variant children: %v", err)
		return fmt.Errorf("error finding product variant children: %v", err)
	}

	if find == nil {
		log.Printf("product variant children %v not found", id)
		return fmt.Errorf("product variant children %v tidak ditemukan", id)
	}

	newData := schemas.OrderItems{
		ItemName:              itemName,
		ItemId:                find.Id.Hex(),
		MpId:                  strconv.FormatFloat(itemId, 'f', -1, 64),
		ItemSku:               find.Sku,
		MpSku:                 itemSku,
		VariantName:           variantName,
		VariantId:             strconv.FormatFloat(variantId, 'f', -1, 64),
		VariantSku:            variantSku,
		Quantity:              uint16(qty),
		OriginalPrice:         float32(subTotal),
		GrandTotal:            float32(grandTotal),
		SellerDiscount:        float32(sellerDisc),
		SellerVoucherDiscount: float32(sellerVoucherDisc),
		MarketplaceDiscount:   float32(voucherDiscMp),
		CoinRebate:            float32(coinDiscMp),
		Weight:                util.RoundTo2DecimalPlaces(weight),
		Promotion: schemas.OrderPromotion{
			PromotionId: strconv.FormatFloat(promotionId, 'f', -1, 64),
			Type:        promotionType,
			GroupId:     strconv.FormatFloat(promotionGroupId, 'f', -1, 64),
			AddonDeal:   addOnDeal,
			AddonDealId: strconv.FormatFloat(addOnDealId, 'f', -1, 64),
		},
		PackageStatus:                "",
		Tax:                          0,
		IsGift:                       false,
		ImageUrl:                     imgUrls,
		SellerDiscountBreakdown:      make([]schemas.DiscountItemBreakdown, 0),
		MarketplaceDiscountBreakdown: make([]schemas.DiscountItemBreakdown, 0),
	}

	for _, discount := range opts.DiscBreakdown {
		if discount.IsSellerDiscount {
			if sellerVoucherDisc > 0 && discount.DiscountType == schemas.DiscountVoucher {
				newData.SellerDiscountBreakdown = append(newData.SellerDiscountBreakdown, schemas.DiscountItemBreakdown{
					DiscountType: schemas.DiscountBreakdownVoucher,
					DiscountCode: discount.Code,
					Amount:       float32(sellerVoucherDisc),
				})
			}
			if sellerDisc > 0 && discount.DiscountType == schemas.DiscountSeller {
				newData.SellerDiscountBreakdown = append(newData.SellerDiscountBreakdown, schemas.DiscountItemBreakdown{
					DiscountType: schemas.DiscountBreakdownPromo,
					DiscountCode: discount.Code,
					Amount:       float32(sellerDisc),
				})
			}
		}
		if discount.IsPlatformSubsidy {
			if voucherDiscMp > 0 && discount.DiscountType == schemas.DiscountVoucher {
				newData.MarketplaceDiscountBreakdown = append(newData.MarketplaceDiscountBreakdown, schemas.DiscountItemBreakdown{
					DiscountType: schemas.DiscountBreakdownVoucher,
					DiscountCode: discount.Code,
					Amount:       float32(voucherDiscMp),
				})
			}
			if shopeeDisc > 0 && discount.DiscountType == schemas.DiscountPlatform {
				newData.MarketplaceDiscountBreakdown = append(newData.MarketplaceDiscountBreakdown, schemas.DiscountItemBreakdown{
					DiscountType: schemas.DiscountBreakdownPromo,
					DiscountCode: discount.Code,
					Amount:       float32(shopeeDisc),
				})
			}
			if coinDiscMp > 0 && discount.DiscountType == schemas.DiscountPlatform {
				newData.MarketplaceDiscountBreakdown = append(newData.MarketplaceDiscountBreakdown, schemas.DiscountItemBreakdown{
					DiscountType: schemas.DiscountBreakdownPromo,
					DiscountCode: "coin",
					Amount:       float32(coinDiscMp),
				})
			}
		}
	}

	*opts.OrderItems = append(*opts.OrderItems, newData)

	return nil
}

func MapEscrowItems(opts MapEscrowItemsOpts) error {
	var escrowItem []interface{}

	switch v := opts.Escrow["items"].(type) {
	case primitive.A:
		escrowItem = v[:]
	case []interface{}:
		escrowItem = v
	default:
		return errors.New("field 'items' on escrow is not a valid JSON array")
	}

	for _, item := range escrowItem {
		item, ok := item.(map[string]interface{})
		if !ok {
			return errors.New("field 'escrow items' not found or not a JSON object")
		}

		itemId, ok := item["item_id"].(float64)
		if !ok {
			itemId = 0.0
		}
		variantId, ok := item["model_id"].(float64)
		if !ok {
			variantId = 0.0
		}
		activityId, ok := item["activity_id"].(float64)
		if !ok {
			activityId = 0.0
		}

		var id string
		if variantId != 0 {
			id = strconv.FormatFloat(variantId, 'f', 0, 64)
		} else {
			id = strconv.FormatFloat(itemId, 'f', 0, 64)
		}

		if activityId != 0.0 {
			id += "_" + strconv.FormatFloat(activityId, 'f', 0, 64)
		}

		(*opts.Result)[id] = item
	}

	return nil
}

func parsePricingAndSetBreakdown(opts parsePricingAndSetBreakdownOpts) error {
	commissionFee := 0.0
	if parse, ok := opts.orderIncome["commission_fee"]; ok && parse != nil {
		commissionFee = parse.(float64)
	} else {
		log.Println("field 'commission_fee' is nil or missing, using default (0.0)")
	}

	serviceFee := 0.0
	if parse, ok := opts.orderIncome["service_fee"]; ok && parse != nil {
		serviceFee = parse.(float64)
	} else {
		log.Println("field 'service_fee' is nil or missing, using default (0.0)")
	}

	sellerTransactionFee := 0.0
	if parse, ok := opts.orderIncome["seller_transaction_fee"]; ok && parse != nil {
		sellerTransactionFee = parse.(float64)
	} else {
		log.Println("field 'seller_transaction_fee' is nil or missing, using default (0.0)")
	}

	sellerDiscount := 0.0
	if parse, ok := opts.orderIncome["seller_discount"]; ok && parse != nil {
		sellerDiscount = parse.(float64)
	} else {
		log.Println("field 'seller_discount' is nil or missing, using default (0.0)")
	}

	originalPrice := 0.0
	if parse, ok := opts.orderIncome["original_price"]; ok && parse != nil {
		originalPrice = parse.(float64)
	} else {
		log.Println("field 'original_price' is nil or missing, using default (0.0)")
	}

	sellerVoucherDisc := 0.0
	if parse, ok := opts.orderIncome["voucher_from_seller"]; ok && parse != nil {
		sellerVoucherDisc = parse.(float64)
	} else {
		log.Println("field 'voucher_from_seller' is nil or missing, using default (0.0)")
	}

	shopeeDisc := 0.0
	if parse, ok := opts.orderIncome["shopee_discount"]; ok && parse != nil {
		shopeeDisc = parse.(float64)
	} else {
		log.Println("field 'shopee_discount' is nil or missing, using default (0.0)")
	}

	shopeeVoucherDisc := 0.0
	if parse, ok := opts.orderIncome["voucher_from_shopee"]; ok && parse != nil {
		shopeeVoucherDisc = parse.(float64)
	} else {
		log.Println("field 'voucher_from_shopee' is nil or missing, using default (0.0)")
	}

	shopeeCoinDisc := 0.0
	if parse, ok := opts.orderIncome["coins"]; ok && parse != nil {
		shopeeCoinDisc = parse.(float64)
	} else {
		log.Println("field 'coins' is nil or missing, using default (0.0)")
	}

	campaignFee := 0.0
	if parse, ok := opts.orderIncome["campaign_fee"]; ok && parse != nil {
		campaignFee = parse.(float64)
	} else {
		log.Println("field 'campaign_fee' is nil or missing, using default (0.0)")
	}

	estimatedShippingFee := 0.0
	if parse, ok := opts.orderIncome["estimated_shipping_fee"]; ok && parse != nil {
		estimatedShippingFee = parse.(float64)
	} else {
		log.Println("field 'estimated_shipping_fee' is nil or missing, using default (0.0)")
	}

	actualShippingFee := 0.0
	if parse, ok := opts.orderIncome["actual_shipping_fee"]; ok && parse != nil {
		actualShippingFee = parse.(float64)
	} else {
		log.Println("field 'actual_shipping_fee' is nil or missing, using default (0.0)")
	}

	buyerPaidShipping := 0.0
	if parse, ok := opts.orderIncome["buyer_paid_shipping_fee"]; ok && parse != nil {
		buyerPaidShipping = parse.(float64)
	} else {
		log.Println("field 'buyer_paid_shipping_fee' is nil or missing, using default (0.0)")
	}

	shippingDisc3Pl := 0.0
	if parse, ok := opts.orderIncome["shipping_fee_discount_from_3pl"]; ok && parse != nil {
		shippingDisc3Pl = parse.(float64)
	} else {
		log.Println("field 'shipping_fee_discount_from_3pl' is nil or missing, using default (0.0)")
	}

	shippingDiscSeller := 0.0
	if parse, ok := opts.orderIncome["seller_shipping_discount"]; ok && parse != nil {
		shippingDiscSeller = parse.(float64)
	} else {
		log.Println("field 'seller_shipping_discount' is nil or missing, using default (0.0)")
	}

	shippingDiscShopee := 0.0
	if parse, ok := opts.orderIncome["shopee_shipping_rebate"]; ok && parse != nil {
		shippingDiscShopee = parse.(float64)
	} else {
		log.Println("field 'shopee_shipping_rebate' is nil or missing, using default (0.0)")
	}

	voucherCode := make([]string, 0)
	if parse, ok := opts.orderIncome["seller_voucher_code"]; ok && parse != nil {
		toArr := make([]interface{}, 0)
		switch v := parse.(type) {
		case primitive.A:
			toArr = v[:]
		case []interface{}:
			toArr = v
		default:
			return errors.New("field 'items' on escrow is not a valid JSON array")
		}
		for _, v := range toArr {
			voucherCode = append(voucherCode, v.(string))
		}
	} else {
		log.Println("field 'seller_voucher_code' is nil or missing, using default (0.0)")
	}

	*opts.result = map[string]float32{
		"commissionFee":        float32(commissionFee),
		"serviceFee":           float32(serviceFee),
		"sellerTransactionFee": float32(sellerTransactionFee),
		"sellerDiscount":       float32(sellerDiscount),
		"originalPrice":        float32(originalPrice),
		"estimatedShippingFee": float32(estimatedShippingFee),
		"actualShippingFee":    float32(actualShippingFee),
		"sellerVoucherDisc":    float32(sellerVoucherDisc),
		"shippingFeeDisc":      float32(actualShippingFee - buyerPaidShipping),
		"shopeeDisc":           float32(shopeeDisc),
		"shopeeVoucherDisc":    float32(shopeeVoucherDisc),
		"grandTotal":           float32(0),
		"subTotal":             float32(0),
	}

	if commissionFee != 0 {
		*opts.feeBreakdown = append(*opts.feeBreakdown, schemas.OrderFee{
			FeeType:      schemas.FeePlatform,
			FeeName:      "Commission Fee",
			Amount:       float32(commissionFee),
			Currency:     "Rp",
			IsBuyerPaid:  false,
			IsSellerPaid: true,
		})
	}
	if serviceFee != 0 {
		*opts.feeBreakdown = append(*opts.feeBreakdown, schemas.OrderFee{
			FeeType:      schemas.FeePlatform,
			FeeName:      "Service Fee",
			Amount:       float32(serviceFee),
			Currency:     "Rp",
			IsBuyerPaid:  false,
			IsSellerPaid: true,
		})
	}
	if sellerTransactionFee != 0 {
		*opts.feeBreakdown = append(*opts.feeBreakdown, schemas.OrderFee{
			FeeType:      schemas.FeePlatform,
			FeeName:      "Seller Transaction Fee",
			Amount:       float32(sellerTransactionFee),
			Currency:     "Rp",
			IsBuyerPaid:  false,
			IsSellerPaid: true,
		})
	}
	if campaignFee != 0 {
		*opts.feeBreakdown = append(*opts.feeBreakdown, schemas.OrderFee{
			FeeType:      schemas.FeePlatform,
			FeeName:      "Campaign Fee",
			Amount:       float32(campaignFee),
			Currency:     "Rp",
			IsBuyerPaid:  false,
			IsSellerPaid: true,
		})
	}
	if sellerDiscount != 0 {
		*opts.discBreakdown = append(*opts.discBreakdown, schemas.OrderDiscount{
			DiscountType:      schemas.DiscountSeller,
			DiscountName:      "Seller Discount",
			Amount:            float32(sellerDiscount),
			Currency:          "Rp",
			IsPlatformSubsidy: false,
			IsSellerDiscount:  true,
		})
	}
	if sellerVoucherDisc != 0 {
		*opts.discBreakdown = append(*opts.discBreakdown, schemas.OrderDiscount{
			DiscountType:      schemas.DiscountVoucher,
			DiscountName:      "Seller Voucher Discount",
			Code:              strings.Join(voucherCode, ","),
			Amount:            float32(sellerVoucherDisc),
			Currency:          "Rp",
			IsPlatformSubsidy: false,
			IsSellerDiscount:  true,
		})
	}
	if shopeeDisc != 0 {
		*opts.discBreakdown = append(*opts.discBreakdown, schemas.OrderDiscount{
			DiscountType:      schemas.DiscountPlatform,
			DiscountName:      "Shopee Discount",
			Amount:            float32(shopeeDisc),
			Currency:          "Rp",
			IsPlatformSubsidy: true,
			IsSellerDiscount:  false,
		})
	}
	if shopeeVoucherDisc != 0 {
		*opts.discBreakdown = append(*opts.discBreakdown, schemas.OrderDiscount{
			DiscountType:      schemas.DiscountVoucher,
			DiscountName:      "Shopee Voucher Discount",
			Amount:            float32(shopeeVoucherDisc),
			Currency:          "Rp",
			IsPlatformSubsidy: true,
			IsSellerDiscount:  false,
		})
	}
	if shippingDisc3Pl != 0 {
		*opts.discBreakdown = append(*opts.discBreakdown, schemas.OrderDiscount{
			DiscountType:      schemas.DiscountPlatform,
			DiscountName:      "3PL Shipping Discount",
			Amount:            float32(shippingDisc3Pl),
			Currency:          "Rp",
			IsPlatformSubsidy: true,
			IsSellerDiscount:  false,
		})
	}
	if shippingDiscSeller != 0 {
		*opts.discBreakdown = append(*opts.discBreakdown, schemas.OrderDiscount{
			DiscountType:      schemas.DiscountSeller,
			DiscountName:      "Seller Shipping Discount",
			Amount:            float32(shippingDiscSeller),
			Currency:          "Rp",
			IsPlatformSubsidy: false,
			IsSellerDiscount:  true,
		})
	}
	if shippingDiscShopee != 0 {
		*opts.discBreakdown = append(*opts.discBreakdown, schemas.OrderDiscount{
			DiscountType:      schemas.DiscountPlatform,
			DiscountName:      "Shopee Shipping Discount",
			Amount:            float32(shippingDiscShopee),
			Currency:          "Rp",
			IsPlatformSubsidy: true,
			IsSellerDiscount:  false,
		})
	}
	if shopeeCoinDisc != 0 {
		*opts.discBreakdown = append(*opts.discBreakdown, schemas.OrderDiscount{
			DiscountType:      schemas.DiscountPlatform,
			DiscountName:      "Shopee Coin Discount",
			Amount:            float32(shopeeCoinDisc),
			Currency:          "Rp",
			IsPlatformSubsidy: true,
			IsSellerDiscount:  false,
		})
	}

	return nil
}

func parseShipping(data map[string]interface{}) map[string]interface{} {
	logisticChannelId := ""
	if parse, ok := data["logistics_channel_id"]; ok && parse != nil {
		logisticChannelId = strconv.FormatFloat(parse.(float64), 'f', -1, 64)
	} else {
		log.Println("field 'logistics_channel_id' is nil or missing, using default (0.0)")
	}

	shippingCarrier := ""
	if parse, ok := data["shipping_carrier"]; ok && parse != nil {
		shippingCarrier = parse.(string)
	} else {
		log.Println("field 'shipping_carrier' is nil or missing, using default (0.0)")
	}
	packageNumber := ""
	pickupCode := ""

	if parse, ok := data["package_number"]; ok && parse != nil {
		packageNumber = parse.(string)
	} else {
		log.Println("field 'package_number' is nil or missing, using default (0.0)")
	}

	pickupTime := 0.0
	deliveredTime := 0.0
	if parse, ok := data["tracking_data"]; ok && parse != nil {
		trackingData := parse.(map[string]interface{})

		if check, ok := trackingData["tracking_number"]; ok && check != nil {
			packageNumber = trackingData["tracking_number"].(string)
		}

		if parse, ok := trackingData["pickup_code"]; ok && parse != nil {
			pickupCode = parse.(string)
		} else {
			log.Println("field 'pickup_code' is nil or missing, using default (0.0)")
		}

		if trackingInfo, ok := trackingData["tracking_info"]; ok && trackingInfo != nil {
			tracking := trackingInfo.([]interface{})

			for _, item := range tracking {
				parsing, ok := item.(map[string]interface{})
				if !ok {
					println("field 'tracking_info' is nil or missing, using default (0.0)")
				}

				if constant.ShopeeTrackingStatusEnum(parsing["logistics_status"].(string)) == constant.ShopeeTrackingPickup {
					if pickupTime != 0 {
						continue
					}
					pickupTime = parsing["update_time"].(float64)
				}
				if constant.ShopeeTrackingStatusEnum(parsing["logistics_status"].(string)) == constant.ShopeeTrackingDelivered {
					if deliveredTime != 0 {
						continue
					}
					deliveredTime = parsing["update_time"].(float64)
				}
			}
		}
	}
	parcelChargeableWeight := ""
	if parse, ok := data["parcel_chargeable_weight"]; ok && parse != nil {
		parcelChargeableWeight = parse.(string)
	} else {
		log.Println("field 'parcel_chargeable_weight' is nil or missing, using default (0.0)")
	}
	logisticsStatus := ""
	if parse, ok := data["logistics_status"]; ok && parse != nil {
		logisticsStatus = parse.(string)
	} else {
		log.Println("field 'logistics_status' is nil or missing, using default (0.0)")
	}

	return map[string]interface{}{
		"logisticChannelId":      logisticChannelId,
		"shippingCarrier":        shippingCarrier,
		"packageNumber":          packageNumber,
		"pickupCode":             pickupCode,
		"parcelChargeableWeight": parcelChargeableWeight,
		"logisticsStatus":        logisticsStatus,
		"pickupAt":               pickupTime,
		"deliveredAt":            deliveredTime,
	}
}

func parseRecipient(data map[string]interface{}) map[string]string {
	name := ""
	if parse, ok := data["name"]; ok && parse != nil {
		name = parse.(string)
	} else {
		log.Println("field 'name' is nil or missing, using default (0.0)")
	}
	phone := ""
	if parse, ok := data["phone"]; ok && parse != nil {
		phone = parse.(string)
	} else {
		log.Println("field 'phone' is nil or missing, using default (0.0)")
	}
	region := ""
	if parse, ok := data["region"]; ok && parse != nil {
		region = parse.(string)
	} else {
		log.Println("field 'region' is nil or missing, using default (0.0)")
	}
	city := ""
	if parse, ok := data["city"]; ok && parse != nil {
		city = parse.(string)
	} else {
		log.Println("field 'city' is nil or missing, using default (0.0)")
	}
	district := ""
	if parse, ok := data["district"]; ok && parse != nil {
		district = parse.(string)
	} else {
		log.Println("field 'district' is nil or missing, using default (0.0)")
	}
	zipcode := ""
	if parse, ok := data["zipcode"]; ok && parse != nil {
		zipcode = parse.(string)
	} else {
		log.Println("field 'zipcode' is nil or missing, using default (0.0)")
	}
	fullAddress := ""
	if parse, ok := data["full_address"]; ok && parse != nil {
		fullAddress = parse.(string)
	} else {
		log.Println("field 'full_address' is nil or missing, using default (0.0)")
	}

	return map[string]string{
		"name":        name,
		"phone":       phone,
		"region":      region,
		"city":        city,
		"district":    district,
		"zipcode":     zipcode,
		"fullAddress": fullAddress,
	}
}
