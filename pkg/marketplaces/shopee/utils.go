package shopee

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"abs-scheduler-go/pkg/config"
	"abs-scheduler-go/pkg/constant"
	"abs-scheduler-go/pkg/queue"
	"abs-scheduler-go/pkg/util"
)

func wrapWithRefresh(ctx context.Context, opts wrapWithRefreshOpts) (map[string]interface{}, error) {
	body, statusCode, err := sendRequest(ctx, *opts.SendOpts)

	errorStatusCode := statusCode == http.StatusUnauthorized || statusCode == http.StatusForbidden

	if opts.hasRetry && errorStatusCode {
		return nil, err
	}

	if errorStatusCode {
		access, refresh, err := refreshToken(ctx, opts.RefreshOpt)
		if err != nil {
			return map[string]interface{}{}, err
		}

		baseUrl, partnerId, partner<PERSON><PERSON>, _, err := getBaseURLAndPartnerId()
		timestamp := generateTimestamp()
		shopId := opts.SendOpts.Params["shop_id"].(string)
		path := strings.Replace(opts.SendOpts.Url, baseUrl, "", -1)

		sign, err := generateSign(partnerKey, partnerId+path+timestamp+access+shopId)
		if err != nil {
			return nil, fmt.Errorf("failed to generate sign: %w", err)
		}

		opts.SendOpts.Params["sign"] = sign
		opts.SendOpts.Params["timestamp"] = timestamp
		opts.SendOpts.Params["access_token"] = access
		opts.SendOpts.Params["refresh_token"] = refresh

		opts.hasRetry = true
		res, err := wrapWithRefresh(ctx, opts)
		if err != nil {
			return nil, err
		}

		return res, err
	}

	if err != nil {
		return map[string]interface{}{}, err
	}

	return body, nil
}

func refreshToken(ctx context.Context, opts RefreshTokenOpts) (string, string, error) {
	path := "/api/v2/auth/access_token/get"
	timestamp := generateTimestamp()
	sign, err := generateSign(opts.PartnerKey, opts.PartnerId+path+timestamp)
	if err != nil {
		return "", "", err
	}

	partnerId, err := strconv.Atoi(opts.PartnerId)
	if err != nil {
		return "", "", err
	}
	shopId, err := strconv.Atoi(opts.ShopId)
	if err != nil {
		return "", "", err
	}

	resBody, _, err := sendRequest(ctx, util.SendRequestOpts{
		Url:    opts.BaseUrl + path,
		Method: http.MethodPost,
		Params: map[string]interface{}{
			"sign":       sign,
			"timestamp":  timestamp,
			"partner_id": partnerId,
		},
		Body: map[string]interface{}{
			"partner_id":    partnerId,
			"refresh_token": opts.Refresh,
			"shop_id":       shopId,
		},
		Headers: nil,
	})

	if err != nil {
		log.Printf("error while refresh token: %v", err)
		return "", "", err
	}

	expire, ok := resBody["expire_in"].(float64)
	if !ok {
		return "", "", fmt.Errorf("invalid type for expire_in")
	}

	_, err = opts.Service.UpdateToken(struct {
		Id         string
		Access     string
		Refresh    string
		ExpAccess  time.Time
		ExpRefresh time.Time
	}{
		Id: opts.ShopId, Access: resBody["access_token"].(string),
		Refresh:    resBody["refresh_token"].(string),
		ExpAccess:  time.Now().Add(time.Duration(expire) * time.Second),
		ExpRefresh: time.Now().Add(30 * 24 * time.Hour),
	})
	if err != nil {
		return "", "", err
	}

	return resBody["access_token"].(string), resBody["refresh_token"].(string), nil
}

func generateSign(partnerKey string, data string) (string, error) {
	h := hmac.New(sha256.New, []byte(partnerKey))
	h.Write([]byte(data))

	return hex.EncodeToString(h.Sum(nil)), nil
}

func generateTimestamp() string {
	return strconv.FormatInt(time.Now().Unix(), 10)
}

func DecryptTokens(params map[string]interface{}) (string, string, error) {
	key := config.CredentialKey

	decryptAccess, err := util.DecryptValue(params["access_token"].(string), key)
	if err != nil {
		log.Println("error while decrypt access token: ", err.Error())
		return "", "", err
	}
	decryptRefresh, err := util.DecryptValue(params["refresh_token"].(string), key)
	if err != nil {
		log.Println("error while decrypt refresh token: ", err.Error())
		return "", "", err
	}

	return decryptAccess, decryptRefresh, nil
}

func sendRequest(ctx context.Context, opts util.SendRequestOpts) (map[string]interface{}, int, error) {
	res, err := util.SendRequest(ctx, opts)

	if err != nil {
		statusCode := http.StatusInternalServerError
		if res != nil && res.StatusCode != 0 {
			statusCode = res.StatusCode
		}
		return map[string]interface{}{}, statusCode, err
	}

	defer res.Body.Close()

	var body map[string]interface{}
	err = json.NewDecoder(res.Body).Decode(&body)
	if err != nil {
		return nil, res.StatusCode, err
	}

	if errMsg, ok := body["error"].(string); ok && errMsg != "" {
		return nil, res.StatusCode, fmt.Errorf("error_code: %s, message: %s", body["error"], body["message"])
	}

	return body, res.StatusCode, nil
}

func getBaseURLAndPartnerId() (baseUrl string, partnerId string, partnerKey string, location *time.Location, err error) {
	location, err = time.LoadLocation("Asia/Jakarta")
	if err != nil {
		return "", "", "", nil, err
	}

	return config.ShopeeBaseUrl, config.ShopeePartnerId, config.ShopeePartnerKey, location, nil
}

func incrementQueueJobCount(opts incrementQueueJobOpts) {
	key := "scheduler-" + opts.data.SchedulerTask.Hex() + "-batch-" + opts.data.Batch
	total := 0

	for _, item := range opts.insertedIds {
		total += len(item)
	}

	err := opts.redis.IncrBy(context.Background(), key, int64(total)).Err()
	if err != nil {
		log.Printf("error while add job count job %v: %v", opts.data, err.Error())
	}
}

func queueJobs(opts *getListOpts) error {
	if len((*opts.result)[opts.key+"_detail"]) > 0 {
		for _, id := range (*opts.result)[opts.key+"_detail"] {
			err := opts.queueServ.AddJob(queue.JobStoreKey, queue.Job{
				Key:      "scheduler:task_execution",
				Priority: int(opts.data.Priority),
				Value: queue.JobValue{
					Event:     queue.EventWaiting,
					Id:        id,
					PrevEvent: "",
					Name:      fmt.Sprintf("fetch_%s_detail", opts.key),
					Platform:  constant.MarketplaceCodeShopee,
					MaxRetry:  int(opts.data.MaxRetries),
				},
			})
			if err != nil {
				return fmt.Errorf("failed to add job to queue %s_detail: %w", opts.key, err)
			}
		}
	}
	if len((*opts.result)[opts.key+"_list"]) > 0 {
		for _, id := range (*opts.result)[opts.key+"_list"] {
			err := opts.queueServ.AddJob(queue.JobStoreKey, queue.Job{
				Key:      "scheduler:task_execution",
				Priority: int(opts.data.Priority),
				Value: queue.JobValue{
					Event:     queue.EventWaiting,
					Id:        id,
					PrevEvent: "",
					Name:      fmt.Sprintf("fetch_%s_list", opts.key),
					Platform:  constant.MarketplaceCodeShopee,
					MaxRetry:  int(opts.data.MaxRetries),
				},
			})
			if err != nil {
				return fmt.Errorf("failed to add job to queue %s_list: %w", opts.key, err)
			}
		}
	}
	return nil
}
