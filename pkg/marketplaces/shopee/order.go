package shopee

import (
	"context"
	"errors"
	"fmt"
	"log"
	"math"
	"strconv"
	"strings"
	"sync"
	"time"

	"abs-scheduler-go/internal/orders/schemas"
	schemas2 "abs-scheduler-go/internal/scheduler_tasks/schemas"
	schemas3 "abs-scheduler-go/internal/task_executions/schemas"
	"abs-scheduler-go/pkg/config"
	"abs-scheduler-go/pkg/constant"
	"abs-scheduler-go/pkg/queue"
	"abs-scheduler-go/pkg/util"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

func GenerateOrderTaskList(payload FetchOrderListOpts) (string, error) {
	baseUrl, partnerId, _, location, err := getBaseURLAndPartnerId()
	if err != nil {
		return "", err
	}

	path := "/api/v2/order/get_order_list"

	now := time.Now().In(location)
	year, month, day := now.Date()

	insert, err := payload.Service.Insert(schemas3.TaskExecution{
		SchedulerTask:   payload.Schedule.ID,
		Batch:           payload.Schedule.LastBatch,
		Status:          schemas3.TaskExecutionStatusWaiting,
		Trigger:         schemas3.TaskExecutionTriggerCron,
		Operation:       schemas2.SchedulerTaskOperationFetchOrderList,
		MarketplaceCode: payload.Store.MarketplaceCode,
		StartTime:       primitive.NewDateTimeFromTime(time.Now()),
		EndTime:         primitive.DateTime(0),
		RetryCount:      0,
		MaxRetries:      payload.Schedule.MaxRetries,
		Company:         payload.Store.Company,
		Store:           payload.Store.ID,
		RequestParam: map[string]interface{}{
			"partner_id":       partnerId,
			"shop_id":          payload.Store.MarketplaceId,
			"time_range_field": "update_time",
			"time_from":        strconv.FormatInt(time.Date(year, month, day, 0, 0, 0, 0, now.Location()).Unix(), 10),
			"time_to":          strconv.FormatInt(time.Date(year, month, day, 23, 59, 59, 999, now.Location()).Unix(), 10),
			"page_size":        100,
		},
		RequestHeader: map[string]interface{}{
			"Content-Type": "application/json",
		},
		RequestBody:   nil,
		RequestUrl:    baseUrl + path,
		RequestMethod: string(constant.HttpMethodGet),
	})
	if err != nil {
		return "", err
	}

	return insert, nil
}

func GetOrderList(opts getListOpts) error {
	// initialize required params
	baseUrl, partnerId, partnerKey, _, err := getBaseURLAndPartnerId()
	if err != nil {
		return fmt.Errorf("failed to get base URL and partner ID: %w", err)
	}

	tokens, err := getToken(getTokenOpts{
		storeService: opts.storeService,
		Data:         &opts.data,
	})
	if err != nil {
		return fmt.Errorf("failed to get tokens: %w", err)
	}

	// prepare request params
	path := strings.Replace(opts.data.RequestUrl, baseUrl, "", -1)
	timestamp := generateTimestamp()
	shopId := opts.data.RequestParam["shop_id"].(string)
	sign, err := generateSign(partnerKey, partnerId+path+timestamp+tokens["access"]+shopId)
	if err != nil {
		return fmt.Errorf("failed to generate sign: %w", err)
	}

	// set auth request params
	opts.data.RequestParam["sign"] = sign
	opts.data.RequestParam["timestamp"] = timestamp
	opts.data.RequestParam["access_token"] = tokens["access"]
	opts.data.RequestParam["refresh_token"] = tokens["refresh"]

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// send request
	res, err := wrapWithRefresh(ctx, wrapWithRefreshOpts{
		SendOpts: &util.SendRequestOpts{
			Url:     opts.data.RequestUrl,
			Method:  opts.data.RequestMethod,
			Params:  opts.data.RequestParam,
			Body:    opts.data.RequestBody,
			Headers: opts.data.RequestHeader,
		},
		RefreshOpt: RefreshTokenOpts{
			Refresh:    tokens["refresh"],
			PartnerKey: partnerKey,
			PartnerId:  partnerId,
			ShopId:     opts.data.RequestParam["shop_id"].(string),
			BaseUrl:    baseUrl,
			Service:    opts.storeService,
		},
		StoreService: nil,
	})

	if err != nil {
		opts.data.RetryCount++

		if errors.Is(ctx.Err(), context.DeadlineExceeded) {
			return fmt.Errorf("API request timed out: %w", err)
		}

		return fmt.Errorf("request failed: %w", err)
	}

	// set encrypted token
	opts.data.RequestParam["access_token"] = tokens["encryptedAccess"]
	opts.data.RequestParam["refresh_token"] = tokens["encryptedRefresh"]

	orderList, err := extractOrderList(res)
	if err != nil {
		return err
	}
	opts.list = orderList
	opts.key = "order"
	opts.result = &map[string][]string{
		"order_detail": nil,
		"order_list":   nil,
	}

	err = processOrderBatches(opts)
	if err != nil {
		return err
	}

	more, cursor, err := checkHasMoreOrder(res)
	if err != nil {
		return err
	}
	if more {
		err := handleOrderPagination(opts, cursor)
		if err != nil {
			return err
		}
	}

	err = queueJobs(&opts)
	if err != nil {
		return err
	}

	// decrement queue job count for scheduler batches
	if !opts.data.SchedulerTask.IsZero() {
		incrementQueueJobCount(incrementQueueJobOpts{
			data:        opts.data,
			redis:       opts.redis,
			insertedIds: *opts.result,
		})
	}

	return nil
}

func GetOrderDetail(opts getListOpts) error {
	var orderList []map[string]interface{}
	escrowMap := make(map[string]map[string]interface{})

	tokens, err := getToken(getTokenOpts{
		storeService: opts.storeService,
		Data:         &opts.data,
	})
	if err != nil {
		return err
	}

	err = getOrderDetail(getDetailOpts{
		storeService: opts.storeService,
		data:         opts.data,
		list:         &orderList,
		escrowMap:    &escrowMap,
		accessToken:  tokens["access"],
		refreshToken: tokens["refresh"],
	})
	if err != nil {
		return err
	}

	insertedIds, err := genOrderTranslateTaskExecution(genTranslateExecutionOpts{
		data:     orderList,
		existing: opts.data,
		escrows:  escrowMap,
		service:  opts.service,
	})
	if err != nil {
		return err
	}

	for _, id := range insertedIds {
		err := opts.queueServ.AddJob(queue.JobStoreKey, queue.Job{
			Key:      "scheduler:task_execution",
			Priority: int(opts.data.Priority + 1),
			Value: queue.JobValue{
				Event:     queue.EventWaiting,
				Id:        id,
				PrevEvent: "",
				Name:      "translate_order",
				Platform:  constant.MarketplaceCodeShopee,
				MaxRetry:  int(opts.data.MaxRetries),
			},
		})
		if err != nil {
			return err
		}
	}

	if !opts.data.SchedulerTask.IsZero() {
		err = opts.redis.IncrBy(
			context.Background(),
			"scheduler-"+opts.data.SchedulerTask.Hex()+"-batch-"+opts.data.Batch,
			int64(len(insertedIds)),
		).Err()
		if err != nil {
			log.Printf("error while add job count job %v: %v", opts.data, err.Error())
		}
	}

	return nil
}

func TranslateOrder(opts TranslateOrderOpts) (*schemas.Order, error) {
	order := opts.Data.RequestBody
	var orderItems []schemas.OrderItems
	mock := schemas.Order{}

	exist, err := opts.Service.FindByOrderId(order["order_sn"].(string), constant.MarketplaceCodeShopee)
	if err != nil {
		return nil, err
	}

	if exist != nil {
		mock = *exist
	}

	escrow, ok := order["escrow"].(map[string]interface{})
	if !ok {
		log.Println("field 'escrow' not found or not a JSON object")
		return nil, errors.New("field 'escrow' not found or not a JSON object")
	}

	orderIncome, ok := escrow["order_income"].(map[string]interface{})
	if !ok {
		log.Println("field 'order_income' not found or not a JSON object")
		return nil, errors.New("field 'order_income' not found or not a JSON object")
	}

	escrowItemMap := make(map[string]interface{})
	err = MapEscrowItems(MapEscrowItemsOpts{
		Escrow: orderIncome,
		Result: &escrowItemMap,
	})
	if err != nil {
		return nil, err
	}

	var itemList []interface{}

	//log.Printf("item_list type: %T", order["item_list"])

	switch v := order["item_list"].(type) {
	case primitive.A:
		itemList = v[:]
	case []interface{}:
		itemList = v
	default:
		return nil, errors.New("field 'items' on escrow is not a valid JSON array")
	}

	totalWeight := 0.0

	var feeBreakdown []schemas.OrderFee
	var discBreakdown []schemas.OrderDiscount
	var pricing map[string]float32
	err = parsePricingAndSetBreakdown(parsePricingAndSetBreakdownOpts{
		order:         order,
		orderIncome:   orderIncome,
		result:        &pricing,
		feeBreakdown:  &feeBreakdown,
		discBreakdown: &discBreakdown,
	})
	if err != nil {
		return nil, err
	}

	proratedVoucher := 0.0
	if pricing["sellerVoucherDisc"] > 0 {
		proratedVoucher = math.Floor(float64(pricing["sellerVoucherDisc"] / float32(len(itemList))))
	}
	voucherLeft := float64(pricing["sellerVoucherDisc"]) - proratedVoucher

	incompleteData := make([]schemas.IncompleteData, 0)

	undefinedItems := make([]string, 0)
	for _, item := range itemList {
		parsed, ok := item.(map[string]interface{})
		if !ok {
			return nil, errors.New("field 'items' not found or not a JSON object")
		}

		err = TranslateItems(TranslateItemOpts{
			Item:           parsed,
			Escrow:         escrowItemMap,
			OrderItems:     &orderItems,
			TotalWeight:    &totalWeight,
			Pricing:        &pricing,
			ProratedDisc:   proratedVoucher,
			ProductService: opts.ProductService,
			DiscBreakdown:  discBreakdown,
		})
		if err != nil {
			variantId, ok := parsed["model_id"].(float64)
			if !ok {
				variantId = 0.0
			}
			itemId, ok := parsed["item_id"].(float64)
			if !ok {
				itemId = 0.0
			}

			var id string
			if variantId != 0 {
				id = strconv.FormatFloat(variantId, 'f', 0, 64)
			} else {
				id = strconv.FormatFloat(itemId, 'f', 0, 64)
			}

			undefinedItems = append(undefinedItems, fmt.Sprintf("product dengan id %s tidak ditemukan", id))
			return nil, err
		}
	}

	if len(undefinedItems) > 0 {
		incompleteData = append(incompleteData, schemas.IncompleteData{
			Property: "items",
			Reason:   undefinedItems,
		})
	}

	if voucherLeft > 0 {
		orderItems[len(orderItems)-1].SellerDiscount += float32(voucherLeft)
	}

	paidAt := 0.0
	if parse, ok := order["pay_time"].(float64); ok && parse != 0 {
		paidAt = parse
		//return nil, errors.New("field 'pay_time' not found or not a JSON object")
	} else {
		log.Println("field 'pay_time' not found use default (0)")
	}

	paymentStatus := "UNPAID"
	if paidAt > 0 {
		paymentStatus = "PAID"
	}

	orderedAt, ok := order["create_time"].(float64)
	if !ok {
		log.Println("field 'create_time' not found or not a JSON object")
		return nil, errors.New("field 'create_time' not found or not a JSON object")
	}

	maxShipmentAt, ok := order["ship_by_date"].(float64)
	if !ok {
		log.Println("field 'ship_by_date' not found or not a JSON object")
		return nil, errors.New("field 'ship_by_date' not found or not a JSON object")
	}

	pickedUpAt, ok := order["pickup_done_time"].(float64)
	if !ok {
		log.Println("field 'pickup_done_time' not found or not a JSON object")
		return nil, errors.New("field 'pickup_done_time' not found or not a JSON object")
	}

	updatedAt, ok := order["update_time"].(float64)
	if !ok {
		log.Println("field 'update_time' not found or not a JSON object")
		return nil, errors.New("field 'update_time' not found or not a JSON object")
	}

	noteUpdateTime, ok := order["note_update_time"].(float64)
	if !ok {
		log.Println("field 'note_update_time' not found or not a JSON object")
		return nil, errors.New("field 'note_update_time' not found or not a JSON object")
	}

	splitUp, ok := order["split_up"].(bool)
	if !ok {
		log.Println("field 'split_up' not found or not a JSON object")
		return nil, errors.New("field 'split_up' not found or not a JSON object")
	}

	cancelReason := ""
	if parse, ok := order["cancel_reason"]; ok && parse != nil {
		cancelReason = parse.(string)
	} else {
		log.Println("field 'cancel_reason' is nil or missing, using default (0.0)")
	}

	cancelBy := ""
	if parse, ok := order["cancel_by"]; ok && parse != nil {
		cancelBy = parse.(string)
	} else {
		log.Println("field 'cancel_by' is nil or missing, using default (0.0)")
	}

	if cancelReason == "" {
		updatedAt = 0.0
	}

	var rawShipping []interface{}

	switch v := order["package_list"].(type) {
	case primitive.A:
		rawShipping = v[:]
	case []interface{}:
		rawShipping = v
	default:
		return nil, errors.New("field 'items' on package is not a valid JSON array")
	}

	shippingData, ok := rawShipping[0].(map[string]interface{})
	if !ok {
		log.Println("field 'shipping' not found or not a JSON object")
		//return nil, errors.New("field 'shipping' not found or not a JSON object")
	}

	store, err := opts.StoreService.GetByMpId(opts.Data.RequestParam["shop_id"].(string), constant.MarketplaceCodeShopee)
	if err != nil {
		return nil, err
	}
	opts.Data.RequestParam["access_token"] = store.MarketplaceCredential.EncryptedAccessValue
	opts.Data.RequestParam["refresh_token"] = store.MarketplaceCredential.EncryptedRefreshValue

	access, refresh, err := DecryptTokens(opts.Data.RequestParam)
	if err != nil {
		return nil, err
	}

	parsedStatus := constant.ShopeeOrderStatus[order["order_status"].(string)]
	if parsedStatus == "" {
		parsedStatus = constant.OrderStatusCancel
	}

	if (exist == nil || constant.ShopeeOrderStatusPriority[mock.OrderStatus] < 3) && constant.ShopeeOrderStatusPriority[string(parsedStatus)] >= 3 {
		log.Printf("getting tracking data for %s", order["order_sn"].(string))
		tracking, err := getTrackingData(getTrackingDataOpts{
			orderId:      order["order_sn"].(string),
			result:       &opts.Data,
			accessToken:  access,
			refreshToken: refresh,
			storeService: opts.StoreService,
		})
		log.Printf("completed get tracking data for %s\n", order["order_sn"].(string))

		if err != nil {
			log.Printf("\nerror while get tracking data %s: %v", order["order_sn"].(string), err)
			return nil, err
		}
		if shippingData == nil {
			shippingData = make(map[string]interface{})
		}
		shippingData["tracking_data"] = tracking
	}

	shipping := parseShipping(shippingData)

	logistic, err := opts.LogisticService.GetByMpId(shipping["logisticChannelId"].(string), constant.MarketplaceCodeShopee)
	if err != nil {
		return nil, fmt.Errorf("failed to get logistic: %w", err)
	}

	shippingName := make([]string, 0)
	if logistic == nil {
		incompleteData = append(incompleteData, schemas.IncompleteData{
			Property: "logistic",
			Reason:   []string{fmt.Sprintf("logistic dengan channel id %v tidak ditemukan", shipping["logisticChannelId"])},
		})
	} else {
		shippingName = []string{logistic.Name}
		if logistic.ServiceName != "" {
			shippingName = append(shippingName, logistic.ServiceName)
		}
	}

	if pickedUpAt == 0 && shipping["pickupAt"] != "" {
		pickedUpAt = shipping["pickupAt"].(float64)
		if err != nil {
			log.Println("field 'pickupAt' not found or not a JSON object")
		}
	}
	deliveredAt := 0.0
	if shipping["deliveredAt"] != "" {
		deliveredAt = shipping["deliveredAt"].(float64)
		if err != nil {
			log.Println("field 'deliveredAt' not found or not a JSON object")
		}
	}

	recipientData, ok := order["recipient_address"].(map[string]interface{})
	if !ok {
		log.Println("field 'recipient_address' not found or not a JSON object")
		return nil, errors.New("field 'recipient_address' not found or not a JSON object")
	}
	recipient := parseRecipient(recipientData)

	mock, err = util.CombineData(exist, &schemas.Order{
		OrderId:         order["order_sn"].(string),
		MarketplaceCode: constant.MarketplaceCodeShopee,
		Items:           orderItems,
		Currency:        order["currency"].(string),
		OrderStatus:     string(parsedStatus),
		StoreId:         store.ID,
		MpStoreId:       store.MarketplaceId,
		Payment: schemas.OrderPayment{
			Method:                  orderIncome["buyer_payment_method"].(string),
			PaymentStatus:           paymentStatus,
			Subtotal:                pricing["originalPrice"],
			DiscountSellerAmount:    pricing["sellerDiscount"],
			VoucherSellerDiscount:   pricing["sellerVoucherDisc"],
			DiscountPlatformAmount:  pricing["shopeeDisc"],
			VoucherPlatformDiscount: pricing["shopeeVoucherDisc"],
			TotalFee:                pricing["commissionFee"] + pricing["serviceFee"] + pricing["sellerTransactionFee"],
			ShipmentFee:             pricing["actualShippingFee"],
			ShipmentFeeDiscount:     pricing["shippingFeeDisc"],
			TotalAmount:             pricing["grandTotal"],
			FeeBreakdown:            feeBreakdown,
			DiscountBreakdown:       discBreakdown,
		},
		Shipment: []schemas.OrderShipment{
			{
				LogisticChannelId:     shipping["logisticChannelId"].(string),
				ShippingCarrier:       strings.Join(shippingName, "-"),
				PickupCode:            shipping["pickupCode"].(string),
				PackageNumber:         shipping["packageNumber"].(string),
				ParcelWeight:          uint16(math.Ceil(totalWeight)),
				TrackingNumber:        "",
				PackageStatus:         shipping["logisticsStatus"].(string),
				DeliveredTime:         primitive.NewDateTimeFromTime(time.Unix(int64(deliveredAt), 10)),
				EstimatedShippingFee:  pricing["estimatedShippingFee"],
				ActualShippingFee:     pricing["actualShippingFee"],
				ActualDiscShippingFee: pricing["shippingFeeDisc"],
				ShipByDate:            primitive.NewDateTimeFromTime(time.Unix(int64(maxShipmentAt), 10)),
				PickupTime:            primitive.NewDateTimeFromTime(time.Unix(int64(pickedUpAt), 10)),
				Note:                  order["message_to_seller"].(string),
				Recipient: schemas.OrderRecipient{
					Name:        recipient["name"],
					Phone:       recipient["phone"],
					Region:      recipient["region"],
					City:        recipient["city"],
					District:    recipient["district"],
					Zipcode:     recipient["zipcode"],
					FullAddress: recipient["fullAddress"],
				},
			},
		},
		IncompleteData: incompleteData,
		NoteUpdateTime: strconv.FormatFloat(noteUpdateTime*100, 'f', -1, 64),
		SplitUp:        splitUp,
		Cancellation: schemas.OrderCancellation{
			Reason:                  cancelReason,
			CancelBy:                cancelBy,
			RequestCancellationTime: primitive.NewDateTimeFromTime(time.Unix(int64(updatedAt), 10)),
			CancellationTime:        primitive.NewDateTimeFromTime(time.Unix(int64(updatedAt), 10)),
		},
		Timeline: schemas.OrderTimeline{
			OrderTime:               primitive.NewDateTimeFromTime(time.Unix(int64(orderedAt), 10)),
			ShipByDate:              primitive.NewDateTimeFromTime(time.Unix(int64(maxShipmentAt), 10)),
			PayTime:                 primitive.NewDateTimeFromTime(time.Unix(int64(paidAt), 10)),
			PickupTime:              primitive.NewDateTimeFromTime(time.Unix(int64(pickedUpAt), 10)),
			DeliveredTime:           primitive.NewDateTimeFromTime(time.Unix(int64(deliveredAt), 10)),
			RequestCancellationTime: primitive.NewDateTimeFromTime(time.Unix(0, 0)),
			CancellationTime:        primitive.NewDateTimeFromTime(time.Unix(0, 0)),
			CompleteTime:            primitive.NewDateTimeFromTime(time.Unix(0, 0)),
		},
		CompanyId: opts.Data.Company,
	})
	if err != nil {
		log.Printf("\nerror while combine struct %s", order["order_sn"].(string))
		return nil, err
	}

	id, err := opts.Service.Upsert(mock)
	if err != nil {
		return nil, err
	}

	log.Printf("result id for %s is %s", order["order_sn"].(string), id)

	return &mock, nil
}

func TranslateOrderCb(opts TranslateOrderCbOpts) (*schemas.Order, error) {
	exist, err := opts.Service.FindByOrderId(opts.OrderId, constant.MarketplaceCodeShopee)
	if err != nil {
		return nil, err
	}

	if exist == nil {
		res, err := GetAndTranslateOrder(opts)
		if err != nil {
			return nil, err
		}
		return res, nil
	}

	body := opts.Data.RequestBody
	code, ok := body["code"].(float64)
	if !ok {
		log.Printf("error parsing code to string, type: %T", body["code"])
		return nil, errors.New("error parsing code to string")
	}

	data := body["data"].(map[string]interface{})

	switch int(code) {
	case 3:
		status, ok := data["status"].(string)
		if !ok {
			log.Printf("error parsing status to string, type: %T", data["status"])
			return nil, errors.New("error parsing status to string")
		}

		parsedStatus := constant.ShopeeOrderStatus[status]

		if parsedStatus == "" {
			return nil, nil
		}

		mock := map[string]interface{}{}
		if constant.ShopeeOrderStatusPriority[exist.OrderStatus] <= constant.ShopeeOrderStatusPriority[status] {
			mock["orderStatus"] = string(parsedStatus)

			// Use primitive.DateTime instead of string for MongoDB DateTime fields
			currentTime := primitive.NewDateTimeFromTime(time.Now())

			if parsedStatus == constant.OrderStatusShipped {
				mock["shipment.0.pickupTime"] = currentTime
				mock["timeline.pickupTime"] = currentTime
			}
			if parsedStatus == constant.OrderStatusDelivered {
				mock["shipment.0.deliveredTime"] = currentTime
				mock["timeline.deliveredTime"] = currentTime
			}
			if parsedStatus == constant.OrderStatusCancel {
				mock["timeline.cancellationTime"] = currentTime
			}
			if parsedStatus == constant.OrderStatusCompleted {
				mock["timeline.completeTime"] = currentTime
			}
		}

		res, err := opts.Service.UpdateByOrderId(opts.OrderId, mock)
		if err != nil {
			return nil, err
		}

		return res, nil
	case 4:
		trackingNo, ok := data["tracking_no"].(string)
		if !ok {
			log.Printf("error parsing tracking_no to string, type: %T", data["tracking_no"])
			return nil, errors.New("error parsing tracking_no to string")
		}

		mock := map[string]interface{}{
			"shipment.$.trackingNumber": trackingNo,
		}

		res, err := opts.Service.UpdateByOrderId(opts.OrderId, mock)
		if err != nil {
			return nil, err
		}

		return res, nil
	}

	return nil, nil
}

func GetAndTranslateOrder(opts TranslateOrderCbOpts) (*schemas.Order, error) {
	var orderList []map[string]interface{}
	escrowMap := make(map[string]map[string]interface{})

	path := "/api/v2/order/get_order_detail"

	shopId := ""
	switch opts.Data.RequestBody["shop_id"].(type) {
	case float64:
		shopId = strconv.FormatFloat(opts.Data.RequestBody["shop_id"].(float64), 'f', -1, 64)
	case int32:
		shopId = strconv.FormatInt(int64(opts.Data.RequestBody["shop_id"].(int32)), 10)
	case string:
		shopId = opts.Data.RequestBody["shop_id"].(string)
	}

	opts.Data.RequestUrl = config.ShopeeBaseUrl + path
	opts.Data.RequestParam = make(map[string]interface{})
	opts.Data.RequestParam["order_sn_list"] = opts.OrderId
	opts.Data.RequestParam["partner_id"] = config.ShopeePartnerId
	opts.Data.RequestParam["shop_id"] = shopId
	opts.Data.RequestParam["response_optional_fields"] = "buyer_user_id,buyer_username,estimated_shipping_fee,recipient_address,actual_shipping_fee ,goods_to_declare,note,note_update_time,item_list,pay_time,dropshipper, dropshipper_phone,split_up,buyer_cancel_reason,cancel_by,cancel_reason,actual_shipping_fee_confirmed,buyer_cpf_id,fulfillment_flag,pickup_done_time,package_list,shipping_carrier,payment_method,total_amount,buyer_username,invoice_data,no_plastic_packing,order_chargeable_weight_gram,return_request_due_date,edt"

	store, err := opts.StoreService.GetByMpId(opts.Data.RequestParam["shop_id"].(string), constant.MarketplaceCodeShopee)
	if err != nil {
		return nil, err
	}
	opts.Data.RequestParam["access_token"] = store.MarketplaceCredential.EncryptedAccessValue
	opts.Data.RequestParam["refresh_token"] = store.MarketplaceCredential.EncryptedRefreshValue

	access, refresh, err := DecryptTokens(opts.Data.RequestParam)
	if err != nil {
		return nil, err
	}

	var wg sync.WaitGroup
	errChan := make(chan error, 2)

	wg.Add(2)

	go func() {
		defer wg.Done()
		if err := getOrderDetail(getDetailOpts{
			storeService: opts.StoreService,
			data:         opts.Data,
			service:      opts.TaskExecService,
			list:         &orderList,
			escrowMap:    &escrowMap,
			accessToken:  access,
			refreshToken: refresh,
		}); err != nil {
			errChan <- err
		}
	}()

	go func() {
		defer wg.Done()
		tracking, err := getTrackingData(getTrackingDataOpts{
			orderId:      opts.OrderId,
			result:       &opts.Data,
			accessToken:  access,
			refreshToken: refresh,
			storeService: opts.StoreService,
		})
		if err != nil {
			errChan <- err
		}
		opts.Data.RequestBody["tracking_data"] = tracking
	}()

	wg.Wait()

	if len(errChan) > 0 {
		return nil, err
	}

	opts.Data.RequestBody = orderList[0]
	opts.Data.RequestBody["escrow"] = escrowMap[opts.OrderId]

	//util.PrintJson(opts.Data.RequestBody)

	res, err := TranslateOrder(TranslateOrderOpts{
		Service:         opts.Service,
		Data:            opts.Data,
		StoreService:    opts.StoreService,
		ProductService:  opts.ProductService,
		LogisticService: opts.LogisticService,
	})
	if err != nil {
		return nil, err
	}

	return res, nil
}

func getToken(opts getTokenOpts) (map[string]string, error) {
	store, err := opts.storeService.GetByMpId(opts.Data.RequestParam["shop_id"].(string), constant.MarketplaceCodeShopee)
	if err != nil {
		return nil, err
	}
	opts.Data.RequestParam["access_token"] = store.MarketplaceCredential.EncryptedAccessValue
	opts.Data.RequestParam["refresh_token"] = store.MarketplaceCredential.EncryptedRefreshValue

	access, refresh, err := DecryptTokens(opts.Data.RequestParam)
	if err != nil {
		return nil, err
	}

	return map[string]string{
		"access":           access,
		"refresh":          refresh,
		"encryptedAccess":  store.MarketplaceCredential.EncryptedAccessValue,
		"encryptedRefresh": store.MarketplaceCredential.EncryptedRefreshValue,
	}, nil
}
