package queue

import (
	"fmt"
	"log"
	"strconv"
	"time"

	"abs-scheduler-go/pkg/constant"

	"github.com/redis/go-redis/v9"
)

func parseJob(opts ParseJobOpts) (Job, error) {
	// Safely extract retries with validation
	retriesStr, ok := opts.job["retries"].(string)
	if !ok {
		return Job{}, fmt.Errorf("retries field is missing or not a string")
	}
	retries, err := strconv.Atoi(retriesStr)
	if err != nil {
		return Job{}, fmt.Errorf("invalid retries value: %w", err)
	}

	// Safely extract maxRetry with validation
	maxRetryStr, ok := opts.job["maxRetry"].(string)
	if !ok {
		return Job{}, fmt.Errorf("maxRetry field is missing or not a string")
	}
	maxRetry, err := strconv.Atoi(maxRetryStr)
	if err != nil {
		return Job{}, fmt.Errorf("invalid maxRetry value: %w", err)
	}

	// Safely extract required string fields
	eventStr, ok := opts.job["event"].(string)
	if !ok {
		return Job{}, fmt.<PERSON>rrorf("event field is missing or not a string")
	}

	idStr, ok := opts.job["id"].(string)
	if !ok {
		return Job{}, fmt.Errorf("id field is missing or not a string")
	}

	nameStr, ok := opts.job["name"].(string)
	if !ok {
		return Job{}, fmt.Errorf("name field is missing or not a string")
	}

	platformStr, ok := opts.job["platform"].(string)
	if !ok {
		return Job{}, fmt.Errorf("platform field is missing or not a string")
	}

	// Safely extract optional fields
	var prevEventStr string
	if prevEvent, ok := opts.job["prevEvent"].(string); ok {
		prevEventStr = prevEvent
	}

	var errorMsg string
	if error, ok := opts.job["error"].(string); ok {
		errorMsg = error
	}

	return Job{
		Key: opts.key,
		Value: JobValue{
			Event:     Event(eventStr),
			Id:        idStr,
			PrevEvent: prevEventStr,
			Name:      nameStr,
			Platform:  constant.MarketplaceCodeEnum(platformStr),
			Retries:   retries,
			MaxRetry:  maxRetry,
			Error:     errorMsg,
		},
	}, nil
}

func EncodeJob(job JobValue) map[string]interface{} {
	return map[string]interface{}{
		"id":        job.Id,
		"event":     string(job.Event),
		"prevEvent": job.PrevEvent,
		"platform":  string(job.Platform),
		"retries":   job.Retries,
		"maxRetry":  job.MaxRetry,
		"name":      job.Name,
		"error":     job.Error,
	}
}

func moveToStream(opts MoveToStreamOpts) {
	opts.client.XAdd(opts.ctx, &redis.XAddArgs{
		Stream: opts.stream,
		Values: EncodeJob(opts.job.Value),
	})
}

func ExtendLock(opts ExtendLockOpts) {
	if opts.ExtendTime == 0 {
		opts.ExtendTime = 30 * time.Second
	}
	if opts.ExtendCheckTime == 0 {
		opts.ExtendCheckTime = 15 * time.Second
	}

	ticker := time.NewTicker(opts.ExtendCheckTime)
	defer ticker.Stop()

	for {
		select {
		case <-opts.Ctx.Done():
			return
		case <-ticker.C:
			err := opts.Client.SetNX(opts.Ctx, opts.Key, opts.Value, opts.ExtendTime).Err()
			if err != nil {
				log.Printf("Failed to extend lock key %s : %v", opts.Key, err)
			}
		}
	}
}
