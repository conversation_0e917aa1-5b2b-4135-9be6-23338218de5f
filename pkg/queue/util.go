package queue

import (
	"log"
	"strconv"
	"time"

	"abs-scheduler-go/pkg/constant"
	"github.com/redis/go-redis/v9"
)

func parseJob(opts ParseJobOpts) (Job, error) {
	retries, err := strconv.Atoi(opts.job["retries"].(string))
	if err != nil {
		return Job{}, err
	}
	maxRetry, err := strconv.Atoi(opts.job["maxRetry"].(string))
	if err != nil {
		return Job{}, err
	}

	var errorMsg string
	if opts.job["error"] != nil {
		errorMsg = opts.job["error"].(string)
	}

	return Job{
		Key: opts.key,
		Value: JobValue{
			Event:     Event(opts.job["event"].(string)),
			Id:        opts.job["id"].(string),
			PrevEvent: opts.job["prevEvent"].(string),
			Name:      opts.job["name"].(string),
			Platform:  constant.MarketplaceCodeEnum(opts.job["platform"].(string)),
			Retries:   retries,
			MaxRetry:  maxRetry,
			Error:     errorMsg,
		},
	}, nil
}

func EncodeJob(job JobValue) map[string]interface{} {
	return map[string]interface{}{
		"id":        job.Id,
		"event":     string(job.Event),
		"prevEvent": job.PrevEvent,
		"platform":  string(job.Platform),
		"retries":   job.Retries,
		"maxRetry":  job.MaxRetry,
		"name":      job.Name,
		"error":     job.Error,
	}
}

func moveToStream(opts MoveToStreamOpts) {
	opts.client.XAdd(opts.ctx, &redis.XAddArgs{
		Stream: opts.stream,
		Values: EncodeJob(opts.job.Value),
	})
}

func ExtendLock(opts ExtendLockOpts) {
	if opts.ExtendTime == 0 {
		opts.ExtendTime = 30 * time.Second
	}
	if opts.ExtendCheckTime == 0 {
		opts.ExtendCheckTime = 15 * time.Second
	}

	ticker := time.NewTicker(opts.ExtendCheckTime)
	defer ticker.Stop()

	for {
		select {
		case <-opts.Ctx.Done():
			return
		case <-ticker.C:
			err := opts.Client.SetNX(opts.Ctx, opts.Key, opts.Value, opts.ExtendTime).Err()
			if err != nil {
				log.Printf("Failed to extend lock key %s : %v", opts.Key, err)
			}
		}
	}
}
