package queue

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/redis/go-redis/v9"
)

func (s Service) MoveFromListToStream() {
	for {
		ctx := context.Background()
		jobData, err := s.Redis.ZPopMin(ctx, JobStoreKey).Result()
		if err != nil || len(jobData) == 0 {
			time.Sleep(500 * time.Millisecond)
			continue
		}

		var job JobValue
		err = json.Unmarshal([]byte(jobData[0].Member.(string)), &job)
		if err != nil {
			return
		}

		// Move job to Redis Stream
		_, err = s.Redis.XAdd(ctx, &redis.XAddArgs{
			Stream: WaitingStream,
			Values: EncodeJob(job),
		}).Result()

		if err != nil {
			log.Println("Error moving job to stream:", err)
		} else {
			log.Println("Moved job", job.Id, "to Redis Stream for processing")
		}

		time.Sleep(100 * time.Millisecond)
	}
}

func (s Service) MoveFromScheduleToStream() {
	for {
		now := float64(time.Now().Unix())
		ctx := context.Background()

		jobData, err := s.Redis.ZRangeByScoreWithScores(ctx, ScheduledJobStoreKey, &redis.ZRangeBy{
			Min:   "0",
			Max:   fmt.Sprintf("%f", now),
			Count: 1,
		}).Result()

		if err != nil || len(jobData) == 0 {
			time.Sleep(500 * time.Millisecond)
			continue
		}

		for _, item := range jobData {
			var job JobValue
			err = json.Unmarshal([]byte(jobData[0].Member.(string)), &job)
			if err != nil {
				return
			}

			_, err = s.Redis.XAdd(ctx, &redis.XAddArgs{
				Stream: WaitingStream,
				Values: EncodeJob(job),
			}).Result()

			if err == nil {
				s.Redis.ZRem(ctx, ScheduledJobStoreKey, item.Member)
				log.Println("Moved scheduled job", job.Id, "to Redis Stream")
			} else {
				log.Println("Moved scheduled job", job.Id, "to Redis Stream for processing")
			}
		}

		time.Sleep(100 * time.Millisecond)
	}
}

func (s Service) MoveCompletedStream() {
	ctx := context.Background()
	threeHoursAgo := time.Now().Add(-SuccessThreshold).Unix()

	msgs, err := s.Redis.XRange(ctx, WaitingStream, "0", fmt.Sprintf("%d", threeHoursAgo)).Result()
	if err != nil {
		log.Fatalf("Failed to read from stream: %v", err)
	}

	for _, msg := range msgs {
		job, err := parseJob(ParseJobOpts{
			key: CompleteStream,
			job: msg.Values,
		})
		if err != nil {
			time.Sleep(1 * time.Second)
			log.Printf("Error moving complete job to stream: %v", err)
			continue
		}

		if job.Value.Event == EventCompleted {
			moveToStream(MoveToStreamOpts{
				client: s.Redis,
				ctx:    ctx,
				job:    job,
				stream: CompleteStream,
			})
		}
	}
}

func (s Service) MoveDrainToWaiting() {
	for {
		ctx := context.Background()
		jobData, err := s.Redis.ZPopMin(ctx, DrainedJobStoreKey).Result()
		if err != nil || len(jobData) == 0 {
			time.Sleep(500 * time.Millisecond)
			continue
		}

		var job JobValue
		err = json.Unmarshal([]byte(jobData[0].Member.(string)), &job)
		if err != nil {
			return
		}

		log.Printf("drained job: %v", EncodeJob(job))

		job.PrevEvent = string(EventDrained)
		job.Event = EventWaiting
		err = s.Redis.XAdd(ctx, &redis.XAddArgs{
			Stream: WaitingStream,
			Values: EncodeJob(job),
		}).Err()

		if err != nil {
			log.Println("Error moving drained job to stream:", err)
		} else {
			log.Println("Moved drained job", job.Id, "to Redis Stream for processing")
		}

		time.Sleep(100 * time.Millisecond)
	}
}

func (s Service) MoveActiveToDrain() {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		ctx := context.Background()
		jobData, err := s.Redis.ZPopMin(ctx, ActiveJobStoreKey).Result()
		if err != nil || len(jobData) == 0 {
			continue
		}

		var job JobValue
		err = json.Unmarshal([]byte(jobData[0].Member.(string)), &job)
		if err != nil {
			log.Printf("Error moving active job to drain: %v", err)
			return
		}

		exist, err := s.Redis.Exists(ctx, job.Id+":lock").Result()
		if err != nil {
			log.Printf("Error checking if job exists: %v", err)
			return
		}

		member, err := json.Marshal(job)
		if err != nil {
			log.Printf("Error moving active job to drain: %v", err)
			return
		}

		if exist == 1 {
			s.Redis.ZAdd(ctx, ActiveJobStoreKey, redis.Z{Member: jobData[0].Member})
			continue
		}

		job.Event = EventDrained
		job.PrevEvent = string(EventActive)

		s.Redis.XAdd(ctx, &redis.XAddArgs{
			Stream: WaitingStream,
			Values: EncodeJob(job),
		})
		err = s.Redis.ZAdd(ctx, DrainedJobStoreKey, redis.Z{Member: member}).Err()

		if err != nil {
			log.Println("Error moving drained job to stream:", err)
		} else {
			log.Println("Moved active job", job.Id, "to drain")
		}
	}
}
