package queue

import (
	"context"
	"time"

	"abs-scheduler-go/pkg/constant"
	"github.com/redis/go-redis/v9"
)

type Event string

const (
	EventWaiting   Event = "waiting"
	EventActive    Event = "active"
	EventCompleted Event = "completed"
	EventDrained   Event = "drained"
	EventFailed    Event = "failed"
)

type Service struct {
	Redis *redis.Client
}

type JobValue struct {
	Event     Event                        `json:"event"`
	Id        string                       `json:"id"`
	PrevEvent string                       `json:"prevEvent"`
	Name      string                       `json:"name"`
	Platform  constant.MarketplaceCodeEnum `json:"platform"`
	Retries   int                          `json:"retries"`
	MaxRetry  int                          `json:"maxRetry"`
	Error     string                       `json:"error"`
}

type Job struct {
	Key       string
	Value     JobValue
	Priority  int
	EventName string
}

type ScheduledJob struct {
	job      Job
	schedule time.Time
	key      string
}

type ConsumeQueueOpts struct {
	Callback     func(job *Job) (skipMsg string, err error, delay time.Duration)
	Key          string
	GroupName    string
	EventName    string
	ScheduledKey string
	ActiveKey    string
	SendToDlq    bool
}

type ParseJobOpts struct {
	key string
	job map[string]interface{}
}

type MoveToStreamOpts struct {
	client *redis.Client
	ctx    context.Context
	job    Job
	stream string
}

type ExtendLockOpts struct {
	Key             string
	Client          *redis.Client
	Ctx             context.Context
	Value           interface{}
	ExtendTime      time.Duration
	ExtendCheckTime time.Duration
}
