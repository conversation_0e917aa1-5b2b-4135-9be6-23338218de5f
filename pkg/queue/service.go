package queue

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"math/rand"
	"sync"
	"time"

	"abs-scheduler-go/pkg/config"
	"abs-scheduler-go/pkg/util"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
)

const (
	CallbackStoreKey     = "callback:list"
	CallbackActiveKey    = "callback:active_list"
	CallbackStream       = "callback:events"
	JobStoreKey          = "scheduler:job_list"
	ScheduledJobStoreKey = "scheduler:scheduled_job_list"
	DrainedJobStoreKey   = "scheduler:drained_job_list"
	ActiveJobStoreKey    = "scheduler:active_job_list"
	WaitingStream        = "scheduler:task_execution:events"
	FailedStream         = "scheduler:task_execution:dead_letter_queue_events"
	CompleteStream       = "scheduler:task_execution:complete_events"
	SuccessThreshold     = 3 * time.Hour

	StreamCleanupInterval = 6 * time.Hour      // clean up interval every 6 hours
	StreamRetentionPeriod = 2 * 24 * time.Hour // clean up messages older than 2 days
	StreamTrimBatchSize   = 1000               // batch size for trimming

	CleanupLockKey      = "stream:cleanup:lock"
	CleanupLockDuration = 30 * time.Minute
	CleanupJitterMax    = 5 * time.Minute
)

func NewService(redis *redis.Client) *Service {
	return &Service{Redis: redis}
}

func (s Service) InitQueue() {
	go s.MoveCompletedStream()
	go s.MoveFromListToStream()
	go s.MoveFromScheduleToStream()
	go s.MoveActiveToDrain()
	go s.MoveDrainToWaiting()
	go s.StartStreamCleanupScheduler()
}

// StartStreamCleanupScheduler runs periodic cleanup of old stream messages
// with distributed locking to prevent multiple instances from cleaning simultaneously
func (s Service) StartStreamCleanupScheduler() {
	// List of streams to clean up
	streams := []string{
		WaitingStream,
		FailedStream,
		CompleteStream,
		CallbackStream, // Add callback stream to cleanup
	}

	log.Printf("streams cleanup: %v", streams)

	//Add random jitter to prevent all instances from trying to acquire lock at the same time
	jitter := time.Duration(rand.Int63n(int64(CleanupJitterMax)))
	time.Sleep(jitter)

	log.Printf("Starting stream cleanup scheduler with jitter %v", jitter)

	// Run cleanup immediately on startup
	s.runStreamCleanup(streams)

	// Then set up ticker for periodic runs
	ticker := time.NewTicker(StreamCleanupInterval)
	defer ticker.Stop()

	// Run cleanup on schedule with distributed locking
	for range ticker.C {
		s.runStreamCleanup(streams)
	}
}

// Helper function to run the cleanup process with distributed locking
func (s Service) runStreamCleanup(streams []string) {
	// Try to acquire distributed lock
	ctx := context.Background()
	lockValue := uuid.NewString()

	// Try to acquire lock with NX (only if not exists) and expiration
	acquired, err := s.Redis.SetNX(ctx, CleanupLockKey, lockValue, CleanupLockDuration).Result()
	if err != nil {
		log.Printf("Error acquiring cleanup lock: %v", err)
		return
	}

	if !acquired {
		// Another instance is already running cleanup
		log.Printf("Skipping stream cleanup as another instance holds the lock")
		return
	}

	// We acquired the lock, run cleanup
	log.Printf("Acquired cleanup lock, starting stream cleanup")

	// Create a context that will be canceled when cleanup is done
	cleanupCtx, cancel := context.WithCancel(ctx)

	// Start a goroutine to extend the lock while cleanup is running
	go func() {
		ticker := time.NewTicker(CleanupLockDuration / 3)
		defer ticker.Stop()

		for {
			select {
			case <-cleanupCtx.Done():
				return
			case <-ticker.C:
				// Extend lock if we still own it
				extended, err := s.Redis.Eval(ctx, `
					if redis.call("GET", KEYS[1]) == ARGV[1] then
						return redis.call("PEXPIRE", KEYS[1], ARGV[2])
					else
						return 0
					end
				`, []string{CleanupLockKey}, lockValue, int(CleanupLockDuration/time.Millisecond)).Result()

				if err != nil || extended == int64(0) {
					log.Printf("Failed to extend cleanup lock, another instance may have taken over")
					cancel()
					return
				}
			}
		}
	}()

	// Run the actual cleanup
	s.cleanupStreams(streams)

	// Release the lock if we still own it
	s.Redis.Eval(ctx, `
		if redis.call("GET", KEYS[1]) == ARGV[1] then
			return redis.call("DEL", KEYS[1])
		else
			return 0
		end
	`, []string{CleanupLockKey}, lockValue)

	// Cancel the lock extension goroutine
	cancel()

	log.Printf("Stream cleanup completed and lock released")
}

func (s Service) cleanupStreams(streams []string) {
	ctx := context.Background()
	var wg sync.WaitGroup

	channel := make(chan struct{}, 3)

	for _, stream := range streams {
		wg.Add(1)
		streamName := stream

		channel <- struct{}{}

		go func() {
			defer wg.Done()
			defer func() { <-channel }()

			cutoffTime := time.Now().Add(-StreamRetentionPeriod)
			cutoffID := fmt.Sprintf("%d-0", cutoffTime.UnixMilli())

			log.Printf("Starting cleanup of stream %s, removing messages older than %s",
				streamName, cutoffTime.Format(time.RFC3339))

			streamInfo, err := s.Redis.XInfoStream(ctx, streamName).Result()
			if err != nil {
				if !errors.Is(err, redis.Nil) {
					log.Printf("Error getting stream info for %s: %v", streamName, err)
				}
				return
			}

			if streamInfo.Groups > 0 && streamInfo.Length > 0 {
				remainingToTrim := true
				batchesTrimmed := 0
				totalTrimmed := int64(0)

				batchSize := StreamTrimBatchSize
				if streamInfo.Length > 100000 {
					batchSize = 2000
				} else if streamInfo.Length < 10000 {
					batchSize = 500
				}

				for remainingToTrim {
					trimmed, err := s.Redis.Do(ctx, "XTRIM", stream, "MINID", "~", cutoffID, "LIMIT", batchSize).Int64()

					if err != nil {
						log.Printf("Error trimming stream %s: %v", streamName, err)
						break
					}

					log.Printf("Trimmed %d messages from stream %s", trimmed, streamName)

					totalTrimmed += trimmed
					batchesTrimmed++

					if trimmed == 0 {
						remainingToTrim = false
					}

					if remainingToTrim {
						time.Sleep(2 * time.Millisecond)
					}
				}

				log.Printf("Cleaned up %d messages from stream %s in %d batches",
					totalTrimmed, streamName, batchesTrimmed)
			} else {
				log.Printf("Stream %s is empty or has no consumer groups, skipping cleanup", streamName)
			}

			s.cleanupConsumerGroups(ctx, streamName)
		}()
	}

	wg.Wait()
	log.Printf("All streams cleanup completed")
}

func (s Service) cleanupConsumerGroups(ctx context.Context, stream string) {
	groups, err := s.Redis.XInfoGroups(ctx, stream).Result()
	if err != nil {
		if !errors.Is(err, redis.Nil) {
			log.Printf("Error getting consumer groups for stream %s: %v", stream, err)
		}
		return
	}

	for _, group := range groups {
		consumers, err := s.Redis.XInfoConsumers(ctx, stream, group.Name).Result()
		if err != nil {
			log.Printf("Error getting consumers for group %s: %v", group.Name, err)
			continue
		}

		for _, consumer := range consumers {
			if consumer.Pending == 0 && consumer.Idle > 30*time.Minute {
				log.Printf("Removing idle consumer %s from group %s", consumer.Name, group.Name)
				s.Redis.XGroupDelConsumer(ctx, stream, group.Name, consumer.Name)
			}
		}

		if group.Pending > 0 {
			pending, err := s.Redis.XPendingExt(ctx, &redis.XPendingExtArgs{
				Stream: stream,
				Group:  group.Name,
				Start:  "-",
				End:    "+",
				Count:  100,
			}).Result()

			if err != nil {
				log.Printf("Error getting pending messages for group %s: %v", group.Name, err)
				continue
			}

			var oldMsgIDs []string
			for _, p := range pending {
				if p.Idle > StreamRetentionPeriod {
					oldMsgIDs = append(oldMsgIDs, p.ID)
				}
			}

			if len(oldMsgIDs) > 0 {
				cleanupConsumer := "cleanup-worker-" + uuid.NewString()

				claimed, err := s.Redis.XClaim(ctx, &redis.XClaimArgs{
					Stream:   stream,
					Group:    group.Name,
					Consumer: cleanupConsumer,
					MinIdle:  StreamRetentionPeriod,
					Messages: oldMsgIDs,
				}).Result()

				if err != nil {
					log.Printf("Error claiming old messages for group %s: %v", group.Name, err)
				} else {
					log.Printf("Claimed %d old messages from group %s", len(claimed), group.Name)

					if len(claimed) > 0 {
						claimedIDs := make([]string, len(claimed))
						for i, msg := range claimed {
							claimedIDs[i] = msg.ID
						}

						acked, err := s.Redis.XAck(ctx, stream, group.Name, claimedIDs...).Result()
						if err != nil {
							log.Printf("Error acknowledging old messages for group %s: %v", group.Name, err)
						} else {
							log.Printf("Acknowledged %d old messages from group %s", acked, group.Name)
						}
					}
				}

				s.Redis.XGroupDelConsumer(ctx, stream, group.Name, cleanupConsumer)
			}
		}
	}
}

func (s Service) AddJob(eventName string, opts Job) error {
	value, err := util.StructToMap(opts.Value)
	if err != nil {
		return err
	}

	member, err := json.Marshal(value)
	if err != nil {
		return err
	}

	_, err = s.Redis.ZAdd(context.Background(), eventName, redis.Z{
		Score:  float64(opts.Priority),
		Member: member,
	}).Result()

	return err
}

func (s Service) AddScheduledJob(opts ScheduledJob) error {
	value, err := util.StructToMap(opts.job.Value)
	if err != nil {
		return err
	}

	member, err := json.Marshal(value)
	if err != nil {
		return err
	}

	if opts.key == "" {
		opts.key = ScheduledJobStoreKey
	}

	_, err = s.Redis.ZAdd(context.Background(), opts.key, redis.Z{
		Score:  float64(opts.schedule.Unix()),
		Member: member,
	}).Result()

	return err
}

func (s Service) ConsumeQueue(opts ConsumeQueueOpts) error {
	id := ":" + uuid.NewString()
	ctx := context.Background()
	consumerName := opts.Key + id

	_, err := s.Redis.XGroupCreateMkStream(ctx, opts.EventName, opts.GroupName, "0").Result()
	if err != nil && err.Error() != "BUSYGROUP Consumer Group name already exists" {
		panic(err)
	}

	log.Printf("Starting consumer %s in group %s", consumerName, opts.GroupName)

	// Start a goroutine to claim stale messages
	go claimStaleMessages(s, opts, consumerName)

	// Start a goroutine to check consumer group health
	go checkConsumerGroupHealth(s, opts)

	// Add error recovery
	if config.Environment != "local" {
		defer func() {
			if r := recover(); r != nil {
				log.Printf("Recovered from panic in ConsumeQueue: %v", r)
				// Optionally restart the consumer
				go func() {
					err := s.ConsumeQueue(opts)
					if err != nil {
						log.Printf("Error restarting consumer: %v", err)
					}
				}()
			}
		}()
	}

	// First, check for any pending messages
	processPendingMessages(s, ctx, opts, consumerName)

	// Then start reading new messages
	for {
		log.Printf("Waiting for new messages...")
		readCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
		streams, err := s.Redis.XReadGroup(readCtx, &redis.XReadGroupArgs{
			Group:    opts.GroupName,
			Consumer: consumerName,
			Streams:  []string{opts.EventName, ">"},
			Count:    10,
			Block:    5000 * time.Millisecond,
		}).Result()
		cancel()

		// Add explicit logging
		log.Printf("XReadGroup returned - error: %v, streams len: %d", err, len(streams))

		if err != nil {
			if errors.Is(err, redis.Nil) || errors.Is(err, context.DeadlineExceeded) {
				log.Printf("No new messages or timeout, will check again")
				continue
			}
			log.Printf("Error reading from stream: %v", err)
			time.Sleep(1 * time.Second)
			continue
		}

		// No streams or no messages in streams
		if len(streams) == 0 {
			log.Printf("No streams returned, will check again")
			continue
		}

		messageCount := 0
		for _, s := range streams {
			messageCount += len(s.Messages)
		}

		if messageCount == 0 {
			log.Printf("No messages in streams, will check again")
			continue
		}

		log.Printf("Received %d total messages across %d streams", messageCount, len(streams))

		// Process messages
		processedCount := 0
		for _, stream := range streams {
			for _, message := range stream.Messages {
				log.Printf("Processing message %s", message.ID)
				startTime := time.Now()
				if err := processMessage(s, ctx, opts, consumerName, message); err != nil {
					log.Printf("Error processing message %s: %v", message.ID, err)
					// Continue processing other messages
				} else {
					processTime := time.Since(startTime)
					log.Printf("Successfully processed message %s in %v", message.ID, processTime)
					processedCount++
				}
			}
		}

		log.Printf("Finished processing batch. Successfully processed %d/%d messages",
			processedCount, messageCount)

		// Add a small delay between batches to prevent CPU spinning
		time.Sleep(100 * time.Millisecond)

		log.Printf("Loop completed, starting next iteration")

	}
}

func processPendingMessages(s Service, ctx context.Context, opts ConsumeQueueOpts, consumerName string) {
	for {
		// Try to process pending messages first (messages that were delivered but not acknowledged)
		pendingStreams, err := s.Redis.XReadGroup(ctx, &redis.XReadGroupArgs{
			Group:    opts.GroupName,
			Consumer: consumerName,
			Streams:  []string{opts.EventName, "0"}, // "0" means all pending messages
			Count:    10,
			Block:    100 * time.Millisecond,
		}).Result()

		if err != nil && !errors.Is(err, redis.Nil) {
			log.Printf("Error reading pending messages: %v", err)
			return
		}

		// Process any pending messages first
		if len(pendingStreams) > 0 && len(pendingStreams[0].Messages) > 0 {
			log.Printf("Processing %d pending messages", len(pendingStreams[0].Messages))
			for _, stream := range pendingStreams {
				for _, message := range stream.Messages {
					if err := processMessage(s, ctx, opts, consumerName, message); err != nil {
						log.Printf("Error processing pending message: %v", err)
					}
				}
			}
		} else {
			// No more pending messages, break to start reading new ones
			return
		}
	}
}

// Process a single message
func processMessage(s Service, ctx context.Context, opts ConsumeQueueOpts, consumerName string, message redis.XMessage) error {
	if opts.ActiveKey == "" {
		opts.ActiveKey = ActiveJobStoreKey
	}

	// Log message details with more context
	messageID := message.ID
	jobID := ""
	if id, ok := message.Values["id"]; ok {
		jobID = id.(string)
	}
	log.Printf("Processing message ID: %s, Job ID: %s", messageID, jobID)

	job, err := parseJob(ParseJobOpts{
		key: opts.EventName,
		job: message.Values,
	})
	if err != nil {
		// Log parsing error with more details
		log.Printf("Failed to parse job from message %s: %v, Values: %+v", messageID, err, message.Values)
		// Acknowledge the message even if we can't parse it
		// to prevent it from getting stuck in pending state
		s.Redis.XAck(ctx, opts.EventName, opts.GroupName, message.ID)
		return fmt.Errorf("failed to parse job: %w", err)
	}

	// Check if job has exceeded max retries
	if job.Value.Retries >= job.Value.MaxRetry && opts.SendToDlq {
		job.Value.Event = EventFailed
		log.Printf("Job %s exceeded max retries (%d/%d), moving to DLQ", job.Value.Id, job.Value.Retries, job.Value.MaxRetry)
		moveToStream(MoveToStreamOpts{
			client: s.Redis,
			ctx:    ctx,
			job:    job,
			stream: FailedStream,
		})
		s.Redis.XAck(ctx, opts.EventName, opts.GroupName, message.ID)
		return nil
	}

	job.Value.Error = ""

	// Handle different job states more explicitly
	switch job.Value.Event {
	case EventWaiting:
		// Process waiting jobs normally
		log.Printf("Processing waiting job: %s", job.Value.Id)
	case EventActive:
		// Skip active jobs that are already being processed
		log.Printf("Skipping active job: %s (already being processed)", job.Value.Id)
		s.Redis.XAck(ctx, opts.EventName, opts.GroupName, message.ID)
		return nil
	case EventCompleted:
		// Skip completed jobs
		log.Printf("Skipping completed job: %s", job.Value.Id)
		s.Redis.XAck(ctx, opts.EventName, opts.GroupName, message.ID)
		return nil
	case EventFailed:
		// Skip failed jobs
		log.Printf("Skipping failed job: %s", job.Value.Id)
		s.Redis.XAck(ctx, opts.EventName, opts.GroupName, message.ID)
		return nil
	default:
		// Unknown state, acknowledge and skip
		log.Printf("Unknown job state %s for job %s, skipping", job.Value.Event, job.Value.Id)
		s.Redis.XAck(ctx, opts.EventName, opts.GroupName, message.ID)
		return nil
	}

	job.Value.PrevEvent = string(job.Value.Event)
	job.Value.Event = EventActive
	_, err = s.Redis.XAdd(ctx, &redis.XAddArgs{
		Stream: opts.EventName,
		Values: EncodeJob(job.Value),
	}).Result()
	if err != nil {
		log.Printf("failed to add job to queue: %v", err)
		return err
	}

	member, err := json.Marshal(job.Value)
	if err != nil {
		log.Printf("failed to marshal job: %v", err)
		return err
	}

	s.Redis.ZAdd(ctx, opts.ActiveKey, redis.Z{Member: member})
	s.Redis.SetNX(ctx, job.Value.Id+":lock", member, 30*time.Second)

	ctxExtend, cancel := context.WithCancel(context.Background())
	go ExtendLock(ExtendLockOpts{
		Key:    job.Value.Id + ":lock",
		Client: s.Redis,
		Ctx:    ctxExtend,
		Value:  member,
	})

	log.Printf("Processing jobId: %s", job.Value.Id)

	// Wrap callback in a defer-recover to prevent panic from killing the consumer
	var skip string
	var callbackErr error
	var delay time.Duration

	if config.Environment != "local" {
		func() {
			defer func() {
				if r := recover(); r != nil {
					callbackErr = fmt.Errorf("callback panicked: %v", r)
				}
			}()
			skip, callbackErr, delay = opts.Callback(&job)
		}()
	} else {
		skip, callbackErr, delay = opts.Callback(&job)
	}

	log.Printf("callback complete with err:%s and skip %s", callbackErr, skip)

	if callbackErr != nil {
		job.Value.Retries++
		job.Value.PrevEvent = string(EventActive)
		job.Value.Event = EventWaiting
		job.Value.Error = callbackErr.Error()

		if job.Value.Retries <= job.Value.MaxRetry {
			if opts.ScheduledKey != "" {
				err := s.AddScheduledJob(ScheduledJob{
					job:      job,
					schedule: time.Now().Add(delay),
					key:      opts.ScheduledKey,
				})
				if err != nil {
					log.Printf("Error while added to scheduled: %v", err)
				}
			} else {
				s.Redis.XAdd(ctx, &redis.XAddArgs{
					Stream: opts.EventName,
					Values: EncodeJob(job.Value),
				})
			}
		}
	} else {
		// Job executed successfully
		if skip != "" {
			// Job was skipped, reschedule for later
			log.Printf("Job %s was skipped: %s, rescheduling", job.Value.Id, skip)
			job.Value.Error = ""
			job.Value.Event = EventWaiting
			err := s.AddScheduledJob(ScheduledJob{
				job:      job,
				schedule: time.Now().Add(1 * time.Second),
				key:      opts.ScheduledKey,
			})
			if err != nil {
				log.Printf("Error rescheduling skipped job %s: %v", job.Value.Id, err)
			}
		} else {
			// Job completed successfully
			log.Printf("Job %s completed successfully", job.Value.Id)
			job.Value.PrevEvent = string(job.Value.Event)
			job.Value.Event = EventCompleted
			s.Redis.XAdd(ctx, &redis.XAddArgs{
				Stream: opts.EventName,
				Values: EncodeJob(job.Value),
			})
		}
	}

	err = s.Redis.XAck(ctx, opts.EventName, opts.GroupName, message.ID).Err()
	if err != nil {
		log.Printf("Error acknowledging job: %v", err)
	} else {
		log.Printf("Worker %s completed job: %s\n", consumerName, job.Key)
	}

	err = s.Redis.ZRem(ctx, opts.ActiveKey, member).Err()
	if err != nil {
		log.Printf("Error deleting active job: %v", err)
	}

	cancel()
	return nil
}

// Claim stale messages from other consumers
func claimStaleMessages(s Service, opts ConsumeQueueOpts, consumerName string) {
	ctx := context.Background()
	for {
		time.Sleep(30 * time.Second)

		// Get list of consumers with pending messages
		pending, err := s.Redis.XPendingExt(ctx, &redis.XPendingExtArgs{
			Stream: opts.EventName,
			Group:  opts.GroupName,
			Start:  "-",
			End:    "+",
			Count:  100,
		}).Result()

		if err != nil {
			log.Printf("Error checking pending messages: %v", err)
			continue
		}

		// Claim messages that have been idle for > 1 minute
		var msgIDs []string
		for _, p := range pending {
			if p.Idle > time.Minute {
				msgIDs = append(msgIDs, p.ID)
			}
		}

		if len(msgIDs) > 0 {
			claimed, err := s.Redis.XClaim(ctx, &redis.XClaimArgs{
				Stream:   opts.EventName,
				Group:    opts.GroupName,
				Consumer: consumerName,
				MinIdle:  time.Minute,
				Messages: msgIDs,
			}).Result()

			if err != nil {
				log.Printf("Error claiming messages: %v", err)
			} else {
				log.Printf("Claimed %d stale messages", len(claimed))
				// Process the claimed messages
				for _, message := range claimed {
					if err := processMessage(s, ctx, opts, consumerName, message); err != nil {
						log.Printf("Error processing claimed message: %v", err)
					}
				}
			}
		}
	}
}

// Check consumer group health
func checkConsumerGroupHealth(s Service, opts ConsumeQueueOpts) {
	ctx := context.Background()
	for {
		time.Sleep(5 * time.Minute)

		// Get consumer group info
		info, err := s.Redis.XInfoGroups(ctx, opts.EventName).Result()
		if err != nil {
			log.Printf("Error checking consumer group: %v", err)
			continue
		}

		// Log consumer group state
		for _, group := range info {
			log.Printf("Group %s: consumers=%d pending=%d last-delivered=%s",
				group.Name, group.Consumers, group.Pending, group.LastDeliveredID)
		}

		// Check if consumers are idle and remove them if needed
		consumers, err := s.Redis.XInfoConsumers(ctx, opts.EventName, opts.GroupName).Result()
		if err != nil {
			log.Printf("Error checking consumers: %v", err)
			continue
		}

		for _, consumer := range consumers {
			if consumer.Pending == 0 && consumer.Idle > 30*time.Minute {
				log.Printf("Removing idle consumer: %s", consumer.Name)
				s.Redis.XGroupDelConsumer(ctx, opts.EventName, opts.GroupName, consumer.Name)
			}
		}
	}
}
