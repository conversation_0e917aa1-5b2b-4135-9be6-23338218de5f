package main

import (
	"log"
	"strings"

	"abs-scheduler-go/cmd/callback"
	"abs-scheduler-go/cmd/health_check"
	"abs-scheduler-go/cmd/scheduler_task"
	"abs-scheduler-go/cmd/task_execution"
	"abs-scheduler-go/cmd/webhook"
	"abs-scheduler-go/pkg/config"
	"abs-scheduler-go/pkg/database"
	"abs-scheduler-go/pkg/util"

	"github.com/joho/godotenv"
)

func main() {
	_ = godotenv.Load()

	config.Init()

	modules := []interface{}{"scheduler", "task_executor", "webhook", "callback"}
	moduleList := strings.Split(config.Modules, ",")

	var filteredModules []string
	for _, module := range moduleList {
		if util.Contains(modules, module) {
			filteredModules = append(filteredModules, module)
		}
	}

	if len(filteredModules) == 0 {
		log.Fatalf("No modules enabled or not either %v", modules)
	}

	db := database.Init()

	go health_check.Run(db)

	log.Printf("Running task execution modules %v", filteredModules)

	for _, module := range moduleList {
		go func(m string) {
			switch m {
			case "scheduler":
				scheduler_task.Run(db)
			case "task_executor":
				task_execution.Run(db)
			case "webhook":
				webhook.Run(db)
			case "callback":
				callback.Run(db)
			}
		}(module)
	}

	select {}
}
