package health_check

import (
	"context"
	"fmt"
	"net/http"
	"sync"

	"go.mongodb.org/mongo-driver/mongo"
)

func Run(db map[string]*mongo.Database) {
	var mu sync.Mutex
	http.HandleFunc("/health-check", func(w http.ResponseWriter, r *http.Request) {
		w.<PERSON><PERSON><PERSON><PERSON>(http.StatusOK)
		fmt.Fprintln(w, "OK")
	})

	http.HandleFunc("/readyz", func(w http.ResponseWriter, r *http.Request) {
		mu.Lock()
		defer mu.Unlock()

		for key, conn := range db {
			if err := conn.Client().<PERSON>(context.Background(), nil); err != nil {
				w.WriteHeader(http.StatusServiceUnavailable)
				fmt.Fprintf(w, "%s Database Not Ready\n", key)
				return
			}
		}

		w.WriteHeader(http.StatusOK)
		fmt.Fprintln(w, "Ready")
	})

	fmt.Println("Health Check Listening on :3000")
	http.ListenAndServe(":3000", nil)
}
