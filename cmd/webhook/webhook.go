package webhook

import (
	"strconv"

	"log"
	"net/http"

	"abs-scheduler-go/internal/callbacks"
	"abs-scheduler-go/internal/logistics"
	"abs-scheduler-go/internal/orders"
	"abs-scheduler-go/internal/products"
	"abs-scheduler-go/internal/site_config"
	"abs-scheduler-go/internal/stores"
	"abs-scheduler-go/internal/task_executions"
	"abs-scheduler-go/internal/webhooks"
	"abs-scheduler-go/pkg/config"
	"abs-scheduler-go/pkg/queue"
	"abs-scheduler-go/pkg/util"
	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/mongo"
)

func Run(db map[string]*mongo.Database) {
	webhookCtrl, err := InitializeController(db)
	if err != nil {
		log.Fatalf("Failed to initialize webhook controller: %v", err)
	}

	orderWebhookCtrl, err := InitializeOrderWebhookController(db)
	if err != nil {
		log.Fatalf("Failed to initialize order webhook controller: %v", err)
	}

	productWebhookCtrl, err := InitializeProductWebhookController(db)
	if err != nil {
		log.Fatalf("Failed to initialize product webhook controller: %v", err)
	}

	logisticWebhookCtrl, err := InitializeLogisticWebhookController(db)
	if err != nil {
		log.Fatalf("Failed to initialize logistic webhook controller: %v", err)
	}

	routers := webhooks.NewRoutes(webhooks.Routes{
		WebhookController:         webhookCtrl,
		OrderWebhookController:    orderWebhookCtrl,
		ProductWebhookController:  productWebhookCtrl,
		LogisticWebhookController: logisticWebhookCtrl,
	})

	// Setup routes
	router := routers.SetupRoutes()

	// Start server
	log.Println("Server running on port 8080")
	log.Fatal(http.ListenAndServe(":8080", router))
}

// InitializeServicesAndControllers sets up the services and controllers
func InitializeController(db map[string]*mongo.Database) (*webhooks.WebhookController, error) {

	webhookRepo := webhooks.NewWebhookRepository(db)

	webhookService := webhooks.NewWebhookService(webhookRepo)

	webhookController := webhooks.NewWebhookController(webhookService)

	return webhookController, nil
}

func InitializeOrderWebhookController(db map[string]*mongo.Database) (*webhooks.OrderWebhookController, error) {
	redisClient, redisPass := util.GetRedisEnv()
	redisDb := config.RedisDb
	if redisDb == "" {
		redisDb = "0"
	}
	redisDbParsed, err := strconv.Atoi(redisDb)
	if err != nil {
		log.Fatal("REDIS_DB_EXECUTION environment invalid value")
	}
	client := redis.NewClient(&redis.Options{
		Addr:     redisClient,
		Password: redisPass,
		DB:       redisDbParsed,
	})

	orderServ := orders.NewOrderRepository(db)
	taskExecServ := task_executions.NewTaskExecutionRepository(db)
	storeServ := stores.NewStoreRepository(db)
	productServ := products.NewProductRepository(db)
	queueServ := queue.NewService(client)
	callbackServ := callbacks.NewCallbackUrlRepository(db)
	siteConfigServ := site_config.NewSiteConfigRepository(db)
	logisticServ := logistics.NewLogisticsRepository(db)

	return webhooks.NewOrderWebhookController(webhooks.OrderWebhookController{
		OrderServ:      orderServ,
		TaskExecServ:   taskExecServ,
		StoreServ:      storeServ,
		ProductServ:    productServ,
		QueueServ:      queueServ,
		CallbackServ:   callbackServ,
		SiteConfigServ: siteConfigServ,
		LogisticServ:   logisticServ,
	}), nil
}

func InitializeProductWebhookController(db map[string]*mongo.Database) (*webhooks.ProductWebhookController, error) {
	redisClient, redisPass := util.GetRedisEnv()
	redisDb := config.RedisDb
	if redisDb == "" {
		redisDb = "0"
	}
	redisDbParsed, err := strconv.Atoi(redisDb)
	if err != nil {
		log.Fatal("REDIS_DB_EXECUTION environment invalid value")
	}
	client := redis.NewClient(&redis.Options{
		Addr:     redisClient,
		Password: redisPass,
		DB:       redisDbParsed,
	})

	productServ := products.NewProductRepository(db)
	taskExecServ := task_executions.NewTaskExecutionRepository(db)
	storeServ := stores.NewStoreRepository(db)
	queueServ := queue.NewService(client)

	return webhooks.NewProductWebhookController(webhooks.ProductWebhookController{
		ProductServ:  productServ,
		TaskExecServ: taskExecServ,
		StoreServ:    storeServ,
		QueueServ:    queueServ,
	}), nil
}

func InitializeLogisticWebhookController(db map[string]*mongo.Database) (*webhooks.LogisticWebhookController, error) {
	logisticServ := logistics.NewLogisticsRepository(db)
	storeServ := stores.NewStoreRepository(db)

	return webhooks.NewLogisticWebhookController(webhooks.LogisticWebhookController{
		StoreServ:    storeServ,
		LogisticServ: logisticServ,
	}), nil
}
