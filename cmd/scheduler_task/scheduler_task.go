package scheduler_task

import (
	"log"
	"strconv"
	"time"

	"abs-scheduler-go/internal/scheduler_tasks"
	"abs-scheduler-go/internal/stores"
	"abs-scheduler-go/internal/task_executions"
	"abs-scheduler-go/internal/webhooks"
	"abs-scheduler-go/pkg/config"
	"abs-scheduler-go/pkg/queue"
	"abs-scheduler-go/pkg/util"
	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/mongo"
)

func Run(db map[string]*mongo.Database) {
	running := false

	redisClient, redisPass := util.GetRedisEnv()
	redisDb := config.RedisDb
	if redisDb == "" {
		redisDb = "0"
	}
	redisDbParsed, err := strconv.Atoi(redisDb)
	if err != nil {
		log.Fatal("REDIS_DB_EXECUTION environment invalid value")
	}
	client := redis.NewClient(&redis.Options{
		Addr:     redisClient,
		Password: redisPass,
		DB:       redisDbParsed,
	})

	queueService := queue.NewService(client)

	webhookRepo := webhooks.NewWebhookRepository(db)
	webhookServ := webhooks.NewWebhookService(webhookRepo)

	taskServ := task_executions.NewTaskExecutionRepository(db)
	storeServ := stores.NewStoreRepository(db)

	ctrl := scheduler_tasks.NewSchedulerTaskCtrl(scheduler_tasks.SchedulerTaskController{
		Db:           db,
		QueueServ:    queueService,
		WebhookServ:  webhookServ,
		TaskExecServ: taskServ,
		StoreServ:    storeServ,
		Redis:        client,
	})

	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	go func() {
		for range ticker.C {
			if running == false {
				ctrl.RunScheduler(&running)
			}
		}
	}()

	running2 := false
	ticker2 := time.NewTicker(1 * time.Second)
	defer ticker2.Stop()

	go func() {
		for range ticker2.C {
			if running2 == false {
				ctrl.InsertWebhookToQueue(&running2)
			}
		}
	}()

	select {}
}
