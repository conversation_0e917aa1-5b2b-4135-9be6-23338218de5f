package callback

import (
	"abs-scheduler-go/internal/callbacks"
	"abs-scheduler-go/pkg/queue"
	"abs-scheduler-go/pkg/redis"
	"go.mongodb.org/mongo-driver/mongo"
)

func Run(db map[string]*mongo.Database) {
	client := redis.Connect()

	cbRepo := callbacks.NewCallbackUrlRepository(db)
	queueService := queue.NewService(client)

	ctrl := callbacks.NewCallbackRunner(callbacks.CallbackRunner{
		CallbackRepo: cbRepo,
		QueueServ:    queueService,
		Redis:        client,
	})

	go ctrl.ListToStream()
	go ctrl.RunStream()

	select {}
}
