package task_execution

import (
	"abs-scheduler-go/internal/callbacks"
	"abs-scheduler-go/internal/dead_letter_queues"
	"abs-scheduler-go/internal/logistics"
	"abs-scheduler-go/internal/orders"
	"abs-scheduler-go/internal/products"
	"abs-scheduler-go/internal/scheduler_tasks"
	"abs-scheduler-go/internal/site_config"
	"abs-scheduler-go/internal/stores"
	"abs-scheduler-go/internal/task_executions"
	"abs-scheduler-go/pkg/marketplaces/shopee"
	"abs-scheduler-go/pkg/queue"
	"abs-scheduler-go/pkg/redis"
	"go.mongodb.org/mongo-driver/mongo"
)

func Run(db map[string]*mongo.Database) {
	client := redis.Connect()

	taskExecutionRepo := task_executions.NewTaskExecutionRepository(db)
	storeRepo := stores.NewStoreRepository(db)
	siteConfigRepo := site_config.NewSiteConfigRepository(db)
	failedRepo := dead_letter_queues.NewDeadLetterQueuesRepository(db)
	orderRepo := orders.NewOrderRepository(db)
	schedulerRepo := scheduler_tasks.NewSchedulerTaskRepository(db)
	callbackRepo := callbacks.NewCallbackUrlRepository(db)
	productRepo := products.NewProductRepository(db)
	logisticsRepo := logistics.NewLogisticsRepository(db)
	queueService := queue.NewService(client)

	shopeeExecutor := shopee.NewShopeeExecutor(shopee.Executor{
		Db:                db,
		Redis:             client,
		TaskExecutionRepo: taskExecutionRepo,
		StoreRepo:         storeRepo,
		SiteConfigRepo:    siteConfigRepo,
		ServiceFailed:     failedRepo,
		QueueService:      queueService,
		OrderRepo:         orderRepo,
		CallbackRepo:      callbackRepo,
		ProductRepo:       productRepo,
		LogisticRepo:      logisticsRepo,
	})

	ctrl := task_executions.NewTaskExecutionCtrl(task_executions.TaskExecutionController{
		Db:            db,
		QueueServ:     queueService,
		Repo:          taskExecutionRepo,
		Redis:         client,
		ServiceFailed: failedRepo,
		OrderRepo:     orderRepo,
		SchedulerRepo: schedulerRepo,
		Executor: task_executions.Executor{
			Shopee: shopeeExecutor,
		},
	})

	queueService.InitQueue()

	go ctrl.RunQueue()
	go ctrl.StreamFailedEvents()

	select {}
}
