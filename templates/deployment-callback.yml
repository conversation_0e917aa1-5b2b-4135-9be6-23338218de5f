apiVersion: apps/v1 #  for k8s versions before 1.9.0 use apps/v1beta2  and before 1.8.0 use extensions/v1beta1
kind: Deployment
metadata:
  # This name uniquely identifies the Deployment
  name: be-api-mpa-callback
  annotations:
    secrets.infisical.com/auto-reload: "true"
spec:
  selector:
    matchLabels:
      app: be-api-mpa-callback
  replicas: 1
  revisionHistoryLimit: 3 
  template:
    metadata:
      annotations:
        co.elastic.logs/enabled: "true"
        co.elastic.logs/exclude_lines: '.*VERBOSE.*'
      labels:
        # Label is used as selector in the service.
        app: be-api-mpa-callback
    spec:
      # Refer to the PVC created earlier
      imagePullSecrets:
      - name: tbsi-jfrog
      topologySpreadConstraints:
        - maxSkew: 1
          topologyKey: kubernetes.io/hostname
          whenUnsatisfiable: ScheduleAnyway
          labelSelector:
            matchLabels:
              app: be-api-mpa-callback
      containers:
      - name: be-api-mpa-callback
        image: {{ image_registry }}/{{ image_id }}
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
        envFrom:
          - secretRef:
              name: mpa-secret
        env:
          - name: MODULES
            value: callback
        resources:
         limits:
           cpu: 500m
           memory: 256Mi
         requests:
           cpu: 250m
           memory: 128Mi
        readinessProbe:
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 1
          successThreshold: 2
          failureThreshold: 1
          httpGet:
            host:
            scheme: HTTP
            path: /readyz
            port: 3000
        livenessProbe:
          initialDelaySeconds: 15
          periodSeconds: 15
          timeoutSeconds: 1
          successThreshold: 1
          failureThreshold: 4
          httpGet:
            host:
            scheme: HTTP
            path: /health-check
            port: 3000