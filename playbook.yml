- hosts: localhost
  tasks:
  - name: Deploy the application executor
    k8s:
      state: present
      validate_certs: no
      namespace: "{{ namespace }}"
      definition: "{{ lookup('template', 'deployment-executor.yml') | from_yaml }}"
  - name: Deploy the application webhook
    k8s:
      state: present
      validate_certs: no
      namespace: "{{ namespace }}"
      definition: "{{ lookup('template', 'deployment-webhook.yml') | from_yaml }}"
  - name: Deploy the application scheduler
    k8s:
      state: present
      validate_certs: no
      namespace: "{{ namespace }}"
      definition: "{{ lookup('template', 'deployment-scheduler.yml') | from_yaml }}"
  - name: Deploy the application callback
    k8s:
      state: present
      validate_certs: no
      namespace: "{{ namespace }}"
      definition: "{{ lookup('template', 'deployment-callback.yml') | from_yaml }}"