package main

import (
	"context"
	"fmt"
	"log"
	"strconv"
	"time"

	"abs-scheduler-go/pkg/config"
	"abs-scheduler-go/pkg/util"
	"github.com/redis/go-redis/v9"
)

const (
	WaitingStream = "scheduler:task_execution:events"
	FailedStream  = "scheduler:task_execution:dead_letter_queue_events"
)

func main() {
	// Load environment variables
	config.Init()

	// Connect to Redis
	redisClient, redisPass := util.GetRedisEnv()
	redisDb := config.RedisDb
	if redisDb == "" {
		redisDb = "0"
	}
	redisDbParsed, err := strconv.Atoi(redisDb)
	if err != nil {
		log.Fatal("REDIS_DB environment invalid value")
	}

	client := redis.NewClient(&redis.Options{
		Addr:     redisClient,
		Password: redisPass,
		DB:       redisDbParsed,
	})

	ctx := context.Background()

	fmt.Println("Task Execution Stream Issue Fix")
	fmt.Println("===============================")

	// Step 1: Analyze the current state
	analyzeCurrentState(ctx, client)

	// Step 2: Clean up problematic entries
	fmt.Println("\nStep 2: Cleaning up problematic entries...")
	cleanupProblematicEntries(ctx, client)

	// Step 3: Reset consumer group if needed
	fmt.Println("\nStep 3: Resetting consumer group...")
	resetConsumerGroup(ctx, client)

	// Step 4: Verify the fix
	fmt.Println("\nStep 4: Verifying the fix...")
	analyzeCurrentState(ctx, client)

	fmt.Println("\n✅ Fix completed! Monitor the system to ensure it's working properly.")
}

func analyzeCurrentState(ctx context.Context, client *redis.Client) {
	fmt.Println("\nAnalyzing current stream state...")

	// Check waiting stream
	info, err := client.XInfoStream(ctx, WaitingStream).Result()
	if err != nil {
		fmt.Printf("❌ Error getting stream info: %v\n", err)
		return
	}

	fmt.Printf("📊 Stream length: %d\n", info.Length)

	// Sample recent entries to understand the issue
	entries, err := client.XRevRange(ctx, WaitingStream, "+", "-").Result()
	if err != nil {
		fmt.Printf("❌ Error getting entries: %v\n", err)
		return
	}

	eventCounts := make(map[string]int)
	platformCounts := make(map[string]int)
	
	// Analyze first 1000 entries
	sampleSize := 1000
	if len(entries) < sampleSize {
		sampleSize = len(entries)
	}

	for i := 0; i < sampleSize; i++ {
		entry := entries[i]
		if event, ok := entry.Values["event"].(string); ok {
			eventCounts[event]++
		}
		if platform, ok := entry.Values["platform"].(string); ok {
			platformCounts[platform]++
		}
	}

	fmt.Printf("📈 Event distribution (last %d entries):\n", sampleSize)
	for event, count := range eventCounts {
		fmt.Printf("  %s: %d\n", event, count)
	}

	// Check consumer group
	groups, err := client.XInfoGroups(ctx, WaitingStream).Result()
	if err != nil {
		fmt.Printf("❌ Error getting consumer groups: %v\n", err)
		return
	}

	for _, group := range groups {
		fmt.Printf("👥 Consumer group '%s': %d consumers, %d pending\n", 
			group.Name, group.Consumers, group.Pending)
	}
}

func cleanupProblematicEntries(ctx context.Context, client *redis.Client) {
	// Get stream info
	info, err := client.XInfoStream(ctx, WaitingStream).Result()
	if err != nil {
		fmt.Printf("❌ Error getting stream info: %v\n", err)
		return
	}

	originalLength := info.Length
	fmt.Printf("📊 Original stream length: %d\n", originalLength)

	// If stream is too large, trim it aggressively
	if originalLength > 50000 {
		// Keep only the last 10000 entries
		targetLength := int64(10000)
		
		trimmed, err := client.XTrimMaxLen(ctx, WaitingStream, targetLength).Result()
		if err != nil {
			fmt.Printf("❌ Error trimming stream: %v\n", err)
			return
		}

		fmt.Printf("✂️  Trimmed %d entries, keeping last %d\n", trimmed, targetLength)
	}

	// Clean up completed and failed entries that are older than 1 hour
	oneHourAgo := time.Now().Add(-1 * time.Hour)
	cutoffID := fmt.Sprintf("%d-0", oneHourAgo.UnixMilli())

	// Get entries older than cutoff
	oldEntries, err := client.XRange(ctx, WaitingStream, "-", cutoffID).Result()
	if err != nil {
		fmt.Printf("❌ Error getting old entries: %v\n", err)
		return
	}

	// Remove old completed/failed entries
	removedCount := 0
	for _, entry := range oldEntries {
		if event, ok := entry.Values["event"].(string); ok {
			if event == "completed" || event == "failed" {
				err := client.XDel(ctx, WaitingStream, entry.ID).Err()
				if err != nil {
					fmt.Printf("⚠️  Error removing entry %s: %v\n", entry.ID, err)
				} else {
					removedCount++
				}
			}
		}
	}

	if removedCount > 0 {
		fmt.Printf("🗑️  Removed %d old completed/failed entries\n", removedCount)
	}
}

func resetConsumerGroup(ctx context.Context, client *redis.Client) {
	groupName := "task_execution"

	// Get consumer group info
	groups, err := client.XInfoGroups(ctx, WaitingStream).Result()
	if err != nil {
		fmt.Printf("❌ Error getting consumer groups: %v\n", err)
		return
	}

	var targetGroup *redis.XInfoGroup
	for _, group := range groups {
		if group.Name == groupName {
			targetGroup = &group
			break
		}
	}

	if targetGroup == nil {
		fmt.Printf("⚠️  Consumer group '%s' not found\n", groupName)
		return
	}

	// If there are too many pending messages, reset the consumer group
	if targetGroup.Pending > 1000 {
		fmt.Printf("🔄 Resetting consumer group due to high pending count (%d)\n", targetGroup.Pending)

		// Get all consumers
		consumers, err := client.XInfoConsumers(ctx, WaitingStream, groupName).Result()
		if err != nil {
			fmt.Printf("❌ Error getting consumers: %v\n", err)
			return
		}

		// Remove all consumers
		for _, consumer := range consumers {
			err := client.XGroupDelConsumer(ctx, WaitingStream, groupName, consumer.Name).Err()
			if err != nil {
				fmt.Printf("⚠️  Error removing consumer %s: %v\n", consumer.Name, err)
			} else {
				fmt.Printf("🗑️  Removed consumer: %s\n", consumer.Name)
			}
		}

		// Reset the consumer group to the latest message
		err = client.XGroupSetID(ctx, WaitingStream, groupName, "$").Err()
		if err != nil {
			fmt.Printf("❌ Error resetting consumer group: %v\n", err)
		} else {
			fmt.Printf("✅ Consumer group reset to latest message\n")
		}
	} else {
		fmt.Printf("✅ Consumer group pending count is acceptable (%d)\n", targetGroup.Pending)
	}
}
