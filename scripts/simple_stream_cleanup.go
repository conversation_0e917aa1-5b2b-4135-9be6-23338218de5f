package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"strconv"

	"github.com/redis/go-redis/v9"
)

const (
	WaitingStream  = "scheduler:task_execution:events"
	FailedStream   = "scheduler:task_execution:dead_letter_queue_events"
	CompleteStream = "scheduler:task_execution:complete_events"
	CallbackStream = "callback:events"
)

func main() {
	// Get Redis connection details from environment or use defaults
	redisURL := os.Getenv("REDIS_URL")
	redisPort := os.Getenv("REDIS_PORT")
	if redisURL == "" {
		redisURL = "redis.development.tbsgroup.co.id"
	}
	if redisPort == "" {
		redisPort = "6379"
	}

	redisAddr := redisURL + ":" + redisPort
	redisPass := os.Getenv("REDIS_PASS")
	redisDbStr := os.Getenv("REDIS_DB")
	if redisDbStr == "" {
		redisDbStr = "12" // Default from .env file
	}

	redisDb, err := strconv.Atoi(redisDbStr)
	if err != nil {
		log.Fatal("Invalid REDIS_DB value")
	}

	fmt.Printf("Connecting to Redis at %s, DB %d\n", redisAddr, redisDb)

	// Connect to Redis
	client := redis.NewClient(&redis.Options{
		Addr:     redisAddr,
		Password: redisPass,
		DB:       redisDb,
	})

	ctx := context.Background()

	// Test connection
	_, err = client.Ping(ctx).Result()
	if err != nil {
		log.Fatalf("Failed to connect to Redis: %v", err)
	}

	// Check if we should perform cleanup or just show stats
	if len(os.Args) > 1 && os.Args[1] == "cleanup" {
		performCleanup(ctx, client)
	} else {
		showStreamStats(ctx, client)
	}
}

func showStreamStats(ctx context.Context, client *redis.Client) {
	streams := []string{WaitingStream, FailedStream, CompleteStream, CallbackStream}

	fmt.Println("Redis Stream Statistics:")
	fmt.Println("========================")

	for _, stream := range streams {
		info, err := client.XInfoStream(ctx, stream).Result()
		if err != nil {
			fmt.Printf("❌ %s: Error - %v\n", stream, err)
			continue
		}

		fmt.Printf("📊 %s:\n", stream)
		fmt.Printf("   Length: %d entries\n", info.Length)
		fmt.Printf("   Groups: %d\n", info.Groups)
		fmt.Printf("   First Entry: %s\n", info.FirstEntry.ID)
		fmt.Printf("   Last Entry: %s\n", info.LastEntry.ID)

		// Check consumer groups
		groups, err := client.XInfoGroups(ctx, stream).Result()
		if err != nil {
			fmt.Printf("   ❌ Error getting groups: %v\n", err)
		} else {
			for _, group := range groups {
				fmt.Printf("   Group '%s': %d consumers, %d pending\n",
					group.Name, group.Consumers, group.Pending)
			}
		}
		fmt.Println()
	}

	fmt.Println("To perform cleanup, run: go run scripts/simple_stream_cleanup.go cleanup")
}

func performCleanup(ctx context.Context, client *redis.Client) {
	streams := []string{WaitingStream, FailedStream, CompleteStream, CallbackStream}

	fmt.Println("Starting Redis Stream Cleanup...")
	fmt.Println("================================")

	for _, stream := range streams {
		fmt.Printf("\nCleaning stream: %s\n", stream)

		// Get stream info
		info, err := client.XInfoStream(ctx, stream).Result()
		if err != nil {
			fmt.Printf("Error getting stream info: %v\n", err)
			continue
		}

		originalLength := info.Length
		fmt.Printf("Original length: %d\n", originalLength)

		if originalLength == 0 {
			fmt.Printf("Stream is empty, skipping\n")
			continue
		}

		// For very large streams, trim more aggressively
		var targetLength int64
		if originalLength > 50000 {
			targetLength = 5000 // Keep only last 5000 entries
		} else if originalLength > 10000 {
			targetLength = originalLength / 4 // Keep 25%
		} else {
			targetLength = originalLength / 2 // Keep 50%
		}

		// Trim the stream
		trimmed, err := client.XTrimMaxLen(ctx, stream, targetLength).Result()
		if err != nil {
			fmt.Printf("Error trimming stream: %v\n", err)
			continue
		}

		fmt.Printf("Trimmed %d entries, kept %d entries\n", trimmed, targetLength)

		// Get new length
		newInfo, err := client.XInfoStream(ctx, stream).Result()
		if err != nil {
			fmt.Printf("Error getting new stream info: %v\n", err)
			continue
		}

		fmt.Printf("New length: %d\n", newInfo.Length)
		fmt.Printf("Reduction: %d entries (%.1f%%)\n",
			originalLength-newInfo.Length,
			float64(originalLength-newInfo.Length)/float64(originalLength)*100)
	}

	fmt.Println("\nCleanup completed!")
	fmt.Println("Note: Consumer groups and their state have been preserved.")
}
