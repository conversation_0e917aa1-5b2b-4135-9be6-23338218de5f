package main

import (
	"context"
	"fmt"
	"log"
	"strconv"
	"time"

	"abs-scheduler-go/internal/task_executions"
	"abs-scheduler-go/pkg/config"
	"abs-scheduler-go/pkg/database"
	"abs-scheduler-go/pkg/util"

	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	WaitingStream  = "scheduler:task_execution:events"
	FailedStream   = "scheduler:task_execution:dead_letter_queue_events"
	CallbackStream = "callback:events"
)

func main() {
	// Load environment variables
	config.Init()

	// Connect to databases
	db := database.Init()
	taskRepo := task_executions.NewTaskExecutionRepository(db)

	// Connect to Redis
	redisClient, redisPass := util.GetRedisEnv()
	redisDb := config.RedisDb
	if redisDb == "" {
		redisDb = "0"
	}
	redisDbParsed, err := strconv.Atoi(redisDb)
	if err != nil {
		log.Fatal("REDIS_DB environment invalid value")
	}

	client := redis.NewClient(&redis.Options{
		Addr:     redisClient,
		Password: redisPass,
		DB:       redisDbParsed,
	})

	ctx := context.Background()

	// Run monitoring
	runMonitoring(ctx, client, taskRepo)
}

func runMonitoring(ctx context.Context, client *redis.Client, taskRepo *task_executions.Repository) {
	fmt.Println("Task Execution System Health Monitor")
	fmt.Println("===================================")

	// Check Redis stream health
	checkStreamHealth(ctx, client)

	// Check MongoDB task execution status
	checkTaskExecutionStatus(taskRepo)

	// Check for stuck jobs
	checkStuckJobs(ctx, client, taskRepo)

	// Check consumer group health
	checkConsumerGroupHealth(ctx, client)
}

func checkStreamHealth(ctx context.Context, client *redis.Client) {
	fmt.Println("\n1. Redis Stream Health:")
	fmt.Println("-----------------------")

	streams := []string{WaitingStream, FailedStream, CallbackStream}

	for _, stream := range streams {
		info, err := client.XInfoStream(ctx, stream).Result()
		if err != nil {
			fmt.Printf("❌ %s: Error - %v\n", stream, err)
			continue
		}

		status := "✅ Healthy"
		if info.Length > 10000 {
			status = "⚠️  High volume"
		}
		if info.Length > 100000 {
			status = "❌ Critical volume"
		}

		fmt.Printf("%s %s: %d entries\n", status, stream, info.Length)
	}
}

func checkTaskExecutionStatus(taskRepo *task_executions.Repository) {
	fmt.Println("\n2. Task Execution Status:")
	fmt.Println("-------------------------")

	// Count tasks by status
	statuses := []string{"WAITING", "START", "SUCCESS", "FAILED", "PARTIAL_SUCCESS", "FAILED_TO_QUEUE"}

	for _, status := range statuses {
		filter := bson.M{"status": status}
		tasks, err := taskRepo.GetMany(filter)
		if err != nil {
			fmt.Printf("❌ Error counting %s tasks: %v\n", status, err)
			continue
		}

		statusIcon := "✅"
		if status == "FAILED" || status == "FAILED_TO_QUEUE" {
			statusIcon = "❌"
		} else if status == "WAITING" || status == "START" {
			statusIcon = "⏳"
		}

		fmt.Printf("%s %s: %d tasks\n", statusIcon, status, len(tasks))
	}

	// Check for old stuck tasks
	oneDayAgo := primitive.NewDateTimeFromTime(time.Now().Add(-24 * time.Hour))
	stuckFilter := bson.M{
		"status":    bson.M{"$in": []string{"WAITING", "START"}},
		"createdAt": bson.M{"$lt": oneDayAgo},
	}
	stuckTasks, err := taskRepo.GetMany(stuckFilter)
	if err != nil {
		fmt.Printf("❌ Error checking stuck tasks: %v\n", err)
	} else {
		if len(stuckTasks) > 0 {
			fmt.Printf("⚠️  Old stuck tasks (>24h): %d\n", len(stuckTasks))
		} else {
			fmt.Printf("✅ No old stuck tasks\n")
		}
	}
}

func checkStuckJobs(ctx context.Context, client *redis.Client, taskRepo *task_executions.Repository) {
	fmt.Println("\n3. Stuck Jobs Analysis:")
	fmt.Println("-----------------------")

	// Get pending messages from consumer groups
	pending, err := client.XPendingExt(ctx, &redis.XPendingExtArgs{
		Stream: WaitingStream,
		Group:  "task_execution",
		Start:  "-",
		End:    "+",
		Count:  100,
	}).Result()

	if err != nil {
		fmt.Printf("❌ Error checking pending messages: %v\n", err)
		return
	}

	stuckCount := 0
	for _, p := range pending {
		if p.Idle > 5*time.Minute {
			stuckCount++
		}
	}

	if stuckCount > 0 {
		fmt.Printf("⚠️  Stuck jobs (idle >5min): %d\n", stuckCount)
	} else {
		fmt.Printf("✅ No stuck jobs detected\n")
	}

	fmt.Printf("📊 Total pending messages: %d\n", len(pending))
}

func checkConsumerGroupHealth(ctx context.Context, client *redis.Client) {
	fmt.Println("\n4. Consumer Group Health:")
	fmt.Println("-------------------------")

	groups, err := client.XInfoGroups(ctx, WaitingStream).Result()
	if err != nil {
		fmt.Printf("❌ Error getting consumer groups: %v\n", err)
		return
	}

	for _, group := range groups {
		fmt.Printf("Group: %s\n", group.Name)
		fmt.Printf("  Consumers: %d\n", group.Consumers)
		fmt.Printf("  Pending: %d\n", group.Pending)
		fmt.Printf("  Last Delivered: %s\n", group.LastDeliveredID)

		// Get consumer details
		consumers, err := client.XInfoConsumers(ctx, WaitingStream, group.Name).Result()
		if err != nil {
			fmt.Printf("  ❌ Error getting consumers: %v\n", err)
			continue
		}

		activeConsumers := 0
		for _, consumer := range consumers {
			if consumer.Idle < 5*time.Minute {
				activeConsumers++
			}
		}

		status := "✅ Healthy"
		if activeConsumers == 0 {
			status = "❌ No active consumers"
		} else if group.Pending > 100 {
			status = "⚠️  High pending count"
		}

		fmt.Printf("  %s (Active consumers: %d)\n", status, activeConsumers)
	}
}
