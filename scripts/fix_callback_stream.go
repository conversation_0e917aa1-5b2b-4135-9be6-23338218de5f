package main

import (
	"context"
	"fmt"
	"log"
	"strconv"
	"time"

	"abs-scheduler-go/pkg/config"
	"abs-scheduler-go/pkg/util"
	"github.com/redis/go-redis/v9"
)

const (
	CallbackStream = "callback:events"
)

func main() {
	// Load environment variables
	config.Init()

	// Connect to Redis
	redisClient, redisPass := util.GetRedisEnv()
	redisDb := config.RedisDb
	if redisDb == "" {
		redisDb = "0"
	}
	redisDbParsed, err := strconv.Atoi(redisDb)
	if err != nil {
		log.Fatal("REDIS_DB environment invalid value")
	}

	client := redis.NewClient(&redis.Options{
		Addr:     redisClient,
		Password: redisPass,
		DB:       redisDbParsed,
	})

	ctx := context.Background()

	fmt.Println("Callback Stream Issue Fix")
	fmt.Println("=========================")

	// Step 1: Analyze the current state
	analyzeCallbackStream(ctx, client)

	// Step 2: Clean up problematic entries
	fmt.Println("\nStep 2: Cleaning up callback stream...")
	cleanupCallbackStream(ctx, client)

	// Step 3: Reset consumer group if needed
	fmt.Println("\nStep 3: Resetting callback consumer group...")
	resetCallbackConsumerGroup(ctx, client)

	// Step 4: Verify the fix
	fmt.Println("\nStep 4: Verifying the fix...")
	analyzeCallbackStream(ctx, client)

	fmt.Println("\n✅ Callback stream fix completed!")
}

func analyzeCallbackStream(ctx context.Context, client *redis.Client) {
	fmt.Println("\nAnalyzing callback stream state...")

	// Check callback stream
	info, err := client.XInfoStream(ctx, CallbackStream).Result()
	if err != nil {
		fmt.Printf("❌ Error getting callback stream info: %v\n", err)
		return
	}

	fmt.Printf("📊 Callback stream length: %d\n", info.Length)

	// Sample recent entries
	entries, err := client.XRevRange(ctx, CallbackStream, "+", "-").Result()
	if err != nil {
		fmt.Printf("❌ Error getting callback entries: %v\n", err)
		return
	}

	eventCounts := make(map[string]int)
	nameCounts := make(map[string]int)
	
	// Analyze first 1000 entries
	sampleSize := 1000
	if len(entries) < sampleSize {
		sampleSize = len(entries)
	}

	for i := 0; i < sampleSize; i++ {
		entry := entries[i]
		if event, ok := entry.Values["event"].(string); ok {
			eventCounts[event]++
		}
		if name, ok := entry.Values["name"].(string); ok {
			nameCounts[name]++
		}
	}

	fmt.Printf("📈 Event distribution (last %d entries):\n", sampleSize)
	for event, count := range eventCounts {
		fmt.Printf("  %s: %d\n", event, count)
	}

	fmt.Printf("📈 Name distribution (last %d entries):\n", sampleSize)
	for name, count := range nameCounts {
		fmt.Printf("  %s: %d\n", name, count)
	}

	// Check consumer group
	groups, err := client.XInfoGroups(ctx, CallbackStream).Result()
	if err != nil {
		fmt.Printf("❌ Error getting callback consumer groups: %v\n", err)
		return
	}

	for _, group := range groups {
		fmt.Printf("👥 Consumer group '%s': %d consumers, %d pending\n", 
			group.Name, group.Consumers, group.Pending)
	}
}

func cleanupCallbackStream(ctx context.Context, client *redis.Client) {
	// Get stream info
	info, err := client.XInfoStream(ctx, CallbackStream).Result()
	if err != nil {
		fmt.Printf("❌ Error getting callback stream info: %v\n", err)
		return
	}

	originalLength := info.Length
	fmt.Printf("📊 Original callback stream length: %d\n", originalLength)

	// If stream is too large, trim it aggressively
	if originalLength > 50000 {
		// Keep only the last 5000 entries for callbacks
		targetLength := int64(5000)
		
		trimmed, err := client.XTrimMaxLen(ctx, CallbackStream, targetLength).Result()
		if err != nil {
			fmt.Printf("❌ Error trimming callback stream: %v\n", err)
			return
		}

		fmt.Printf("✂️  Trimmed %d callback entries, keeping last %d\n", trimmed, targetLength)
	}

	// Clean up old completed/failed callback entries
	oneHourAgo := time.Now().Add(-1 * time.Hour)
	cutoffID := fmt.Sprintf("%d-0", oneHourAgo.UnixMilli())

	// Get old entries
	oldEntries, err := client.XRange(ctx, CallbackStream, "-", cutoffID).Result()
	if err != nil {
		fmt.Printf("❌ Error getting old callback entries: %v\n", err)
		return
	}

	// Remove old completed/failed entries
	removedCount := 0
	for _, entry := range oldEntries {
		if event, ok := entry.Values["event"].(string); ok {
			if event == "completed" || event == "failed" {
				err := client.XDel(ctx, CallbackStream, entry.ID).Err()
				if err != nil {
					fmt.Printf("⚠️  Error removing callback entry %s: %v\n", entry.ID, err)
				} else {
					removedCount++
				}
			}
		}
	}

	if removedCount > 0 {
		fmt.Printf("🗑️  Removed %d old callback entries\n", removedCount)
	}
}

func resetCallbackConsumerGroup(ctx context.Context, client *redis.Client) {
	groupName := "callback"

	// Get consumer group info
	groups, err := client.XInfoGroups(ctx, CallbackStream).Result()
	if err != nil {
		fmt.Printf("❌ Error getting callback consumer groups: %v\n", err)
		return
	}

	var targetGroup *redis.XInfoGroup
	for _, group := range groups {
		if group.Name == groupName {
			targetGroup = &group
			break
		}
	}

	if targetGroup == nil {
		fmt.Printf("⚠️  Callback consumer group '%s' not found\n", groupName)
		return
	}

	// If there are too many pending messages, reset the consumer group
	if targetGroup.Pending > 500 {
		fmt.Printf("🔄 Resetting callback consumer group due to high pending count (%d)\n", targetGroup.Pending)

		// Get all consumers
		consumers, err := client.XInfoConsumers(ctx, CallbackStream, groupName).Result()
		if err != nil {
			fmt.Printf("❌ Error getting callback consumers: %v\n", err)
			return
		}

		// Remove all consumers
		for _, consumer := range consumers {
			err := client.XGroupDelConsumer(ctx, CallbackStream, groupName, consumer.Name).Err()
			if err != nil {
				fmt.Printf("⚠️  Error removing callback consumer %s: %v\n", consumer.Name, err)
			} else {
				fmt.Printf("🗑️  Removed callback consumer: %s\n", consumer.Name)
			}
		}

		// Reset the consumer group to the latest message
		err = client.XGroupSetID(ctx, CallbackStream, groupName, "$").Err()
		if err != nil {
			fmt.Printf("❌ Error resetting callback consumer group: %v\n", err)
		} else {
			fmt.Printf("✅ Callback consumer group reset to latest message\n")
		}
	} else {
		fmt.Printf("✅ Callback consumer group pending count is acceptable (%d)\n", targetGroup.Pending)
	}
}
