package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	"abs-scheduler-go/pkg/config"
	"abs-scheduler-go/pkg/util"

	"github.com/redis/go-redis/v9"
)

const (
	WaitingStream  = "scheduler:task_execution:events"
	FailedStream   = "scheduler:task_execution:dead_letter_queue_events"
	CompleteStream = "scheduler:task_execution:complete_events"
	CallbackStream = "callback:events"
)

func main() {
	// Load environment variables
	config.Init()

	// Connect to Redis
	redisClient, redisPass := util.GetRedisEnv()
	redisDb := config.RedisDb
	if redisDb == "" {
		redisDb = "0"
	}
	redisDbParsed, err := strconv.Atoi(redisDb)
	if err != nil {
		log.Fatal("REDIS_DB environment invalid value")
	}

	client := redis.NewClient(&redis.Options{
		Addr:     redisClient,
		Password: redisPass,
		DB:       redisDbParsed,
	})

	ctx := context.Background()

	// Check if we should perform cleanup or just show stats
	if len(os.Args) > 1 && os.Args[1] == "cleanup" {
		performCleanup(ctx, client)
	} else {
		showStreamStats(ctx, client)
	}
}

func showStreamStats(ctx context.Context, client *redis.Client) {
	streams := []string{WaitingStream, FailedStream, CompleteStream, CallbackStream}

	fmt.Println("Redis Stream Statistics:")
	fmt.Println("========================")

	for _, stream := range streams {
		info, err := client.XInfoStream(ctx, stream).Result()
		if err != nil {
			fmt.Printf("Stream %s: Error getting info - %v\n", stream, err)
			continue
		}

		fmt.Printf("\nStream: %s\n", stream)
		fmt.Printf("  Length: %d\n", info.Length)
		fmt.Printf("  First Entry ID: %s\n", info.FirstEntry.ID)
		fmt.Printf("  Last Entry ID: %s\n", info.LastEntry.ID)

		// Get consumer group info
		groups, err := client.XInfoGroups(ctx, stream).Result()
		if err != nil {
			fmt.Printf("  Consumer Groups: Error getting info - %v\n", err)
		} else {
			fmt.Printf("  Consumer Groups: %d\n", len(groups))
			for _, group := range groups {
				fmt.Printf("    - %s: consumers=%d, pending=%d\n",
					group.Name, group.Consumers, group.Pending)
			}
		}

		// Sample some recent entries to check their state
		entries, err := client.XRevRange(ctx, stream, "+", "-").Result()
		if err != nil {
			fmt.Printf("  Recent Entries: Error getting entries - %v\n", err)
		} else {
			eventCounts := make(map[string]int)
			platformCounts := make(map[string]int)

			// Count first 100 entries
			for i, entry := range entries {
				if i >= 100 {
					break
				}
				if event, ok := entry.Values["event"].(string); ok {
					eventCounts[event]++
				}
				if platform, ok := entry.Values["platform"].(string); ok {
					platformCounts[platform]++
				}
			}

			fmt.Printf("  Recent Entry States (last 100):\n")
			for event, count := range eventCounts {
				fmt.Printf("    %s: %d\n", event, count)
			}
			fmt.Printf("  Recent Entry Platforms (last 100):\n")
			for platform, count := range platformCounts {
				fmt.Printf("    %s: %d\n", platform, count)
			}
		}
	}

	fmt.Println("\nTo perform cleanup, run: go run scripts/redis_stream_cleanup.go cleanup")
}

func performCleanup(ctx context.Context, client *redis.Client) {
	streams := []string{WaitingStream, FailedStream, CompleteStream, CallbackStream}

	fmt.Println("Starting Redis Stream Cleanup...")
	fmt.Println("================================")

	for _, stream := range streams {
		fmt.Printf("\nCleaning stream: %s\n", stream)

		// Get stream info
		info, err := client.XInfoStream(ctx, stream).Result()
		if err != nil {
			fmt.Printf("Error getting stream info: %v\n", err)
			continue
		}

		originalLength := info.Length
		fmt.Printf("Original length: %d\n", originalLength)

		// Clean up old entries (older than 24 hours)
		cutoffTime := time.Now().Add(-24 * time.Hour)
		cutoffID := fmt.Sprintf("%d-0", cutoffTime.UnixMilli())

		// Trim the stream
		trimmed, err := client.XTrimMaxLen(ctx, stream, originalLength/2).Result()
		if err != nil {
			fmt.Printf("Error trimming stream: %v\n", err)
			continue
		}

		fmt.Printf("Trimmed %d entries\n", trimmed)

		// Get new length
		newInfo, err := client.XInfoStream(ctx, stream).Result()
		if err != nil {
			fmt.Printf("Error getting new stream info: %v\n", err)
			continue
		}

		fmt.Printf("New length: %d\n", newInfo.Length)
		fmt.Printf("Reduction: %d entries\n", originalLength-newInfo.Length)

		// Clean up consumer groups with no active consumers
		groups, err := client.XInfoGroups(ctx, stream).Result()
		if err != nil {
			fmt.Printf("Error getting consumer groups: %v\n", err)
			continue
		}

		for _, group := range groups {
			consumers, err := client.XInfoConsumers(ctx, stream, group.Name).Result()
			if err != nil {
				fmt.Printf("Error getting consumers for group %s: %v\n", group.Name, err)
				continue
			}

			// Remove idle consumers
			for _, consumer := range consumers {
				if consumer.Pending == 0 && consumer.Idle > 30*time.Minute {
					err := client.XGroupDelConsumer(ctx, stream, group.Name, consumer.Name).Err()
					if err != nil {
						fmt.Printf("Error removing idle consumer %s: %v\n", consumer.Name, err)
					} else {
						fmt.Printf("Removed idle consumer: %s\n", consumer.Name)
					}
				}
			}
		}
	}

	fmt.Println("\nCleanup completed!")
}
