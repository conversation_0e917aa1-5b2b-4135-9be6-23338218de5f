package main

import (
	"abs-scheduler-go/pkg/util"
	"encoding/json"
	"fmt"
	"log"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Simulate the DateTime conversion logic from orders.service.go
func testDateTimeConversion() {
	// Test cases that might cause the "cannot decode string into a DateTime" error
	testCases := map[string]interface{}{
		"shipment.0.pickupTime":     "2025-06-09T14:23:36Z",                    // RFC3339 without milliseconds
		"timeline.pickupTime":       "2025-06-09T09:38:59.319Z",                // RFC3339 with milliseconds (from primitive.DateTime)
		"shipment.0.deliveredTime":  primitive.NewDateTimeFromTime(time.Now()), // Already primitive.DateTime
		"timeline.cancellationTime": 1749453816291,                             // Unix timestamp in milliseconds
		"timeline.completeTime":     float64(1749453816),                       // Unix timestamp in seconds
		"orderStatus":               "SHIPPED",                                 // Non-DateTime field
		"shipment.0.trackingNumber": "ABC123",                                  // Non-DateTime field
	}

	fmt.Println("Testing DateTime conversion logic...")
	fmt.Println("=====================================")

	for key, item := range testCases {
		fmt.Printf("\nProcessing field %s with value: %v (type: %T)\n", key, item, item)

		// Check if item is already a primitive.DateTime, if so, keep it as is
		if _, ok := item.(primitive.DateTime); ok {
			fmt.Printf("✅ Field %s is already primitive.DateTime, keeping as is\n", key)
			continue
		}

		// Check if this field should be a DateTime based on field name patterns
		isDateTimeField := strings.Contains(key, "Time") ||
			strings.Contains(key, "pickupTime") ||
			strings.Contains(key, "deliveredTime") ||
			strings.Contains(key, "cancellationTime") ||
			strings.Contains(key, "completeTime") ||
			strings.Contains(key, "orderTime") ||
			strings.Contains(key, "payTime") ||
			strings.Contains(key, "shipByDate") ||
			strings.Contains(key, "requestCancellationTime")

		if isDateTimeField {
			fmt.Printf("🔍 Field %s identified as DateTime field\n", key)
			switch v := item.(type) {
			case string:
				// Try to parse string as RFC3339 time (with or without milliseconds)
				var valid time.Time
				var err error

				// Try RFC3339 with milliseconds first (e.g., "2025-06-09T09:38:59.319Z")
				if valid, err = time.Parse(time.RFC3339Nano, v); err != nil {
					// Fallback to RFC3339 without milliseconds (e.g., "2025-06-09T09:38:59Z")
					valid, err = time.Parse(time.RFC3339, v)
				}

				if err == nil && !valid.IsZero() {
					converted := primitive.NewDateTimeFromTime(valid)
					fmt.Printf("✅ Converted string %s to DateTime for field %s: %v\n", v, key, converted)
				} else {
					fmt.Printf("❌ Failed to parse string %s as time for field %s: %v\n", v, key, err)
				}
			case float64:
				// Handle Unix timestamp (seconds)
				if v > 0 {
					converted := primitive.NewDateTimeFromTime(time.Unix(int64(v), 0))
					fmt.Printf("✅ Converted Unix timestamp %f to DateTime for field %s: %v\n", v, key, converted)
				}
			case int64:
				// Handle Unix timestamp (seconds)
				if v > 0 {
					converted := primitive.NewDateTimeFromTime(time.Unix(v, 0))
					fmt.Printf("✅ Converted Unix timestamp %d to DateTime for field %s: %v\n", v, key, converted)
				}
			case int:
				// Handle Unix timestamp (seconds) - int type
				if v > 0 {
					converted := primitive.NewDateTimeFromTime(time.Unix(int64(v), 0))
					fmt.Printf("✅ Converted Unix timestamp %d to DateTime for field %s: %v\n", v, key, converted)
				}
			default:
				fmt.Printf("⚠️  Unknown type %T for DateTime field %s, keeping as is\n", item, key)
			}
		} else {
			fmt.Printf("ℹ️  Field %s is not a DateTime field, keeping as is\n", key)
		}
	}

	fmt.Println("\n=====================================")
	fmt.Println("DateTime conversion test completed!")
}

// Test what happens when primitive.DateTime goes through ProcessUpdateFields
func testProcessUpdateFields() {
	fmt.Println("\nTesting ProcessUpdateFields with primitive.DateTime...")
	fmt.Println("====================================================")

	// Simulate the exact scenario from TranslateOrderCb
	currentTime := primitive.NewDateTimeFromTime(time.Now())

	mock := map[string]interface{}{
		"orderStatus":           "SHIPPED",
		"shipment.0.pickupTime": currentTime,
		"timeline.pickupTime":   currentTime,
	}

	fmt.Printf("Before ProcessUpdateFields:\n")
	for key, value := range mock {
		fmt.Printf("  %s: %v (type: %T)\n", key, value, value)
	}

	// This is what happens in UpdateByOrderId
	processed := util.ProcessUpdateFields(mock)

	fmt.Printf("\nAfter ProcessUpdateFields:\n")
	for key, value := range processed {
		fmt.Printf("  %s: %v (type: %T)\n", key, value, value)
	}

	fmt.Println("\n====================================================")
}

// Test what format primitive.DateTime produces when marshaled to JSON
func testDateTimeMarshaling() {
	fmt.Println("\nTesting primitive.DateTime JSON marshaling...")
	fmt.Println("==============================================")

	currentTime := primitive.NewDateTimeFromTime(time.Now())
	fmt.Printf("Original primitive.DateTime: %v (type: %T)\n", currentTime, currentTime)

	// Test JSON marshaling
	jsonData, err := json.Marshal(currentTime)
	if err != nil {
		fmt.Printf("Error marshaling: %v\n", err)
		return
	}
	fmt.Printf("JSON marshaled: %s\n", string(jsonData))

	// Test what happens when we unmarshal it back
	var unmarshaled interface{}
	err = json.Unmarshal(jsonData, &unmarshaled)
	if err != nil {
		fmt.Printf("Error unmarshaling: %v\n", err)
		return
	}
	fmt.Printf("Unmarshaled back: %v (type: %T)\n", unmarshaled, unmarshaled)

	// Test StructToMap function
	testMap := map[string]interface{}{
		"shipment.0.pickupTime": currentTime,
		"orderStatus":           "SHIPPED",
	}

	fmt.Printf("\nBefore StructToMap: %v\n", testMap["shipment.0.pickupTime"])
	result, err := util.StructToMap(testMap)
	if err != nil {
		fmt.Printf("Error in StructToMap: %v\n", err)
		return
	}
	fmt.Printf("After StructToMap: %v (type: %T)\n", result["shipment.0.pickupTime"], result["shipment.0.pickupTime"])

	fmt.Println("\n==============================================")
}

func main() {
	log.Println("Starting DateTime conversion test...")
	testDateTimeConversion()
	testProcessUpdateFields()
	testDateTimeMarshaling()
}
