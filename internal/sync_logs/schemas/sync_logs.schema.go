package schemas

import (
	schemas2 "abs-scheduler-go/internal/scheduler_tasks/schemas"
	"abs-scheduler-go/internal/task_executions/schemas"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SyncLog struct {
	TaskExecution   primitive.ObjectID              `bson:"taskExecution" json:"taskExecution"`
	Company         primitive.ObjectID              `bson:"company" json:"company"`
	Store           primitive.ObjectID              `bson:"store" json:"store"`
	MarketplaceCode schemas.MarketplaceCodeEnum     `bson:"marketplaceCode" json:"marketplaceCode"`
	Operation       schemas2.SchedulerTaskOperation `bson:"operation" json:"operation"`
	Status          schemas.TaskExecutionStatus     `bson:"status" json:"status"`
	ErrorDetails    interface{}                     `bson:"errorDetails" json:"errorDetails"`
	ResultMetadata  interface{}                     `bson:"resultMetadata" json:"resultMetadata"`
}
