package schemas

import (
	"abs-scheduler-go/pkg/constant"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Limitation struct {
	MaxWeight     float32 `json:"maxWeight" bson:"maxWeight"`
	MinWeight     float32 `json:"minWeight" bson:"minWeight"`
	Length        float32 `json:"length" bson:"length"`
	Height        float32 `json:"height" bson:"height"`
	Width         float32 `json:"width" bson:"width"`
	DimensionUnit string  `json:"dimensionUnit" bson:"dimensionUnit"`
	DimensionSum  float32 `json:"dimensionSum" bson:"dimensionSum"`
	MaxVolume     float32 `json:"maxVolume" bson:"maxVolume"`
	MinVolume     float32 `json:"minVolume" json:"minVolume"`
}

type Logistic struct {
	ID              primitive.ObjectID           `json:"_id" bson:"_id,omitempty"`
	MpId            string                       `json:"mpId" bson:"mpId"`
	Name            string                       `json:"name" bson:"name"`
	ServiceName     string                       `json:"serviceName" bson:"serviceName"`
	CodEnabled      bool                         `json:"codeEnabled" bson:"codeEnabled"`
	Enabled         bool                         `json:"enabled" bson:"enabled"`
	FeeType         string                       `json:"feeType" bson:"feeType"`
	MarketplaceCode constant.MarketplaceCodeEnum `json:"marketplaceCode" bson:"marketplaceCode"`
	Limitations     []Limitation                 `json:"limitations" bson:"limitations"`
	CreatedAt       primitive.DateTime           `json:"createdAt" bson:"createdAt,omitempty"`
	UpdatedAt       primitive.DateTime           `json:"updatedAt" bson:"updatedAt,omitempty"`
}
