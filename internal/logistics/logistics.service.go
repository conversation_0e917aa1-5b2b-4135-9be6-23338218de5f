package logistics

import (
	"context"
	"errors"
	"time"

	"abs-scheduler-go/internal/logistics/schemas"
	"abs-scheduler-go/pkg/constant"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Repository struct {
	collection *mongo.Collection
}

func NewLogisticsRepository(db map[string]*mongo.Database) *Repository {
	collection := db["oms"].Collection("logistics")

	return &Repository{collection}
}

func (r *Repository) GetByMpId(id string, mpCode constant.MarketplaceCodeEnum) (*schemas.Logistic, error) {
	var logistic schemas.Logistic

	ctx := context.Background()

	err := r.collection.FindOne(ctx, bson.M{"mpId": id, "marketplaceCode": mpCode}).Decode(&logistic)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}

	return &logistic, nil
}

func (r *Repository) Insert(data schemas.Logistic) (string, error) {
	data.CreatedAt = primitive.NewDateTimeFromTime(time.Now())
	data.UpdatedAt = primitive.NewDateTimeFromTime(time.Now())

	ctx := context.Background()
	insert, err := r.collection.InsertOne(ctx, data)
	if err != nil {
		return "", err
	}

	return insert.InsertedID.(primitive.ObjectID).Hex(), nil
}

func (r *Repository) Update(data schemas.Logistic) (string, error) {
	data.UpdatedAt = primitive.NewDateTimeFromTime(time.Now())

	ctx := context.Background()
	_, err := r.collection.UpdateOne(ctx, bson.M{"_id": data.ID}, bson.M{"$set": data})
	if err != nil {
		return "", err
	}

	return data.ID.Hex(), nil
}
