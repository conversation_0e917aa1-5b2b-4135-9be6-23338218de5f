package stores

import (
	"context"
	"time"

	"abs-scheduler-go/internal/common/interfaces"
	"abs-scheduler-go/internal/stores/schemas"
	"abs-scheduler-go/pkg/config"
	"abs-scheduler-go/pkg/constant"
	"abs-scheduler-go/pkg/util"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Repository struct {
	collection *mongo.Collection
}

func NewStoreRepository(db map[string]*mongo.Database) *Repository {
	collection := db["user"].Collection("stores")

	return &Repository{collection}
}

func (r *Repository) GetAll(opts *schemas.Store) ([]schemas.Store, error) {
	var stores []schemas.Store

	filter := bson.M{"marketplaceCredential.status": true}
	ctx := context.Background()

	if opts != nil && !primitive.ObjectID.IsZero(opts.Company) {
		filter["company"] = opts.Company
	}

	if opts != nil && opts.MarketplaceCode != "" {
		filter["marketplaceCode"] = opts.MarketplaceCode
	}

	curr, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	err = curr.All(ctx, &stores)
	if err != nil {
		return nil, err
	}

	return stores, nil
}

func (r *Repository) GetByMpId(id string, mpCode constant.MarketplaceCodeEnum) (schemas.Store, error) {
	var store schemas.Store
	filter := bson.M{"marketplaceId": id, "marketplaceCode": mpCode}
	ctx := context.Background()

	//util.PrintJson(filter)

	err := r.collection.FindOne(ctx, filter).Decode(&store)
	if err != nil {
		return schemas.Store{}, err
	}

	return store, nil
}

func (r *Repository) GetById(id primitive.ObjectID) (*schemas.Store, error) {
	var store schemas.Store
	filter := bson.M{"_id": id}
	ctx := context.Background()

	err := r.collection.FindOne(ctx, filter).Decode(&store)
	if err != nil {
		return nil, err
	}

	return &store, nil
}

func (r *Repository) UpdateToken(opts interfaces.UpdateTokenOpts) (schemas.Store, error) {
	var store schemas.Store
	ctx := context.Background()

	key := config.CredentialKey

	encAccess, err := util.EncryptValue(opts.Access, key)
	if err != nil {
		return store, err
	}
	encRefresh, err := util.EncryptValue(opts.Refresh, key)
	if err != nil {
		return store, err
	}

	res := r.collection.FindOneAndUpdate(
		ctx,
		bson.M{"marketplaceId": opts.Id},
		bson.M{
			"$set": bson.M{
				"marketplaceCredential.encryptedAccessValue":  encAccess,
				"marketplaceCredential.encryptedRefreshValue": encRefresh,
				"marketplaceCredential.accessExpiredAt":       opts.ExpAccess,
				"marketplaceCredential.refreshExpiredAt":      opts.ExpRefresh,
				"updatedAt":                                   time.Now(),
			},
		},
	)

	err = res.Decode(&store)
	if err != nil {
		return store, err
	}

	return store, nil
}
