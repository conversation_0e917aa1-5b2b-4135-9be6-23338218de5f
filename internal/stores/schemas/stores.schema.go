package schemas

import (
	"abs-scheduler-go/pkg/constant"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type marketplaceCredential struct {
	EncryptedAccessValue  string             `json:"encryptedAccessValue" bson:"encryptedAccessValue"`
	EncryptedRefreshValue string             `json:"encryptedRefreshValue" bson:"encryptedRefreshValue"`
	Status                bool               `json:"status" bson:"status"`
	AccessExpiredAt       primitive.DateTime `json:"accessExpiredAt" bson:"accessExpiredAt"`
	RefreshExpiredAt      primitive.DateTime `json:"refreshExpiredAt" bson:"refreshExpiredAt"`
}

type Store struct {
	ID                    primitive.ObjectID           `json:"_id" bson:"_id,omitempty"`
	MarketplaceId         string                       `json:"marketplaceId" bson:"marketplaceId"`
	MarketplaceCode       constant.MarketplaceCodeEnum `json:"marketplaceCode" bson:"marketplaceCode"`
	Name                  string                       `json:"name" bson:"name"`
	Company               primitive.ObjectID           `json:"company" bson:"company"`
	MarketplaceCredential marketplaceCredential        `json:"marketplaceCredential" bson:"marketplaceCredential"`
}
