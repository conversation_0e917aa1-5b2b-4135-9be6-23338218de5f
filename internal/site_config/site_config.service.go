package site_config

import (
	"context"
	"errors"

	"abs-scheduler-go/internal/common/interfaces"
	"abs-scheduler-go/internal/site_config/schemas"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type Repository struct {
	collection *mongo.Collection
}

func NewSiteConfigRepository(db map[string]*mongo.Database) *Repository {
	collection := db["user"].Collection("siteconfigs")

	return &Repository{collection}
}

func (r *Repository) GetByKey(payload interfaces.GetByKeyOpts) (schemas.SiteConfig, error) {
	var siteConfig schemas.SiteConfig

	filter := bson.M{"key": payload.Key}
	ctx := context.Background()

	err := r.collection.FindOne(ctx, filter).Decode(&siteConfig)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return schemas.SiteConfig{}, nil
		}
		return schemas.SiteConfig{}, err
	}

	return siteConfig, nil
}
