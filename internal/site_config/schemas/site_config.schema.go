package schemas

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SiteConfig struct {
	ID          primitive.ObjectID `json:"_id" bson:"_id,omitempty"`
	Key         string             `json:"key" bson:"key"`
	Description string             `json:"description" bson:"description"`
	Value       string             `json:"value" bson:"value"`
	Status      string             `json:"status" bson:"status"`
}
