package schemas

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ProductStatus string

const (
	ProductStatusActive   ProductStatus = "active"
	ProductStatusInactive ProductStatus = "inactive"
)

type ProductSchema struct {
	Id           primitive.ObjectID   `json:"id" bson:"_id,omitempty"`
	Sku          string               `json:"sku" bson:"sku"`
	Name         string               `json:"name" bson:"name"`
	Description  []map[string]string  `json:"description" bson:"description"`
	ImageUrl     string               `json:"imageUrl" bson:"imageUrl"`
	Height       int                  `json:"height" bson:"height"`
	Width        int                  `json:"width" bson:"width"`
	Length       int                  `json:"length" bson:"length"`
	Weight       int                  `json:"weight" bson:"weight"`
	Company      primitive.ObjectID   `json:"company" bson:"company"`
	Variants     []primitive.ObjectID `json:"variants" bson:"variants"`
	LastSyncedAt primitive.DateTime   `json:"lastSyncedAt" bson:"lastSyncedAt"`
	Status       ProductStatus        `json:"status" bson:"status"`
	CreatedAt    primitive.DateTime   `json:"createdAt" bson:"createdAt,omitempty"`
	UpdatedAt    primitive.DateTime   `json:"updatedAt" bson:"updatedAt,omitempty"`
}
