package schemas

import "go.mongodb.org/mongo-driver/bson/primitive"

type ProductVariantSchema struct {
	Id        primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	Product   primitive.ObjectID `json:"product" bson:"product"`
	Sku       string             `json:"sku" bson:"sku"`
	Price     float64            `json:"price" bson:"price"`
	DiscPrice float64            `json:"discPrice" bson:"discPrice"`
	Image     string             `json:"image" bson:"image"`
	CreatedAt primitive.DateTime `json:"createdAt" bson:"createdAt,omitempty"`
	UpdatedAt primitive.DateTime `json:"updatedAt" bson:"updatedAt,omitempty"`
}
