package schemas

import (
	"abs-scheduler-go/pkg/constant"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ProductImage struct {
	MpImageId   string `json:"mpImageId" bson:"mpImageId"`
	MpProductId string `json:"mpProductId" bson:"mpProductId"`
	Url         string `json:"url" bson:"url"`
}

type AttributeList struct {
	ValueId           string `json:"valueId" bson:"valueId"`
	OriginalValueName string `json:"originalValueName" bson:"originalValueName"`
	ValueUnit         string `json:"valueUnit" bson:"valueUnit"`
}

type Attribute struct {
	AttributeId           string          `json:"attributeId" bson:"attributeId"`
	OriginalAttributeName string          `json:"originalAttributeName" bson:"originalAttributeName"`
	IsMandatory           bool            `json:"isMandatory" bson:"isMandatory"`
	Lists                 []AttributeList `json:"lists" bson:"lists"`
}

type Logistic struct {
	LogisticId string `json:"logisticId" bson:"logisticId"`
	Enabled    bool   `json:"enabled" bson:"enabled"`
	IsFree     bool   `json:"isFree" bson:"isFree"`
	UniqueId   string `json:"uniqueId" bson:"uniqueId"`
}

type ProductChildrenSchema struct {
	Id              primitive.ObjectID           `json:"id" bson:"_id,omitempty"`
	Product         primitive.ObjectID           `json:"product" bson:"product"`
	SkuMarketplace  string                       `json:"skuMarketplace" bson:"skuMarketplace"`
	MpProductId     string                       `json:"mpProductId" bson:"mpProductId"`
	MpCategoryId    string                       `json:"mpCategoryId" bson:"mpCategoryId"`
	Name            string                       `json:"name" bson:"name"`
	Images          []ProductImage               `json:"images" bson:"images"`
	Description     []map[string]string          `json:"description" bson:"description"`
	MarketplaceCode constant.MarketplaceCodeEnum `json:"marketplaceCode" bson:"marketplaceCode"`
	Store           string                       `json:"store" bson:"store"`
	Company         primitive.ObjectID           `json:"company" bson:"company"`
	IsVisible       bool                         `json:"isVisible" bson:"isVisible"`
	IsMain          bool                         `json:"isMain" bson:"isMain"`
	Attributes      []Attribute                  `json:"attributes" bson:"attributes"`
	Logistics       []Logistic                   `json:"logistics" bson:"logistics"`
	Height          int                          `json:"height" bson:"height"`
	Weight          int                          `json:"weight" bson:"weight"`
	Width           int                          `json:"width" bson:"width"`
	Length          int                          `json:"length" bson:"length"`
	CreatedAt       primitive.DateTime           `json:"createdAt" bson:"createdAt,omitempty"`
	UpdatedAt       primitive.DateTime           `json:"updatedAt" bson:"updatedAt,omitempty"`
}
