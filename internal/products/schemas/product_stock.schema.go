package schemas

import "go.mongodb.org/mongo-driver/bson/primitive"

type ProductStockSchema struct {
	Id          primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	Product     primitive.ObjectID `json:"product" bson:"product"`
	Sku         string             `json:"sku" bson:"sku"`
	Batch       string             `json:"batch" bson:"batch"`
	ExpiredDate primitive.DateTime `json:"expiredDate" bson:"expiredDate,omitempty"`
	Quantity    int32              `json:"quantity" bson:"quantity"`
	Reserved    int32              `json:"reserved" bson:"reserved"`
	Company     primitive.ObjectID `json:"company" bson:"company"`
	Warehouse   primitive.ObjectID `json:"warehouse" bson:"warehouse,omitempty"`
	CreatedAt   primitive.DateTime `json:"createdAt" bson:"createdAt,omitempty"`
	UpdatedAt   primitive.DateTime `json:"updatedAt" bson:"updatedAt,omitempty"`
}
