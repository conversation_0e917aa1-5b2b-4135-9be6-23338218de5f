package products

import (
	"context"
	"errors"
	"fmt"
	"log"
	"strings"
	"time"

	"abs-scheduler-go/internal/products/schemas"
	"abs-scheduler-go/pkg/constant"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Repository struct {
	productColl             *mongo.Collection
	productChildColl        *mongo.Collection
	productStockColl        *mongo.Collection
	productVariantColl      *mongo.Collection
	productVariantChildColl *mongo.Collection
	productSeq              *mongo.Collection
}

func NewProductRepository(db map[string]*mongo.Database) *Repository {
	productColl := db["oms"].Collection("products")
	productChildColl := db["oms"].Collection("productchildrens")
	productStockColl := db["oms"].Collection("productstocks")
	productVariantColl := db["oms"].Collection("productvariants")
	productVariantChildColl := db["oms"].Collection("productvariantchildrens")
	productSeq := db["oms"].Collection("productskuseqs")

	_, err := productColl.Indexes().CreateMany(context.Background(), []mongo.IndexModel{
		{
			Keys:    bson.M{"sku": 1},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys:    bson.M{"status": 1},
			Options: options.Index(),
		},
		{
			Keys:    bson.M{"company": 1},
			Options: options.Index(),
		},
	})
	if err != nil {
		log.Panicf("error creating product indexes: %v", err)
	}

	_, err = productChildColl.Indexes().CreateMany(context.Background(), []mongo.IndexModel{
		{
			Keys:    bson.D{{Key: "mpProductId", Value: 1}, {Key: "marketplaceCode", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys:    bson.D{{Key: "mpCategoryId", Value: 1}, {Key: "marketplaceCode", Value: 1}},
			Options: options.Index(),
		},
		{
			Keys:    bson.M{"product": 1},
			Options: options.Index(),
		},
		{
			Keys:    bson.M{"skuMarketplace": 1},
			Options: options.Index(),
		},
		{
			Keys:    bson.M{"mpProductId": 1},
			Options: options.Index(),
		},
		{
			Keys:    bson.M{"mpCategoryId": 1},
			Options: options.Index(),
		},
		{
			Keys:    bson.M{"marketplaceCode": 1},
			Options: options.Index(),
		},
		{
			Keys:    bson.M{"company": 1},
			Options: options.Index(),
		},
		{
			Keys:    bson.M{"isVisible": 1},
			Options: options.Index(),
		},
		{
			Keys:    bson.M{"isMain": 1},
			Options: options.Index(),
		},
	})
	if err != nil {
		log.Panicf("error creating product children indexes: %v", err)
	}

	_, err = productVariantColl.Indexes().CreateMany(context.Background(), []mongo.IndexModel{
		{
			Keys:    bson.D{{Key: "sku", Value: 1}, {Key: "product", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys:    bson.M{"sku": 1},
			Options: options.Index(),
		},
	})
	if err != nil {
		log.Panicf("error creating product variant indexes: %v", err)
	}

	_, err = productVariantChildColl.Indexes().CreateMany(context.Background(), []mongo.IndexModel{
		{
			Keys:    bson.D{{Key: "productVariantId", Value: 1}, {Key: "marketplaceCode", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys:    bson.M{"mpSku": 1},
			Options: options.Index(),
		},
		{
			Keys:    bson.M{"marketplaceCode": 1},
			Options: options.Index(),
		},
		{
			Keys:    bson.M{"productVariantId": 1},
			Options: options.Index(),
		},
		{
			Keys:    bson.M{"productId": 1},
			Options: options.Index(),
		},
	})
	if err != nil {
		log.Panicf("error creating product variant children indexes: %v", err)
	}

	_, err = productStockColl.Indexes().CreateMany(context.Background(), []mongo.IndexModel{
		{
			Keys:    bson.D{{Key: "sku", Value: 1}, {Key: "expiredDate", Value: 1}, {Key: "company", Value: 1}, {Key: "warehouse", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys:    bson.M{"company": 1},
			Options: options.Index(),
		},
		{
			Keys:    bson.M{"product": 1},
			Options: options.Index(),
		},
		{
			Keys:    bson.M{"sku": 1},
			Options: options.Index(),
		},
		{
			Keys:    bson.M{"warehouse": 1},
			Options: options.Index(),
		},
	})
	if err != nil {
		log.Panicf("error creating product stock indexes: %v", err)
	}

	_, err = productSeq.Indexes().CreateMany(context.Background(), []mongo.IndexModel{
		{
			Keys:    bson.D{{Key: "collection", Value: 1}, {Key: "period", Value: 1}},
			Options: options.Index().SetUnique(true),
		},
	})
	if err != nil {
		log.Panicf("error creating product seq indexes: %v", err)
	}

	return &Repository{
		productColl:             productColl,
		productChildColl:        productChildColl,
		productStockColl:        productStockColl,
		productVariantColl:      productVariantColl,
		productVariantChildColl: productVariantChildColl,
		productSeq:              productSeq,
	}
}

func (r *Repository) FindProduct(id primitive.ObjectID) (data *schemas.ProductSchema, err error) {
	var result schemas.ProductSchema

	err = r.productColl.FindOne(context.Background(), bson.M{"_id": id}).Decode(&result)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, fmt.Errorf("error while get product data %w", err)
	}

	return &result, nil
}

func (r *Repository) FindMultiProductBySkus(skus []string) (data *[]schemas.ProductVariantChildrenSchema, err error) {
	result := make([]schemas.ProductVariantChildrenSchema, 0)

	ctx := context.Background()

	cursor, err := r.productVariantChildColl.Find(ctx, bson.M{"mpSku": bson.M{"$in": skus}})
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, fmt.Errorf("error while get product data %w", err)
	}

	for cursor.Next(ctx) {
		var product schemas.ProductVariantChildrenSchema
		errDecode := cursor.Decode(&product)
		if errDecode != nil {
			return nil, fmt.Errorf("error while decode product data %w", errDecode)
		}
		result = append(result, product)
	}

	return &result, nil
}

func (r *Repository) FindProductChildren(id string, marketplaceCode constant.MarketplaceCodeEnum) (data *schemas.ProductChildrenSchema, err error) {
	var result schemas.ProductChildrenSchema

	err = r.productChildColl.FindOne(context.Background(), bson.M{"mpProductId": id, "marketplaceCode": marketplaceCode}).Decode(&result)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, fmt.Errorf("error while get product children data %w", err)
	}

	return &result, nil
}

func (r *Repository) FindProductVariant(productId primitive.ObjectID) (data *schemas.ProductVariantSchema, err error) {
	var result schemas.ProductVariantSchema

	err = r.productVariantColl.FindOne(context.Background(), bson.M{"product": productId}).Decode(&result)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, fmt.Errorf("error while get product variant data %w", err)
	}

	return &result, nil
}

func (r *Repository) FindProductVariantChildren(id string, marketplaceCode constant.MarketplaceCodeEnum) (data *schemas.ProductVariantChildrenSchema, err error) {
	var result schemas.ProductVariantChildrenSchema

	err = r.productVariantChildColl.FindOne(context.Background(), bson.M{"productVariantId": id, "marketplaceCode": marketplaceCode}).Decode(&result)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, fmt.Errorf("error while get product variant children data %w", err)
	}

	return &result, nil
}

func (r *Repository) InsertProduct(product schemas.ProductSchema) (id string, err error) {
	product.CreatedAt = primitive.NewDateTimeFromTime(time.Now())
	product.UpdatedAt = primitive.NewDateTimeFromTime(time.Now())

	if product.Variants == nil {
		product.Variants = []primitive.ObjectID{}
	}

	res, err := r.productColl.InsertOne(context.Background(), product)
	if err != nil {
		return "", fmt.Errorf("error while insert product data %w", err)
	}

	log.Printf("Inserted product with id: %s", res.InsertedID)

	return res.InsertedID.(primitive.ObjectID).Hex(), nil
}

func (r *Repository) UpdateProduct(product schemas.ProductSchema) (result *schemas.ProductSchema, err error) {
	product.UpdatedAt = primitive.NewDateTimeFromTime(time.Now())

	err = r.productColl.FindOneAndUpdate(context.Background(), bson.M{"_id": product.Id}, bson.M{"$set": product}).Decode(&result)
	if err != nil {
		return nil, fmt.Errorf("error while update product data %w", err)
	}

	return result, nil
}

func (r *Repository) UpdateProductChildren(product schemas.ProductChildrenSchema) (result *schemas.ProductChildrenSchema, err error) {
	product.UpdatedAt = primitive.NewDateTimeFromTime(time.Now())

	err = r.productChildColl.FindOneAndUpdate(context.Background(), bson.M{"_id": product.Id}, bson.M{"$set": product}).Decode(&result)
	if err != nil {
		return nil, fmt.Errorf("error while update product children data %w", err)
	}

	return result, nil
}

func (r *Repository) AppendProductChildren(id primitive.ObjectID, productChildrenId primitive.ObjectID) (result *schemas.ProductChildrenSchema, err error) {
	err = r.productColl.FindOneAndUpdate(
		context.Background(),
		bson.M{"_id": id},
		bson.M{
			"$set":      bson.M{"updatedAt": primitive.NewDateTimeFromTime(time.Now())},
			"$addToSet": bson.M{"variants": productChildrenId},
		},
	).Decode(&result)
	if err != nil {
		return nil, fmt.Errorf("error while update product children data %w", err)
	}

	return result, nil
}

func (r *Repository) UpdateProductVariant(product schemas.ProductVariantSchema) (result *schemas.ProductVariantSchema, err error) {
	product.UpdatedAt = primitive.NewDateTimeFromTime(time.Now())

	err = r.productVariantColl.FindOneAndUpdate(context.Background(), bson.M{"_id": product.Id}, bson.M{"$set": product}).Decode(&result)
	if err != nil {
		return nil, fmt.Errorf("error while update product variant data %w", err)
	}

	return result, nil
}

func (r *Repository) UpdateProductVariantChildren(product schemas.ProductVariantChildrenSchema) (result *schemas.ProductVariantChildrenSchema, err error) {
	product.UpdatedAt = primitive.NewDateTimeFromTime(time.Now())

	err = r.productVariantChildColl.FindOneAndUpdate(context.Background(), bson.M{"_id": product.Id}, bson.M{"$set": product}).Decode(&result)
	if err != nil {
		return nil, fmt.Errorf("error while update product variant children data %w", err)
	}

	return result, nil
}

func (r *Repository) DeleteProduct(id primitive.ObjectID) error {
	_, err := r.productColl.DeleteOne(context.Background(), bson.M{"_id": id})
	if err != nil {
		return fmt.Errorf("error deleting product: %w", err)
	}

	return nil
}

func (r *Repository) InsertProductChildren(product schemas.ProductChildrenSchema) (id string, err error) {
	product.CreatedAt = primitive.NewDateTimeFromTime(time.Now())
	product.UpdatedAt = primitive.NewDateTimeFromTime(time.Now())

	res, err := r.productChildColl.InsertOne(context.Background(), product)
	if err != nil {
		return "", fmt.Errorf("error while insert product children data %w", err)
	}

	return res.InsertedID.(primitive.ObjectID).Hex(), nil
}

func (r *Repository) DeleteProductChildren(id primitive.ObjectID, timeRange []time.Time) error {
	err := r.productChildColl.FindOne(context.Background(), bson.M{"_id": id, "createdAt": bson.M{"$gte": timeRange[0], "$lte": timeRange[1]}}).Err()
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil
		}
		return fmt.Errorf("error finding product children: %w", err)
	}

	_, err = r.productChildColl.DeleteOne(context.Background(), bson.M{"_id": id})
	if err != nil {
		return fmt.Errorf("error deleting product children: %w", err)
	}

	return nil
}

func (r *Repository) InsertProductVariant(product schemas.ProductVariantSchema) (id string, err error) {
	product.CreatedAt = primitive.NewDateTimeFromTime(time.Now())
	product.UpdatedAt = primitive.NewDateTimeFromTime(time.Now())

	res, err := r.productVariantColl.InsertOne(context.Background(), product)
	if err != nil {
		return "", fmt.Errorf("error while insert product variant data %w", err)
	}

	return res.InsertedID.(primitive.ObjectID).Hex(), nil
}

func (r *Repository) DeleteProductVariant(id primitive.ObjectID, timeRange []time.Time) error {
	err := r.productVariantColl.FindOne(context.Background(), bson.M{"_id": id, "createdAt": bson.M{"$gte": timeRange[0], "$lte": timeRange[1]}}).Err()
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil
		}
		return fmt.Errorf("error finding product variant: %w", err)
	}

	_, err = r.productVariantColl.DeleteOne(context.Background(), bson.M{"_id": id})
	if err != nil {
		return fmt.Errorf("error deleting product variant: %w", err)
	}

	return nil
}

func (r *Repository) InsertProductVariantChildren(product schemas.ProductVariantChildrenSchema) (id string, err error) {
	product.CreatedAt = primitive.NewDateTimeFromTime(time.Now())
	product.UpdatedAt = primitive.NewDateTimeFromTime(time.Now())

	res, err := r.productVariantChildColl.InsertOne(context.Background(), product)
	if err != nil {
		return "", fmt.Errorf("error while insert product variant children data %w", err)
	}

	return res.InsertedID.(primitive.ObjectID).Hex(), nil
}

func (r *Repository) DeleteProductVariantChildren(id primitive.ObjectID, timeRange []time.Time) error {
	err := r.productVariantChildColl.FindOne(context.Background(), bson.M{"_id": id, "createdAt": bson.M{"$gte": timeRange[0], "$lte": timeRange[1]}}).Err()
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil
		}
		return fmt.Errorf("error finding product variant children: %w", err)
	}

	_, err = r.productVariantChildColl.DeleteOne(context.Background(), bson.M{"_id": id})
	if err != nil {
		return err
	}

	return nil
}

func (r *Repository) InsertProductStock(product schemas.ProductStockSchema) (id string, err error) {
	product.CreatedAt = primitive.NewDateTimeFromTime(time.Now())
	product.UpdatedAt = primitive.NewDateTimeFromTime(time.Now())

	res, err := r.productStockColl.InsertOne(context.Background(), product)
	if err != nil {
		return "", fmt.Errorf("error while insert product stock data %w", err)
	}

	return res.InsertedID.(primitive.ObjectID).Hex(), nil
}

func (r *Repository) UpdateProductStock(product schemas.ProductStockSchema) (result *schemas.ProductStockSchema, err error) {
	product.UpdatedAt = primitive.NewDateTimeFromTime(time.Now())

	err = r.productStockColl.FindOneAndUpdate(context.Background(), bson.M{"_id": product.Id}, bson.M{"$set": product}).Decode(&result)
	if err != nil {
		return nil, fmt.Errorf("error while update product stock data %w", err)
	}

	return result, nil
}

func (r *Repository) DeleteProductStock(id primitive.ObjectID, timeRange []time.Time) error {
	err := r.productStockColl.FindOne(context.Background(), bson.M{"_id": id, "createdAt": bson.M{"$gte": timeRange[0], "$lte": timeRange[1]}}).Err()
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil
		}
		return fmt.Errorf("error finding product stock: %w", err)
	}

	_, err = r.productStockColl.DeleteOne(context.Background(), bson.M{"_id": id})
	if err != nil {
		return fmt.Errorf("error deleting product stock: %w", err)
	}

	return nil
}

func (r *Repository) GetSku(collection string) (seq string, err error) {
	var update schemas.SkuSeq

	year, month, _ := time.Now().Date()

	err = r.productSeq.FindOneAndUpdate(
		context.Background(),
		bson.M{"collection": collection, "period": fmt.Sprintf("%d-%d", year, month)},
		bson.M{"$inc": bson.M{"value": 1}},
		options.FindOneAndUpdate().SetReturnDocument(options.After).SetUpsert(true),
	).Decode(&update)
	if err != nil {
		return "", fmt.Errorf("error while get sku seq data %w", err)
	}

	return fmt.Sprintf("%s%06d", strings.Replace(update.Period, "-", "", -1), update.Value), nil
}

func (r *Repository) FindProductStock(sku string) (data *schemas.ProductStockSchema, err error) {
	var result schemas.ProductStockSchema

	err = r.productStockColl.FindOne(context.Background(), bson.M{"sku": sku}).Decode(&result)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, fmt.Errorf("error while get product stock data %w", err)
	}

	return &result, nil
}
