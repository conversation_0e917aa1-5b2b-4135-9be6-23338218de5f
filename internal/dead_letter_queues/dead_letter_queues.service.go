package dead_letter_queues

import (
	"context"
	"time"

	"abs-scheduler-go/internal/dead_letter_queues/schemas"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Repository struct {
	collection *mongo.Collection
}

func NewDeadLetterQueuesRepository(db map[string]*mongo.Database) *Repository {
	collection := db["main"].Collection("deadletterqueues")

	return &Repository{collection}
}

func (r *Repository) Insert(data schemas.DeadLetterQueues) (string, error) {
	ctx := context.Background()

	data.CreatedAt = primitive.NewDateTimeFromTime(time.Now())
	data.UpdatedAt = primitive.NewDateTimeFromTime(time.Now())

	insert, err := r.collection.InsertOne(ctx, data)
	if err != nil {
		return "", err
	}

	return insert.InsertedID.(primitive.ObjectID).Hex(), nil
}
