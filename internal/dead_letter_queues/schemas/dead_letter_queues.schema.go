package schemas

import "go.mongodb.org/mongo-driver/bson/primitive"

type DeadLetterQueues struct {
	SchedulerTask    primitive.ObjectID `bson:"schedulerTask,omitempty" json:"schedulerTask,omitempty"`
	Batch            string             `bson:"batch" json:"batch"`
	RetryCount       uint8              `bson:"retryCount" json:"retryCount"`
	MaxRetries       uint8              `bson:"maxRetries" json:"maxRetries"`
	LastErrorDetails interface{}        `bson:"lastErrorDetails"  json:"lastErrorDetails"`
	Company          primitive.ObjectID `bson:"company,omitempty" json:"company,omitempty"`
	Store            primitive.ObjectID `bson:"store,omitempty" json:"store,omitempty"`
	CreatedAt        primitive.DateTime `bson:"createdAt,omitempty" json:"createdAt,omitempty"`
	UpdatedAt        primitive.DateTime `bson:"updatedAt,omitempty" json:"updatedAt,omitempty"`
}
