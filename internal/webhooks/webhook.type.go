package webhooks

import (
	"context"

	"abs-scheduler-go/internal/common/interfaces"
	"abs-scheduler-go/pkg/queue"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type FindAllOpts struct {
	Ctx    context.Context
	Filter bson.M
	Limit  uint
	Skip   uint
	Sort   bson.M
}

type addCallbackOpts struct {
	id             primitive.ObjectID
	shopId         string
	orderId        string
	queueServ      *queue.Service
	callbackRepo   interfaces.CallbackRepository
	orderRepo      interfaces.OrderRepository
	siteConfigRepo interfaces.SiteConfigRepository
}
