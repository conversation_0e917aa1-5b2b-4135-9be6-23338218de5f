package schemas

import (
	"time"

	"abs-scheduler-go/pkg/constant"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Webhook struct {
	ID              primitive.ObjectID           `json:"_id" bson:"_id,omitempty"`
	MPStoreId       string                       `json:"mpStoreId" bson:"mpStoreId"`
	MarketPlaceCode constant.MarketplaceCodeEnum `json:"marketPlaceCode" bson:"marketPlaceCode"`
	Data            map[string]interface{}       `json:"data" bson:"data"`
	IsProcessed     bool                         `json:"isProcessed" bson:"isProcessed"`
	CreatedAt       time.Time                    `json:"created_at" bson:"createdAt"` // Timestamp for creation
	UpdatedAt       time.Time                    `json:"updated_at" bson:"updatedAt"` // Timestamp for last update
}
