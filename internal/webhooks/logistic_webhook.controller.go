package webhooks

import (
	"log"
	"net/http"
	"strings"

	_ "abs-scheduler-go/internal/common/interfaces"
	"abs-scheduler-go/internal/logistics"
	"abs-scheduler-go/internal/stores"
	"abs-scheduler-go/pkg/constant"
	"abs-scheduler-go/pkg/marketplaces/shopee"
	"abs-scheduler-go/pkg/util"
)

type LogisticWebhookController struct {
	LogisticServ *logistics.Repository
	StoreServ    *stores.Repository
}

func NewLogisticWebhookController(opts LogisticWebhookController) *LogisticWebhookController {
	return &opts
}

func (c *LogisticWebhookController) Sync(w http.ResponseWriter, r *http.Request) {
	body, validation, err := util.ValidateRequest(r, util.ValidationSourceQuery, []util.ValidationRule{
		{
			Field: "marketplace_code",
			Rule: []util.Rule{
				{
					Option: util.RuleRequired,
				},
			},
			Format: util.ValidationDataTypeString,
		},
		{
			Field: "shop_id",
			Rule: []util.Rule{
				{
					Option: util.RuleRequired,
				},
			},
			Format: util.ValidationDataTypeString,
		},
	})
	if err != nil {
		util.SendResponse(w, http.StatusBadRequest, "sync logistic failed", "Invalid JSON payload", nil)
		return
	}
	if len(validation) > 0 {
		util.SendResponse(w, http.StatusPreconditionFailed, "sync logistic failed", "Validation Error", validation)
		return
	}

	var res map[string]interface{}
	switch strings.ToUpper(body["marketplace_code"].(string)) {
	case string(constant.MarketplaceCodeShopee):
		res, err = shopee.SyncLogistic(shopee.SyncLogisticsOpts{
			ShopId:       body["shop_id"].(string),
			StoreServ:    c.StoreServ,
			LogisticServ: c.LogisticServ,
		})
		if err != nil {
			log.Printf("error while sync logistic shopee: %v", err)
			util.SendResponse(w, http.StatusInternalServerError, "sync logistic failed", "Failed to sync logistic", nil)
			return
		}
	}

	util.SendResponse(w, http.StatusOK, "successfully sync logistic", "", res)
}
