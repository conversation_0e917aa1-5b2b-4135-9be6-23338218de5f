package webhooks

import (
	"errors"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"abs-scheduler-go/internal/callbacks"
	schemas5 "abs-scheduler-go/internal/callbacks/schemas"
	"abs-scheduler-go/internal/common/interfaces"
	_ "abs-scheduler-go/internal/common/interfaces"
	"abs-scheduler-go/internal/logistics"
	"abs-scheduler-go/internal/orders"
	schemas3 "abs-scheduler-go/internal/orders/schemas"
	"abs-scheduler-go/internal/products"
	schemas2 "abs-scheduler-go/internal/scheduler_tasks/schemas"
	"abs-scheduler-go/internal/site_config"
	"abs-scheduler-go/internal/stores"
	schemas4 "abs-scheduler-go/internal/stores/schemas"
	"abs-scheduler-go/internal/task_executions"
	"abs-scheduler-go/internal/task_executions/schemas"
	"abs-scheduler-go/pkg/config"
	"abs-scheduler-go/pkg/constant"
	"abs-scheduler-go/pkg/marketplaces/shopee"
	"abs-scheduler-go/pkg/queue"
	"abs-scheduler-go/pkg/util"
	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type OrderWebhookController struct {
	OrderServ      *orders.Repository
	TaskExecServ   *task_executions.Repository
	StoreServ      *stores.Repository
	ProductServ    *products.Repository
	QueueServ      *queue.Service
	CallbackServ   *callbacks.Repository
	SiteConfigServ *site_config.Repository
	LogisticServ   *logistics.Repository
}

func NewOrderWebhookController(opts OrderWebhookController) *OrderWebhookController {
	return &opts
}

// ConfirmOrder handles the confirmation of an order.
func (c *OrderWebhookController) ConfirmOrder(w http.ResponseWriter, r *http.Request) {
	var orderId primitive.ObjectID

	body, validation, err := util.ValidateRequest(r, util.ValidationSourceBody, []util.ValidationRule{
		{
			Field: "order_id",
			Rule: []util.Rule{
				{
					Option: util.RuleRequired,
				},
			},
			Format: util.ValidationDataTypeString,
		},
	})
	if err != nil {
		util.SendResponse(w, http.StatusBadRequest, "confirm order failed", "Invalid JSON payload", nil)
		return
	}
	if len(validation) > 0 {
		util.SendResponse(w, http.StatusPreconditionFailed, "confirm order failed", "Validation Error", validation)
		return
	}

	parsed, err := primitive.ObjectIDFromHex(body["order_id"].(string))
	if err != nil {
		util.SendResponse(w, http.StatusBadRequest, "confirm order failed", "Invalid Order Id format", nil)
		return
	}
	orderId = parsed

	order, err := getOrderDetail(getOrderDetailOpts{
		orderServ:  c.OrderServ,
		orderId:    orderId,
		httpWriter: w,
	})
	if err != nil {
		return
	}

	if constant.ShopeeOrderStatusPriority[order.OrderStatus] > 2 {
		util.SendResponse(w, http.StatusBadRequest, "confirm order failed", "Order already processed", nil)
		return
	}

	err = insertTaskExecution(insertTaskExecutionOpts{
		queueServ:    c.QueueServ,
		taskExecServ: c.TaskExecServ,
		order:        *order,
		orderId:      orderId,
		startTime:    time.Now(),
		httpWriter:   w,
		operation:    schemas2.SchedulerTaskOperationConfirmOrder,
		body: map[string]interface{}{
			"orderId": orderId,
		},
		method: string(constant.HttpMethodPost),
	})
	if err != nil {
		util.SendResponse(w, http.StatusInternalServerError, "confirm order failed", "Internal Server error", nil)
		return
	}

	util.SendResponse(w, http.StatusOK, "successfully confirm order", "", nil)
}

// FulfillOrder handles the fulfillment of an order.
func (c *OrderWebhookController) FulfillOrder(w http.ResponseWriter, r *http.Request) {
	var orderId primitive.ObjectID
	body, validation, err := util.ValidateRequest(r, util.ValidationSourceBody, []util.ValidationRule{
		{
			Field: "order_id",
			Rule: []util.Rule{
				{
					Option: util.RuleRequired,
				},
			},
			Format: util.ValidationDataTypeString,
		},
	})
	if err != nil {
		util.SendResponse(w, http.StatusBadRequest, "confirm order failed", "Invalid JSON payload", nil)
		return
	}
	if len(validation) > 0 {
		util.SendResponse(w, http.StatusBadRequest, "confirm order failed", "Validation Error", validation)
		return
	}
	parsed, err := primitive.ObjectIDFromHex(body["order_id"].(string))
	if err != nil {
		util.SendResponse(w, http.StatusBadRequest, "fulfill order failed", "Invalid Order Id format", nil)
		return
	}
	orderId = parsed

	order, err := getOrderDetail(getOrderDetailOpts{
		orderServ:  c.OrderServ,
		orderId:    orderId,
		httpWriter: w,
	})
	if err != nil {
		return
	}

	body["shop_id"] = order.MpStoreId

	switch order.MarketplaceCode {
	case constant.MarketplaceCodeShopee:
		requiredFields, err := shopee.ValidateRequiredFields(shopee.ValidateRequiredFieldsOpts{
			ShopId:       order.StoreId.Hex(),
			StoreService: c.StoreServ,
			OrderId:      order.OrderId,
		})
		if err != nil {
			log.Printf("error while get order %s, err: %v", orderId.Hex(), err)
			util.SendResponse(w, http.StatusInternalServerError, "fulfill order failed", "Internal Server error", nil)
			return
		}

		if len(requiredFields) > 0 {
			validation := make(map[string]interface{})
			for _, field := range requiredFields {
				if body[field] == "" {
					validation[field] = fmt.Sprintf("%s is required", field)
				}
			}
			if len(validation) > 0 {
				util.SendResponse(w, http.StatusInternalServerError, "fulfill order failed", "Validation Error", validation)
				return
			}
		}
	}

	err = insertTaskExecution(insertTaskExecutionOpts{
		queueServ:    c.QueueServ,
		taskExecServ: c.TaskExecServ,
		order:        *order,
		orderId:      orderId,
		startTime:    time.Now(),
		httpWriter:   w,
		operation:    schemas2.SchedulerTaskOperationFulfilOrder,
		body:         body,
		method:       string(constant.HttpMethodPost),
	})
	if err != nil {
		util.SendResponse(w, http.StatusInternalServerError, "fulfill order failed", "Internal Server error", nil)
		return
	}

	util.SendResponse(w, http.StatusOK, "successfully confirm order", "", nil)
}

func (c *OrderWebhookController) AdjustStock(w http.ResponseWriter, r *http.Request) {
	body, validation, err := util.ValidateRequest(r, util.ValidationSourceBody, []util.ValidationRule{
		{
			Field: "products",
			Rule: []util.Rule{
				{
					Option: util.RuleRequired,
				},
			},
			Format: util.ValidationDataTypeArray,
			NestedRules: []util.ValidationRule{
				{
					Field: "sku",
					Rule: []util.Rule{
						{
							Option: util.RuleRequired,
						},
					},
					Format: util.ValidationDataTypeString,
				},
				{
					Field: "qty",
					Rule: []util.Rule{
						{
							Option:  util.RulePattern,
							Value:   "^\\+[0-9]+$|^-\\+[0-9]+$",
							Message: "format qty harus + atau - diikuti angka",
						},
					},
					Format: util.ValidationDataTypeString,
				},
			},
		},
	})
	if err != nil {
		util.SendResponse(w, http.StatusBadRequest, "adjust stock failed", "Invalid JSON payload", nil)
		return
	}
	if len(validation) > 0 {
		util.SendResponse(w, http.StatusPreconditionFailed, "adjust stock failed", "Validation Error", validation)
		return
	}

	util.SendResponse(w, http.StatusOK, "successfully adjust stock", "", body)
}

func (c *OrderWebhookController) SyncOrderDetail(w http.ResponseWriter, r *http.Request) {
	company := util.GetCompanyData(r)
	body, validation, err := util.ValidateRequest(r, util.ValidationSourceQuery, []util.ValidationRule{
		{
			Field: "order_id",
			Rule: []util.Rule{
				{
					Option: util.RuleRequired,
				},
			},
			Format: util.ValidationDataTypeString,
		},
	})
	if err != nil {
		util.SendResponse(w, http.StatusBadRequest, "sync order detail failed", "Invalid JSON payload", nil)
		return
	}
	if len(validation) > 0 {
		util.SendResponse(w, http.StatusPreconditionFailed, "sync order detail failed", "Validation Error", validation)
		return
	}

	orderIds := strings.Split(body["order_id"].(string), ",")
	if len(orderIds) == 0 {
		util.SendResponse(w, http.StatusBadRequest, "sync order detail failed", "Invalid Order Id format", nil)
		return
	}

	if len(orderIds) > 10 {
		util.SendResponse(w, http.StatusBadRequest, "sync order detail failed", "Max 10 order id", nil)
		return
	}

	ids := make([]primitive.ObjectID, 0)
	for _, id := range orderIds {
		parsed, err := primitive.ObjectIDFromHex(id)
		if err != nil {
			util.SendResponse(w, http.StatusBadRequest, "sync order detail failed", "Invalid Order Id format", nil)
			return
		}
		ids = append(ids, parsed)
	}

	orderList, err := c.OrderServ.FindByIds(ids)
	if err != nil {
		util.SendResponse(w, http.StatusInternalServerError, "sync order detail failed", "Internal Server error", nil)
		return
	}

	if orderList == nil || len(*orderList) == 0 {
		util.SendResponse(w, http.StatusNotFound, "sync order detail failed", "Order not found", nil)
		return
	}

	var wg sync.WaitGroup

	errChan := make(chan error, len(*orderList))
	wg.Add(len(*orderList))

	companyId, err := primitive.ObjectIDFromHex(company["_id"].(string))
	if err != nil {
		util.SendResponse(w, http.StatusInternalServerError, "sync order detail failed", "failed parse company id", nil)
		return
	}

	for _, order := range *orderList {
		go func(order schemas3.Order) {
			defer wg.Done()
			defer addCallback(addCallbackOpts{
				id:             companyId,
				shopId:         order.MpStoreId,
				orderId:        order.OrderId,
				queueServ:      c.QueueServ,
				callbackRepo:   c.CallbackServ,
				orderRepo:      c.OrderServ,
				siteConfigRepo: c.SiteConfigServ,
			})

			switch order.MarketplaceCode {
			case constant.MarketplaceCodeShopee:
				_, err = shopee.GetAndTranslateOrder(shopee.TranslateOrderCbOpts{
					Service:         c.OrderServ,
					StoreService:    c.StoreServ,
					ProductService:  c.ProductServ,
					TaskExecService: c.TaskExecServ,
					LogisticService: c.LogisticServ,
					OrderId:         order.OrderId,
					Data: schemas.TaskExecution{
						Company: companyId,
						RequestBody: map[string]interface{}{
							"shop_id":  order.MpStoreId,
							"order_id": order.OrderId,
						},
					},
				})
				log.Println("err", err)
				if err != nil {
					errChan <- err
					return
				}
			}
		}(order)
	}

	wg.Wait()
	close(errChan)

	if len(errChan) > 0 {
		util.SendResponse(w, http.StatusInternalServerError, "sync order detail failed", "Internal Server error", nil)
		return
	}

	util.SendResponse(w, http.StatusOK, "successfully sync order", "", nil)
}

func (c *OrderWebhookController) SyncOrderAll(w http.ResponseWriter, r *http.Request) {
	company := util.GetCompanyData(r)
	companyId, err := primitive.ObjectIDFromHex(company["_id"].(string))
	if err != nil {
		util.SendResponse(w, http.StatusInternalServerError, "sync order all failed", "failed parse company id", nil)
		return
	}

	storeList, err := c.StoreServ.GetAll(&schemas4.Store{Company: companyId})
	if err != nil {
		util.SendResponse(w, http.StatusInternalServerError, "sync order all failed", "Failed on fetching store data", nil)
		return
	}

	batch := uuid.NewString()
	baseUrl := config.ShopeeBaseUrl
	partnerId := config.ShopeePartnerId
	location, err := time.LoadLocation("Asia/Jakarta")
	if err != nil {
		util.SendResponse(w, http.StatusInternalServerError, "sync order all failed", "Failed on fetching store data", nil)
		return
	}

	ids := make([]string, 0)
	for _, store := range storeList {
		path := "/api/v2/order/get_order_list"

		now := time.Now().In(location)
		year, month, day := now.Date()

		id, err := c.TaskExecServ.Insert(schemas.TaskExecution{
			SchedulerTask:   primitive.NilObjectID,
			Batch:           batch,
			Status:          schemas.TaskExecutionStatusWaiting,
			Trigger:         schemas.TaskExecutionTriggerManual,
			Operation:       schemas2.SchedulerTaskOperationFetchOrderList,
			MarketplaceCode: store.MarketplaceCode,
			StartTime:       primitive.NewDateTimeFromTime(time.Now()),
			EndTime:         primitive.DateTime(0),
			RetryCount:      0,
			MaxRetries:      3,
			Company:         store.Company,
			Store:           store.ID,
			RequestParam: map[string]interface{}{
				"partner_id":       partnerId,
				"shop_id":          store.MarketplaceId,
				"time_range_field": "update_time",
				"time_from":        strconv.FormatInt(time.Now().Unix(), 10),
				"time_to":          strconv.FormatInt(time.Date(year, month, day, 23, 59, 59, 999, now.Location()).Unix(), 10),
				"page_size":        100,
			},
			RequestHeader: map[string]interface{}{
				"Content-Type": "application/json",
			},
			RequestBody:   nil,
			RequestUrl:    baseUrl + path,
			RequestMethod: string(constant.HttpMethodGet),
			Priority:      0,
		})
		if err != nil {
			log.Printf("error while insert to db: %v", err)
			util.SendResponse(w, http.StatusInternalServerError, "sync order all failed", "Failed on fetching store data", nil)
			return
		}

		ids = append(ids, id)
	}

	for _, id := range ids {
		err = c.QueueServ.AddJob(queue.JobStoreKey, queue.Job{
			Key:      "scheduler:task_execution",
			Priority: 0,
			Value: queue.JobValue{
				Event:     queue.EventWaiting,
				Id:        id,
				PrevEvent: "",
				Name:      strings.ToLower(string(schemas2.SchedulerTaskOperationFetchOrderList)),
				Platform:  constant.MarketplaceCodeShopee,
				MaxRetry:  3,
			},
		})
		if err != nil {
			log.Printf("error while insert to db: %v", err)
			util.SendResponse(w, http.StatusInternalServerError, "sync order all failed", "Failed on fetching store data", nil)
			return
		}
	}

	util.SendResponse(w, http.StatusOK, "successfully sync order", "", nil)
}

type getOrderDetailOpts struct {
	orderServ  *orders.Repository
	orderId    primitive.ObjectID
	httpWriter http.ResponseWriter
}

func getOrderDetail(opts getOrderDetailOpts) (*schemas3.Order, error) {
	order, err := opts.orderServ.FindById(opts.orderId)
	if err != nil {
		log.Printf("error while get order %s, err: %v", opts.orderId.Hex(), err)
		return nil, errors.New("error while get order")
	}

	if order == nil {
		return nil, errors.New("error while get order")
	}

	return order, nil
}

type insertTaskExecutionOpts struct {
	queueServ    *queue.Service
	taskExecServ interfaces.TaskExecutionsRepository
	order        schemas3.Order
	orderId      primitive.ObjectID
	startTime    time.Time
	httpWriter   http.ResponseWriter
	operation    schemas2.SchedulerTaskOperationEnum
	body         map[string]interface{}
	params       map[string]interface{}
	url          string
	method       string
}

func insertTaskExecution(opts insertTaskExecutionOpts) error {
	id, err := opts.taskExecServ.Insert(schemas.TaskExecution{
		Status:          schemas.TaskExecutionStatusWaiting,
		Trigger:         schemas.TaskExecutionTriggerManual,
		Operation:       opts.operation,
		MarketplaceCode: opts.order.MarketplaceCode,
		StartTime:       primitive.NewDateTimeFromTime(time.Now()),
		Batch:           uuid.NewString(),
		RetryCount:      0,
		MaxRetries:      5,
		Company:         opts.order.CompanyId,
		Store:           opts.order.StoreId,
		RequestParam:    opts.params,
		RequestHeader:   nil,
		RequestBody:     opts.body,
		RequestUrl:      opts.url,
		RequestMethod:   opts.method,
		Priority:        0,
		Running:         0,
		CreatedAt:       0,
		UpdatedAt:       0,
		ExpiredAt:       0,
	})
	if err != nil {
		log.Printf("error while get order %s, err: %v", opts.orderId.Hex(), err)
		return errors.New("internal Server error")
	}

	err = opts.queueServ.AddJob(queue.JobStoreKey, queue.Job{
		Key:      "scheduler:task_execution",
		Priority: 3,
		Value: queue.JobValue{
			Event:     queue.EventWaiting,
			Id:        id,
			PrevEvent: "",
			Name:      "confirm_order",
			Platform:  constant.MarketplaceCodeShopee,
			MaxRetry:  5,
		},
	})
	if err != nil {
		log.Printf("error while get order %s, err: %v", opts.orderId.Hex(), err)
		return errors.New("internal Server error")
	}

	return nil
}

func addCallback(opts addCallbackOpts) {
	order, err := opts.orderRepo.FindByOrderId(opts.orderId, constant.MarketplaceCodeShopee)
	if err != nil {
		log.Printf("err while finding order by id %s: %v", opts.orderId, err)
	}

	callbacks, err := opts.callbackRepo.GetUrlByCompany(opts.id)
	if err != nil {
		log.Printf("get callback err: %v", err)
	}

	for _, callback := range *callbacks {
		parse, err := util.StructToMap(order)
		if err != nil {
			log.Printf("parse err: %v", err)
		}

		insertCallback, err := opts.callbackRepo.InsertCallback(&schemas5.Callback{
			Company:         opts.id,
			Store:           opts.shopId,
			MarketplaceCode: constant.MarketplaceCodeShopee,
			CallbackUrl:     callback.ID,
			Data:            parse,
			Response:        nil,
			Status:          schemas5.CallbackStatusWaiting,
		})
		if err != nil {
			log.Printf("insert callback err: %v", err)
		}

		siteConfig, err := opts.siteConfigRepo.GetByKey(interfaces.GetByKeyOpts{Key: "callback.max-retry"})
		if err != nil {
			log.Printf("get callback max-retry err: %v", err)
			return
		}

		if siteConfig.Value == "" {
			siteConfig.Value = "3"
		}

		maxRetry, err := strconv.Atoi(siteConfig.Value)
		if err != nil {
			log.Printf("err while casting siteConfig: %v", err)
			return
		}

		err = opts.queueServ.AddJob(queue.CallbackStoreKey, queue.Job{
			Key: queue.CallbackStoreKey,
			Value: queue.JobValue{
				Event:     queue.EventWaiting,
				Id:        insertCallback,
				PrevEvent: "",
				Name:      "send-callback",
				Platform:  constant.MarketplaceCodeShopee,
				Retries:   0,
				MaxRetry:  maxRetry,
				Error:     "",
			},
			Priority: 2,
		})
		if err != nil {
			log.Printf("add callback err: %v", err)
		}
	}
}
