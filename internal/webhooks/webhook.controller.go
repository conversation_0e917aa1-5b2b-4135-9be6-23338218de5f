package webhooks

import (
	"context"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"abs-scheduler-go/internal/webhooks/schemas"
	"abs-scheduler-go/pkg/constant"
	"abs-scheduler-go/pkg/util"
	"github.com/gorilla/mux"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// WebhookController handles HTTP requests for webhooks.
type WebhookController struct {
	service *WebhookService
}

// NewWebhookController creates a new WebhookController.
func NewWebhookController(service *WebhookService) *WebhookController {
	return &WebhookController{service: service}
}

// GetAllWebhook handles the retrieval of all webhooks.
func (h *WebhookController) GetAllWebhook(w http.ResponseWriter, r *http.Request) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	filters := bson.M{}
	query := r.URL.Query()

	if mpCode := query.Get("marketPlaceCode"); mpCode != "" {
		filters["marketPlaceCode"] = mpCode
	}

	if isProcessed := query.Get("isProcessed"); isProcessed != "" {
		filters["isProcessed"] = isProcessed == "true"
	}

	limit, err := strconv.ParseUint(query.Get("limit"), 10, 32)
	if err != nil {
		util.SendResponse(w, http.StatusBadRequest, "get webhooks failed", "failed on parsing limit value", nil)
		return
	}
	skip, err := strconv.ParseUint(query.Get("skip"), 10, 32)
	if err != nil {
		util.SendResponse(w, http.StatusBadRequest, "get webhooks failed", "failed on parsing skip value", nil)
		return
	}

	webhooks, err := h.service.GetAllWebhook(FindAllOpts{
		Ctx:    ctx,
		Filter: filters,
		Limit:  uint(limit),
		Skip:   uint(skip),
		Sort:   nil,
	})
	if err != nil {
		util.SendResponse(w, http.StatusInternalServerError, "get webhooks failed", "Failed to fetch webhooks", nil)
		return
	}

	if webhooks == nil {
		webhooks = make([]*schemas.Webhook, 0)
	}

	util.SendResponse(w, http.StatusOK, "successfully get webhooks", "", webhooks)
}

// CreateWebhook handles the creation of a new webhook.
func (h *WebhookController) CreateWebhook(w http.ResponseWriter, r *http.Request) {

	// Get the marketPlaceCode from the URL
	vars := mux.Vars(r)
	marketPlaceCode := constant.MarketplaceCodeEnum(strings.ToUpper(vars["marketPlaceCode"]))

	// Check for valid marketplace code
	if !util.ValidateMarketPlaceCode(marketPlaceCode) {
		util.SendResponse(w, http.StatusBadRequest, "create webhooks failed", "Invalid marketplace code", nil)
		return
	}

	// Read request body
	bodyBytes, err := io.ReadAll(r.Body)
	if err != nil {
		util.SendResponse(w, http.StatusBadRequest, "create webhooks failed", "Failed to read request body", nil)
		return
	}

	// Decode JSON into a generic map
	jsonData, err := util.UnmarshalJSON(bodyBytes)
	if err != nil {
		util.SendResponse(w, http.StatusBadRequest, "create webhooks failed", "Invalid JSON payload", nil)
		return
	}

	MPStoreId, err := util.FindRecursiveShopID(marketPlaceCode, jsonData)
	if err != nil {
		MPStoreId = ""
	}

	webhook := schemas.Webhook{
		ID:              primitive.NewObjectID(),
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
		Data:            jsonData,
		IsProcessed:     false,
		MarketPlaceCode: marketPlaceCode,
		MPStoreId:       MPStoreId,
	}

	createdWebhook, err := h.service.CreateWebhook(&webhook)
	if err != nil {
		util.SendResponse(w, http.StatusInternalServerError, "create webhooks failed", "Failed to create webhook", nil)
		return
	}

	util.SendResponse(w, http.StatusCreated, "successfully create webhooks", "", createdWebhook)
}

// GetWebhookByID retrieves a webhook by its ID.
func (h *WebhookController) GetWebhookByID(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]

	webhook, err := h.service.GetWebhookByID(id)
	if err != nil {
		util.SendResponse(w, http.StatusNotFound, "get webhook detail failed", "Webhook not found", nil)
		return
	}

	util.SendResponse(w, http.StatusOK, "successfully get webhook detail", "", webhook)
}
