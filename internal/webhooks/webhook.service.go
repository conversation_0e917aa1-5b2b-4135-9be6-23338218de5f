package webhooks

import (
	"abs-scheduler-go/internal/webhooks/schemas"
)

type WebhookService struct {
	repo *WebhookRepository
}

// NewWebhookService creates a new WebhookService.
func NewWebhookService(repo *WebhookRepository) *WebhookService {
	return &WebhookService{repo: repo}
}

// GetAllWebhook get all webhook
func (s *WebhookService) GetAllWebhook(opts FindAllOpts) ([]*schemas.Webhook, error) {
	return s.repo.FindAll(opts)
}

// CreateWebhook creates a new webhook.
func (s *WebhookService) CreateWebhook(webhook *schemas.Webhook) (*schemas.Webhook, error) {
	return s.repo.CreateWebhook(webhook)
}

// GetWebhookByID retrieves a webhook by its ID.
func (s *WebhookService) GetWebhookByID(id string) (*schemas.Webhook, error) {
	return s.repo.GetWebhookByID(id)
}

func (s *WebhookService) UpdateWebhook(id string, webhook *schemas.Webhook) (*schemas.Webhook, error) {
	return s.repo.UpdateWebhookById(id, webhook)
}
