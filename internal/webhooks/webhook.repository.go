package webhooks

import (
	"context"
	"log"

	"abs-scheduler-go/internal/webhooks/schemas"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// WebhookRepository handles database operations for webhooks.
type WebhookRepository struct {
	Collection *mongo.Collection
}

// NewWebhookRepository creates a new WebhookRepository.
func NewWebhookRepository(db map[string]*mongo.Database) *WebhookRepository {

	return &WebhookRepository{
		Collection: db["main"].Collection("webhooks"),
	}
}

func (r *WebhookRepository) FindAll(opts FindAllOpts) ([]*schemas.Webhook, error) {
	findOpts := options.Find()

	if opts.Limit != 0 {
		findOpts.SetLimit(int64(opts.Limit))
	}
	if opts.Skip != 0 {
		findOpts.SetSkip(int64(opts.Skip))
	}
	if opts.Sort != nil {
		findOpts.SetSort(opts.Sort)
	}

	cursor, err := r.Collection.Find(opts.Ctx, opts.Filter, findOpts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(opts.Ctx)

	var webhooks []*schemas.Webhook
	if err := cursor.All(opts.Ctx, &webhooks); err != nil {
		return nil, err
	}
	return webhooks, nil
}

// CreateWebhook inserts a new webhook into the database.
func (r *WebhookRepository) CreateWebhook(webhook *schemas.Webhook) (*schemas.Webhook, error) {
	result, err := r.Collection.InsertOne(context.Background(), webhook)
	if err != nil {
		log.Printf("Failed to create webhook: %v", err)
		return nil, err
	}

	webhook.ID = result.InsertedID.(primitive.ObjectID)
	return webhook, nil
}

// GetWebhookByID retrieves a webhook by its ID.
func (r *WebhookRepository) GetWebhookByID(id string) (*schemas.Webhook, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, err
	}

	var webhook schemas.Webhook
	err = r.Collection.FindOne(context.Background(), bson.M{"_id": objectID}).Decode(&webhook)
	if err != nil {
		return nil, err
	}

	return &webhook, nil
}

func (r *WebhookRepository) UpdateWebhookById(id string, webhook *schemas.Webhook) (*schemas.Webhook, error) {
	exist, err := r.GetWebhookByID(id)
	if err != nil {
		return nil, err
	}

	var result schemas.Webhook
	err = r.Collection.FindOneAndUpdate(
		context.Background(),
		bson.M{"_id": exist.ID},
		bson.M{"$set": webhook},
		options.FindOneAndUpdate().SetReturnDocument(options.After),
	).Decode(&result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}
