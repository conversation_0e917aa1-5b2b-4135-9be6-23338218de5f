package webhooks

import (
	"net/http"

	"abs-scheduler-go/pkg/util"
	"github.com/gorilla/mux"
)

type Routes struct {
	WebhookController         *WebhookController
	OrderWebhookController    *OrderWebhookController
	ProductWebhookController  *ProductWebhookController
	LogisticWebhookController *LogisticWebhookController
}

func NewRoutes(opts Routes) *Routes {
	return &opts
}

func (r *Routes) publicRoutes(router *mux.Router) {
	router.HandleFunc("/webhooks", r.WebhookController.GetAllWebhook).Methods(http.MethodGet)
	router.HandleFunc("/webhooks/{marketPlaceCode}", r.WebhookController.CreateWebhook).Methods(http.MethodPost)
	router.HandleFunc("/webhooks/{id}", r.WebhookController.GetWebhookByID).Methods(http.MethodGet)
}

func (r *Routes) protectedRoutes(router *mux.Router) {
	subRouter := router.PathPrefix("").Subrouter()

	subRouter.Use(util.ValidateToken)

	subRouter.HandleFunc("/order/confirm", r.OrderWebhookController.ConfirmOrder).Methods(http.MethodPost)
	subRouter.HandleFunc("/order/fulfill", r.OrderWebhookController.FulfillOrder).Methods(http.MethodPost)
	subRouter.HandleFunc("/order/sync-all", r.OrderWebhookController.SyncOrderAll).Methods(http.MethodGet)
	subRouter.HandleFunc("/order/sync-detail", r.OrderWebhookController.SyncOrderDetail).Methods(http.MethodGet)
	subRouter.HandleFunc("/product/stock-adjustment", r.ProductWebhookController.AdjustStock).Methods(http.MethodPost)
	subRouter.HandleFunc("/logistic/sync", r.LogisticWebhookController.Sync).Methods(http.MethodGet)
}

// SetupRoutes configures the webhook routes
func (r *Routes) SetupRoutes() *mux.Router {
	router := mux.NewRouter()

	router.Use(util.InterceptResponseMiddleware)

	r.publicRoutes(router)
	r.protectedRoutes(router)

	return router
}
