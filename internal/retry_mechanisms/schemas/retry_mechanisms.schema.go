package schemas

import (
	"abs-scheduler-go/internal/task_executions/schemas"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type RetryMechanismSchema struct {
	TaskExecution primitive.ObjectID          `json:"taskExecution,omitempty" bson:"taskExecution,omitempty"`
	RetryCount    int8                        `json:"retryCount" bson:"retryCount"`
	MaxRetries    int8                        `json:"max_retries" bson:"maxRetries"`
	LastRetryTime primitive.DateTime          `json:"lastRetryTime" bson:"lastRetryTime"`
	NextRetryTime primitive.DateTime          `json:"nextRetryTime" bson:"nextRetryTime"`
	Status        schemas.TaskExecutionStatus `json:"status" bson:"status"`
	Company       primitive.ObjectID          `json:"company,omitempty" bson:"company,omitempty"`
	Store         primitive.ObjectID          `json:"store,omitempty" bson:"store,omitempty"`
}
