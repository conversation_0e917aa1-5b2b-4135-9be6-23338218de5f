package callbacks

import (
	"context"
	"errors"
	"fmt"

	"abs-scheduler-go/internal/callbacks/schemas"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Repository struct {
	urlColl *mongo.Collection
	cbColl  *mongo.Collection
}

func NewCallbackUrlRepository(db map[string]*mongo.Database) *Repository {
	urlColl := db["main"].Collection("callbackurls")
	cbColl := db["main"].Collection("callbacks")
	return &Repository{
		urlColl: urlColl,
		cbColl:  cbColl,
	}
}

func (r *Repository) GetUrlByCompany(company primitive.ObjectID) (*[]schemas.CallbackUrl, error) {
	var result []schemas.CallbackUrl
	curr, err := r.urlColl.Find(context.Background(), bson.M{"company": company})

	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, fmt.Errorf("error while get callback data %w", err)
	}

	for curr.Next(context.Background()) {
		var callback schemas.CallbackUrl
		if err := curr.Decode(&callback); err != nil {
			return nil, fmt.Errorf("error while decoding callback data: %w", err)
		}
		result = append(result, callback)
	}

	return &result, nil
}

func (r *Repository) GetCallbackById(id primitive.ObjectID) (*schemas.Callback, error) {
	var result schemas.Callback

	err := r.cbColl.FindOne(context.Background(), bson.M{"_id": id}).Decode(&result)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, fmt.Errorf("error while get callback data %w", err)
	}

	return &result, nil
}

func (r *Repository) GetCallbackUrlById(id primitive.ObjectID) (*schemas.CallbackUrl, error) {
	var result schemas.CallbackUrl

	err := r.urlColl.FindOne(context.Background(), bson.M{"_id": id}).Decode(&result)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, fmt.Errorf("error while get callback url data %w", err)
	}

	return &result, nil
}

func (r *Repository) UpdateStatus(id primitive.ObjectID, status schemas.CallbackStatus, response map[string]interface{}) error {
	res, err := r.cbColl.UpdateByID(context.Background(), id, bson.M{"$set": bson.M{"status": status, "response": response}})
	if err != nil {
		return err
	}

	if res.MatchedCount == 0 {
		return errors.New("callback status not matched")
	}

	return nil
}

func (r *Repository) InsertCallback(callback *schemas.Callback) (string, error) {
	insert, err := r.cbColl.InsertOne(context.Background(), callback)
	if err != nil {
		return "", fmt.Errorf("error while insert callback data %w", err)
	}

	return insert.InsertedID.(primitive.ObjectID).Hex(), nil
}
