package schemas

import (
	"abs-scheduler-go/pkg/constant"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type CallbackStatus string

const (
	CallbackStatusWaiting CallbackStatus = "waiting"
	CallbackStatusSuccess CallbackStatus = "success"
	CallbackStatusError   CallbackStatus = "error"
)

type Callback struct {
	ID              primitive.ObjectID           `bson:"_id,omitempty" json:"id"`
	CallbackUrl     primitive.ObjectID           `bson:"callbackUrl" json:"callbackUrl"`
	Company         primitive.ObjectID           `bson:"company" json:"company"`
	Store           string                       `bson:"store" json:"store"`
	MarketplaceCode constant.MarketplaceCodeEnum `bson:"marketplaceCode" json:"marketplaceCode"`
	Data            map[string]interface{}       `bson:"data" json:"data"`
	Response        map[string]interface{}       `bson:"response" json:"response"`
	Status          CallbackStatus               `bson:"status" json:"status"`
}
