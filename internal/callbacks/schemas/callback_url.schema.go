package schemas

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type CallbackUrl struct {
	ID        primitive.ObjectID `bson:"_id" json:"id"`
	Company   primitive.ObjectID `bson:"company" json:"company"`
	Url       string             `bson:"url" json:"url"`
	Key       string             `bson:"key" json:"key"`
	Status    bool               `bson:"status" json:"status"`
	CreatedAt primitive.DateTime `bson:"createdAt,omitempty" json:"createdAt,omitempty"`
	UpdatedAt primitive.DateTime `bson:"updatedAt,omitempty" json:"updatedAt,omitempty"`
}
