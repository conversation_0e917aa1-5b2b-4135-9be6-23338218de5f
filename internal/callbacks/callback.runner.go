package callbacks

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"time"

	"abs-scheduler-go/internal/callbacks/schemas"
	"abs-scheduler-go/internal/common/interfaces"
	"abs-scheduler-go/pkg/queue"
	"abs-scheduler-go/pkg/util"

	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type CallbackRunner struct {
	CallbackRepo interfaces.CallbackRepository
	QueueServ    *queue.Service
	Redis        *redis.Client
}

func NewCallbackRunner(opts CallbackRunner) *CallbackRunner {
	return &opts
}

func (c *CallbackRunner) ListToStream() {
	for {
		now := float64(time.Now().Unix())
		ctx := context.Background()

		jobData, err := c.Redis.ZRangeByScoreWithScores(ctx, queue.CallbackStoreKey, &redis.ZRangeBy{
			Min:   "0",
			Max:   fmt.Sprintf("%f", now),
			Count: 1,
		}).Result()

		if err != nil || len(jobData) == 0 {
			time.Sleep(500 * time.Millisecond)
			continue
		}

		for _, item := range jobData {
			var job queue.JobValue
			err = json.Unmarshal([]byte(jobData[0].Member.(string)), &job)
			if err != nil {
				return
			}

			_, err = c.Redis.XAdd(ctx, &redis.XAddArgs{
				Stream: queue.CallbackStream,
				Values: queue.EncodeJob(job),
			}).Result()

			if err == nil {
				c.Redis.ZRem(ctx, queue.CallbackStoreKey, item.Member)
				log.Println("Moved scheduled job", job.Id, "to Redis Stream")
			} else {
				log.Println("Moved scheduled job", job.Id, "to Redis Stream for processing")
			}
		}

		time.Sleep(100 * time.Millisecond)
	}
}

func (c *CallbackRunner) RunStream() {
	err := c.QueueServ.ConsumeQueue(queue.ConsumeQueueOpts{
		Key:          "callback",
		GroupName:    "callback",
		ScheduledKey: queue.CallbackStoreKey,
		ActiveKey:    queue.CallbackActiveKey,
		EventName:    queue.CallbackStream,
		SendToDlq:    true, // Enable DLQ for failed callbacks
		Callback: func(job *queue.Job) (skipMsg string, err error, delay time.Duration) {
			log.Printf("Processing callback job: %s (retry %d/%d)", job.Value.Id, job.Value.Retries, job.Value.MaxRetry)

			var body map[string]interface{}
			exponential := time.Duration(6*util.IntPow(5, job.Value.Retries)) * time.Second

			// parsing job id to mongo ObjectId
			id, err := primitive.ObjectIDFromHex(job.Value.Id)
			if err != nil {
				log.Printf("Invalid ObjectID format for callback job %s: %v", job.Value.Id, err)
				return "", fmt.Errorf("invalid ObjectID format: %w", err), exponential
			}

			// check is job exist
			task, err := c.CallbackRepo.GetCallbackById(id)
			if err != nil {
				log.Printf("Callback not found for job %s: %v", job.Value.Id, err)
				return "", fmt.Errorf("callback not found: %w", err), exponential
			}

			if task == nil {
				log.Printf("Callback task is nil for job %s", job.Value.Id)
				return "", fmt.Errorf("callback task is nil"), exponential
			}

			log.Printf("Processing callback %s for company %s", task.ID.Hex(), task.Company.Hex())

			// Update callback status in database
			defer func() {
				log.Printf("Updating callback status for job %s, error: %v", job.Value.Id, err)
				status := schemas.CallbackStatusSuccess
				if err != nil {
					status = schemas.CallbackStatusError
				}

				updateErr := c.CallbackRepo.UpdateStatus(id, status, body)
				if updateErr != nil {
					log.Printf("Error updating callback status for job %s: %v", job.Value.Id, updateErr)
				}
			}()

			url, err := c.CallbackRepo.GetCallbackUrlById(task.CallbackUrl)
			if err != nil {
				log.Printf("Failed to get callback URL for job %s: %v", job.Value.Id, err)
				return "", fmt.Errorf("failed to get callback URL: %w", err), exponential
			}

			if url == nil {
				log.Printf("Callback URL is nil for job %s", job.Value.Id)
				return "", fmt.Errorf("callback URL is nil"), exponential
			}

			headers := map[string]interface{}{"Content-Type": "application/json"}
			if url.Key != "" {
				headers["x-api-key"] = url.Key
			}

			log.Printf("Sending callback to URL: %s for job %s", url.Url, job.Value.Id)

			// processing queue - Fixed timeout bug (was 15 & time.Second, should be 15 * time.Second)
			res, err := util.SendRequest(context.Background(), util.SendRequestOpts{
				Timeout: 15 * time.Second,
				Url:     url.Url,
				Method:  http.MethodPost,
				Params:  nil,
				Body:    task.Data,
				Headers: headers,
			})

			if err != nil {
				log.Printf("Failed to send callback for job %s: %v", job.Value.Id, err)
				body = map[string]interface{}{
					"status": false,
					"code":   http.StatusInternalServerError,
					"error":  err.Error(),
				}
				return "", err, exponential
			}

			if res.Body == nil {
				log.Printf("Response body is nil for callback job %s", job.Value.Id)
				body = map[string]interface{}{
					"status": false,
					"code":   res.StatusCode,
					"error":  "response body is nil",
				}
				return "", errors.New("response body is nil"), exponential
			}
			defer res.Body.Close()

			if res.StatusCode != http.StatusOK {
				log.Printf("Callback returned non-OK status %d for job %s", res.StatusCode, job.Value.Id)
				body = map[string]interface{}{
					"status": false,
					"code":   res.StatusCode,
					"error":  fmt.Sprintf("HTTP status %d", res.StatusCode),
				}
				return "", fmt.Errorf("got status code %v", res.StatusCode), exponential
			}

			err = json.NewDecoder(res.Body).Decode(&body)
			if err != nil {
				log.Printf("Failed to decode response body for callback job %s: %v", job.Value.Id, err)
				return "", err, exponential
			}

			log.Printf("Callback completed successfully for job %s", job.Value.Id)
			return "", nil, 0
		},
	})
	if err != nil {
		log.Fatalf("Error while consuming queue callbacks %v", err)
	}
}
