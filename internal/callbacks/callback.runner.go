package callbacks

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"time"

	"abs-scheduler-go/internal/callbacks/schemas"
	"abs-scheduler-go/internal/common/interfaces"
	"abs-scheduler-go/pkg/queue"
	"abs-scheduler-go/pkg/util"
	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type CallbackRunner struct {
	CallbackRepo interfaces.CallbackRepository
	QueueServ    *queue.Service
	Redis        *redis.Client
}

func NewCallbackRunner(opts CallbackRunner) *CallbackRunner {
	return &opts
}

func (c *CallbackRunner) ListToStream() {
	for {
		now := float64(time.Now().Unix())
		ctx := context.Background()

		jobData, err := c.Redis.ZRangeByScoreWithScores(ctx, queue.CallbackStoreKey, &redis.ZRangeBy{
			Min:   "0",
			Max:   fmt.Sprintf("%f", now),
			Count: 1,
		}).Result()

		if err != nil || len(jobData) == 0 {
			time.Sleep(500 * time.Millisecond)
			continue
		}

		for _, item := range jobData {
			var job queue.JobValue
			err = json.Unmarshal([]byte(jobData[0].Member.(string)), &job)
			if err != nil {
				return
			}

			_, err = c.Redis.XAdd(ctx, &redis.XAddArgs{
				Stream: queue.CallbackStream,
				Values: queue.EncodeJob(job),
			}).Result()

			if err == nil {
				c.Redis.ZRem(ctx, queue.CallbackStoreKey, item.Member)
				log.Println("Moved scheduled job", job.Id, "to Redis Stream")
			} else {
				log.Println("Moved scheduled job", job.Id, "to Redis Stream for processing")
			}
		}

		time.Sleep(100 * time.Millisecond)
	}
}

func (c *CallbackRunner) RunStream() {
	err := c.QueueServ.ConsumeQueue(queue.ConsumeQueueOpts{
		Key:          "callback",
		GroupName:    "callback",
		ScheduledKey: queue.CallbackStoreKey,
		ActiveKey:    queue.CallbackActiveKey,
		EventName:    queue.CallbackStream,
		Callback: func(job *queue.Job) (skipMsg string, err error, delay time.Duration) {
			var body map[string]interface{}
			exponential := time.Duration(6*util.IntPow(5, job.Value.Retries)) * time.Second

			// parsing job id to mongo ObjectId
			id, err := primitive.ObjectIDFromHex(job.Value.Id)
			if err != nil {
				return "", err, exponential
			}

			// check is job exist
			task, err := c.CallbackRepo.GetCallbackById(id)
			if err != nil {
				return "", err, exponential
			}

			// Defer handling of database update
			defer func() {
				log.Printf("statusErr: %v", err)
				status := schemas.CallbackStatusSuccess
				if err != nil {
					status = schemas.CallbackStatusError
				}

				updateErr := c.CallbackRepo.UpdateStatus(id, status, body)
				if updateErr != nil {
					log.Printf("Error while updating status: %v", updateErr)
				}

				if status == schemas.CallbackStatusError && job.Value.Retries <= job.Value.MaxRetry {
					task.ID = primitive.NewObjectID()
					task.Status = schemas.CallbackStatusWaiting
					_, insertErr := c.CallbackRepo.InsertCallback(task)
					if insertErr != nil {
						log.Printf("Error inserting task: %v", insertErr)
					} else {
						job.Value.Id = task.ID.Hex()
					}
				}
			}()

			url, err := c.CallbackRepo.GetCallbackUrlById(task.CallbackUrl)
			if err != nil {
				return "", err, exponential
			}

			headers := map[string]interface{}{"Content-Type": "application/json"}
			if url.Key != "" {
				headers["x-api-key"] = url.Key
			}

			// processing queue
			res, err := util.SendRequest(context.Background(), util.SendRequestOpts{
				Timeout: 15 & time.Second,
				Url:     url.Url,
				Method:  http.MethodPost,
				Params:  nil,
				Body:    task.Data,
				Headers: headers,
			})

			if err != nil {
				body = map[string]interface{}{
					"status": false,
					"code":   http.StatusInternalServerError,
					"error":  err.Error(),
				}
				return "", err, exponential
			}

			if res.Body == nil {
				body = map[string]interface{}{
					"status": false,
					"code":   res.StatusCode,
					"error":  "response body is nil",
				}
				return "", errors.New("response body is nil"), exponential
			}
			defer res.Body.Close()

			if res.StatusCode != http.StatusOK {
				return "", fmt.Errorf("got status code %v", res.StatusCode), exponential
			}

			err = json.NewDecoder(res.Body).Decode(&body)
			if err != nil {
				return "", err, exponential
			}

			return "", nil, 0
		},
	})
	if err != nil {
		log.Fatalf("Error while consuming queue callbacks %v", err)
	}
}
