package schemas

import (
	"abs-scheduler-go/internal/scheduler_tasks/schemas"
	"abs-scheduler-go/pkg/constant"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type TaskExecutionStatusEnum string

const (
	TaskExecutionStatusWaiting        TaskExecutionStatusEnum = "WAITING"
	TaskExecutionStatusStart          TaskExecutionStatusEnum = "START"
	TaskExecutionStatusSuccess        TaskExecutionStatusEnum = "SUCCESS"
	TaskExecutionStatusFailed         TaskExecutionStatusEnum = "FAILED"
	TaskExecutionStatusPartialSuccess TaskExecutionStatusEnum = "PARTIAL_SUCCESS"
	TaskExecutionStatusFailedToQueue  TaskExecutionStatusEnum = "FAILED_TO_QUEUE"
)

type TaskExecutionTriggerEnum string

const (
	TaskExecutionTriggerManual TaskExecutionTriggerEnum = "MANUAL"
	TaskExecutionTriggerCron   TaskExecutionTriggerEnum = "CRON"
)

type TaskExecution struct {
	ID              primitive.ObjectID                 `json:"id" bson:"_id,omitempty"`
	SchedulerTask   primitive.ObjectID                 `json:"schedulerTask,omitempty" bson:"schedulerTask,omitempty"`
	Batch           string                             `json:"batch" bson:"batch"`
	Status          TaskExecutionStatusEnum            `json:"status" bson:"status"`
	Trigger         TaskExecutionTriggerEnum           `json:"trigger" bson:"trigger"`
	Operation       schemas.SchedulerTaskOperationEnum `json:"operation" bson:"operation"`
	MarketplaceCode constant.MarketplaceCodeEnum       `json:"marketplaceCode" bson:"marketplaceCode"`
	StartTime       primitive.DateTime                 `json:"startTime" bson:"startTime"`
	EndTime         primitive.DateTime                 `json:"endTime" bson:"endTime,omitempty"`
	ErrorDetails    []string                           `json:"errorDetails" bson:"errorDetails"`
	RetryCount      uint8                              `json:"retryCount" bson:"retryCount"`
	MaxRetries      uint8                              `json:"maxRetries" bson:"maxRetries"`
	Company         primitive.ObjectID                 `json:"company" bson:"company"`
	Store           primitive.ObjectID                 `json:"store" bson:"store"`
	RequestParam    map[string]interface{}             `json:"requestParam" bson:"requestParam"`
	RequestHeader   map[string]interface{}             `json:"requestHeader" bson:"requestHeader"`
	RequestBody     map[string]interface{}             `json:"requestBody" bson:"requestBody"`
	RequestUrl      string                             `json:"requestUrl" bson:"requestUrl"`
	RequestMethod   string                             `json:"requestMethod" bson:"requestMethod"`
	Priority        uint8                              `json:"priority" bson:"priority"`
	Running         uint8                              `json:"running" bson:"running"`
	CreatedAt       primitive.DateTime                 `bson:"createdAt,omitempty" json:"createdAt,omitempty"`
	UpdatedAt       primitive.DateTime                 `bson:"updatedAt,omitempty" json:"updatedAt,omitempty"`
	ExpiredAt       primitive.DateTime                 `bson:"expiredAt,omitempty" json:"expiredAt,omitempty"`
}
