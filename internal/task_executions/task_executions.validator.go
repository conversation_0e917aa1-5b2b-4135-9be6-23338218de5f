package task_executions

import (
	"fmt"
	"strings"

	"abs-scheduler-go/internal/task_executions/schemas"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func ValidateInsert(payload *schemas.TaskExecution) error {
	errors := make(map[string]string)
	if payload.Batch == "" {
		errors["batch"] = "batch is required"
	}
	if payload.Company.IsZero() {
		errors["company"] = "company is required"
	}
	if payload.MarketplaceCode == "" {
		errors["marketplaceCode"] = "marketplaceCode is required"
	}
	if payload.MaxRetries == 0 {
		errors["maxRetries"] = "maxRetries is required"
	}
	if payload.Operation == "" {
		errors["operation"] = "operation is required"
	}
	if payload.Store.IsZero() {
		errors["store"] = "store is required"
	}
	if payload.ErrorDetails == nil {
		payload.ErrorDetails = []string{}
	}

	if len(errors) > 0 {
		var errorMessages []string
		for field, msg := range errors {
			errorMessages = append(errorMessages, fmt.Sprintf("%s: %s", field, msg))
		}
		return fmt.Errorf("validation failed: %s", strings.Join(errorMessages, ", "))
	}

	return nil
}

func ValidateDelete(ids []string) []string {
	var errors []string
	for _, id := range ids {
		if _, err := primitive.ObjectIDFromHex(id); err != nil {
			errors = append(errors, err.Error())
		}
	}

	return errors
}
