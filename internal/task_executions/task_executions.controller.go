package task_executions

import (
	"context"
	"fmt"
	"log"
	"math"
	"time"

	"abs-scheduler-go/internal/common/interfaces"
	schemas2 "abs-scheduler-go/internal/dead_letter_queues/schemas"
	"abs-scheduler-go/internal/task_executions/schemas"
	"abs-scheduler-go/pkg/constant"
	"abs-scheduler-go/pkg/marketplaces/shopee"
	"abs-scheduler-go/pkg/queue"

	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Executor struct {
	Shopee *shopee.Executor
}

type TaskExecutionController struct {
	Db            map[string]*mongo.Database
	QueueServ     *queue.Service
	Redis         *redis.Client
	Repo          interfaces.TaskExecutionsRepository
	ServiceFailed interfaces.DeadLetterQueuesRepository
	OrderRepo     interfaces.OrderRepository
	SchedulerRepo interfaces.SchedulerRepository
	Executor      Executor
}

func NewTaskExecutionCtrl(opts TaskExecutionController) *TaskExecutionController {
	return &opts
}

func (c *TaskExecutionController) RunQueue() {
	err := c.QueueServ.ConsumeQueue(queue.ConsumeQueueOpts{
		Key:          "scheduler:task_execution",
		GroupName:    "task_execution",
		EventName:    queue.WaitingStream,
		SendToDlq:    true,
		ScheduledKey: queue.ScheduledJobStoreKey,
		Callback: func(job *queue.Job) (skipMsg string, err error, delay time.Duration) {
			log.Printf("Processing task execution job: %s (retry %d/%d)", job.Value.Id, job.Value.Retries, job.Value.MaxRetry)

			// parsing job id to mongo ObjectId
			id, err := primitive.ObjectIDFromHex(job.Value.Id)
			if err != nil {
				log.Printf("Invalid ObjectID format for job %s: %v", job.Value.Id, err)
				return "", fmt.Errorf("invalid ObjectID format: %w", err), 0
			}

			// check is job exist
			task, err := c.Repo.GetSingle(id)
			if err != nil {
				log.Printf("Task not found for job %s: %v", job.Value.Id, err)
				return "", fmt.Errorf("task not found: %w", err), 0
			}

			log.Printf("Processing task %s for marketplace %s, operation %s", task.ID.Hex(), task.MarketplaceCode, task.Operation)

			// decrement job queue count when queue from scheduler task
			defer func() {
				if err != nil && job.Value.Retries < job.Value.MaxRetry {
					return
				}

				if !task.SchedulerTask.IsZero() {
					ctx := context.Background()
					key := fmt.Sprintf("scheduler-%s-batch-%s", task.SchedulerTask.Hex(), task.Batch)

					res, err := c.Redis.Decr(ctx, key).Result()
					if err != nil {
						log.Printf("Error while decrement queue job count: %v", err)
					}

					if res <= 0 {
						c.Redis.Del(ctx, key)
						_, err := c.SchedulerRepo.SetDone(task.SchedulerTask)
						if err != nil {
							log.Printf("Error while setting queue done db: %v", err)
						}
					}
				}
			}()

			// processing queue
			skip, err := proceedQueue(task, c.Executor)
			delay = 5 * time.Second * time.Duration(math.Pow(2, float64(job.Value.Retries)))

			if err != nil {
				log.Printf("Task execution failed for job %s: %v", job.Value.Id, err)
				return "", err, delay
			}
			if skip != "" {
				log.Printf("Task execution skipped for job %s: %s", job.Value.Id, skip)
				return skip, nil, 1 * time.Second
			}

			log.Printf("Task execution completed successfully for job %s", job.Value.Id)
			return "", nil, 0
		},
	})
	if err != nil {
		log.Fatalf("Error while consuming queue task_execution %v", err)
	}
}

func (c *TaskExecutionController) StreamFailedEvents() {
	err := c.QueueServ.ConsumeQueue(queue.ConsumeQueueOpts{
		Key:       "scheduler:task_execution",
		GroupName: "task_execution",
		EventName: queue.FailedStream,
		SendToDlq: false,
		Callback: func(job *queue.Job) (skipMsg string, err error, delay time.Duration) {
			id, err := primitive.ObjectIDFromHex(job.Value.Id)
			if err != nil {
				return "", err, 0
			}

			task, err := c.Repo.GetSingle(id)
			if err != nil {
				return "", err, 0
			}

			_, err = c.ServiceFailed.Insert(schemas2.DeadLetterQueues{
				SchedulerTask:    task.SchedulerTask,
				Batch:            task.Batch,
				RetryCount:       task.RetryCount,
				MaxRetries:       task.MaxRetries,
				LastErrorDetails: job.Value.Error,
				Company:          task.Company,
				Store:            task.Store,
			})
			if err != nil {
				return "", err, 0
			}

			return "", nil, 0
		},
	})
	if err != nil {
		log.Fatalf("Error while consuming failed queue task_execution %v", err)
	}
}

func proceedQueue(job schemas.TaskExecution, serv Executor) (string, error) {
	if job.MarketplaceCode == constant.MarketplaceCodeShopee {
		skip, err := serv.Shopee.Execute(job)
		if err != nil {
			return "", err
		}
		if skip != "" {
			return skip, nil
		}
	}
	return "", nil
}
