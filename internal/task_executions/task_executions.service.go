package task_executions

import (
	"context"
	"errors"
	"fmt"
	"time"

	"abs-scheduler-go/internal/task_executions/schemas"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Repository struct {
	collection *mongo.Collection
}

func NewTaskExecutionRepository(db map[string]*mongo.Database) *Repository {
	collection := db["main"].Collection("taskexecutions")

	return &Repository{collection}
}

func (r *Repository) GetMany(filter bson.M) ([]schemas.TaskExecution, error) {
	var task []schemas.TaskExecution

	ctx := context.Background()

	curr, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	defer curr.Close(ctx)

	err = curr.All(ctx, &task)
	if err != nil {
		return nil, err
	}

	return task, nil
}

func (r *Repository) GetByIds(ids []primitive.ObjectID) ([]schemas.TaskExecution, error) {
	var tasks []schemas.TaskExecution

	ctx := context.Background()

	cursor, err := r.collection.Find(ctx, bson.M{"_id": bson.M{"$in": ids}})
	if err != nil {
		return nil, err
	}

	for cursor.Next(ctx) {
		var task schemas.TaskExecution
		if err := cursor.Decode(&task); err != nil {
			return nil, err
		}
		tasks = append(tasks, task)
	}

	return tasks, nil
}

func (r *Repository) GetSingle(id primitive.ObjectID) (schemas.TaskExecution, error) {
	var task schemas.TaskExecution

	ctx := context.Background()

	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&task)
	if err != nil {
		return schemas.TaskExecution{}, err
	}

	return task, nil
}

func (r *Repository) Insert(data schemas.TaskExecution) (string, error) {
	err := ValidateInsert(&data)
	if err != nil {
		return "", err
	}

	data.CreatedAt = primitive.NewDateTimeFromTime(time.Now())
	data.UpdatedAt = primitive.NewDateTimeFromTime(time.Now())

	ctx := context.Background()
	insert, err := r.collection.InsertOne(ctx, data)
	if err != nil {
		return "", err
	}

	return insert.InsertedID.(primitive.ObjectID).Hex(), nil
}

func (r *Repository) InsertMany(data []schemas.TaskExecution) ([]string, error) {
	var mock []interface{}

	for i, item := range data {
		err := ValidateInsert(&item)
		if err != nil {
			return []string{}, errors.New(fmt.Sprintf("Error inserting task execution #%d: %s", i, err.Error()))
		}

		item.CreatedAt = primitive.NewDateTimeFromTime(time.Now())
		item.UpdatedAt = primitive.NewDateTimeFromTime(time.Now())

		mock = append(mock, item)
	}

	ctx := context.Background()
	insert, err := r.collection.InsertMany(ctx, mock)
	if err != nil {
		return []string{}, err
	}

	var result []string
	for _, i := range insert.InsertedIDs {
		result = append(result, i.(primitive.ObjectID).Hex())
	}

	return result, nil
}

func (r *Repository) GetToRun(limit uint16) ([]schemas.TaskExecution, error) {
	var result []schemas.TaskExecution
	var onQueue []string
	ctx := context.Background()

	if limit == 0 {
		limit = 10
	}

	data, err := r.collection.Aggregate(ctx, bson.A{
		bson.M{
			"$match": bson.M{
				"running": 0,
				"status":  schemas.TaskExecutionStatusWaiting,
			},
		},
		bson.M{
			"$limit": limit,
		},
	})
	if err != nil {
		return nil, err
	}

	var decoded []schemas.TaskExecution
	if err = data.All(ctx, &decoded); err != nil {
		return nil, err
	}

	for _, item := range decoded {
		if item.Running > 1 {
			onQueue = append(onQueue, item.ID.String())
		} else {
			result = append(result, item)
		}
	}

	_, err = r.collection.UpdateMany(ctx, bson.M{"_id": onQueue}, bson.M{
		"$set": bson.M{
			"running": bson.M{"$inc": 1},
		},
	})

	return result, nil
}

func (r *Repository) DeleteMany(ids []string) (interface{}, error) {
	validation := ValidateDelete(ids)
	if validation != nil {
		return nil, fmt.Errorf("format %v invalid ObjectId type", validation)
	}

	del, err := r.collection.DeleteMany(context.Background(), bson.M{"_id": bson.M{"$in": ids}})
	if err != nil {
		return nil, err
	}

	return del, nil
}

func (r *Repository) UpdateStatus(id primitive.ObjectID, status schemas.TaskExecutionStatusEnum) (interface{}, error) {
	mock := bson.M{
		"status":    status,
		"updatedAt": primitive.NewDateTimeFromTime(time.Now()),
	}
	if status == schemas.TaskExecutionStatusSuccess {
		mock["expiredAt"] = primitive.NewDateTimeFromTime(time.Now().Add(2 * time.Hour))
		mock["errorDetails"] = bson.A{}
	}

	update, err := r.collection.UpdateByID(context.Background(), id, bson.M{"$set": mock})
	if err != nil {
		return nil, err
	}

	return update, nil
}

func (r *Repository) UpdateBody(id primitive.ObjectID, params map[string]interface{}) (interface{}, error) {
	update, err := r.collection.UpdateByID(context.Background(), id, bson.M{"$set": bson.M{"requestBody": params}})
	if err != nil {
		return nil, err
	}

	return update, nil
}

func (r *Repository) SetError(id primitive.ObjectID, errorMsg string, retry uint8, maxRetry uint8) (schemas.TaskExecution, error) {
	set := bson.M{
		"status":     schemas.TaskExecutionStatusFailed,
		"retryCount": retry,
		"updatedAt":  primitive.NewDateTimeFromTime(time.Now()),
	}

	if retry >= maxRetry {
		set["expiredAt"] = primitive.NewDateTimeFromTime(time.Now().Add(2 * time.Hour))
	}

	var update schemas.TaskExecution
	err := r.collection.FindOneAndUpdate(
		context.Background(),
		bson.M{"_id": id},
		bson.M{
			"$set": set,
			"$push": bson.M{
				"errorDetails": errorMsg,
			},
		},
	).Decode(&update)

	if err != nil {
		return schemas.TaskExecution{}, err
	}

	return update, nil
}
