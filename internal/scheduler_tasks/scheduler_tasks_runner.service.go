package scheduler_tasks

import (
	"log"

	schemas2 "abs-scheduler-go/internal/scheduler_tasks/schemas"
	"abs-scheduler-go/internal/stores"
	"abs-scheduler-go/internal/task_executions"
	"abs-scheduler-go/internal/task_executions/schemas"
	"abs-scheduler-go/pkg/constant"
	"abs-scheduler-go/pkg/marketplaces/shopee"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type SchedulerTaskRunnerService struct {
	Db         map[string]*mongo.Database
	Repository *task_executions.Repository
}

func NewSchedulerTaskRunnerService(opts SchedulerTaskRunnerService) *SchedulerTaskRunnerService {
	return &opts
}

type FetchOrderListOpts struct {
	Schedule    schemas2.SchedulerTask
	SucceedData *[]string
}

func (s *SchedulerTaskRunnerService) FetchOrderList(opts FetchOrderListOpts) error {
	storeServ := stores.NewStoreRepository(s.Db)
	storeList, err := storeServ.GetAll(nil)
	if err != nil {
		return err
	}
	log.Printf("storeList length %v", len(storeList))

	//taskExecutionServ := task_executions.NewTaskExecutionRepository(s.Db["main"])
	for _, store := range storeList {
		switch store.MarketplaceCode {
		case constant.MarketplaceCodeShopee:
			inserted, err := shopee.GenerateOrderTaskList(shopee.FetchOrderListOpts{
				Schedule: opts.Schedule,
				Store:    store,
				Service:  s.Repository,
			})
			if err != nil {
				log.Printf("Error while generating task executions store %v with detail %v", store.MarketplaceCode, err.Error())
				return err
			}
			*opts.SucceedData = append(*opts.SucceedData, inserted)
		}
	}

	return nil
}

func (s *SchedulerTaskRunnerService) FetchProductList(opts FetchOrderListOpts) error {
	storeServ := stores.NewStoreRepository(s.Db)
	storeList, err := storeServ.GetAll(nil)
	if err != nil {
		return err
	}

	for _, store := range storeList {
		switch store.MarketplaceCode {
		case constant.MarketplaceCodeShopee:
			inserted, err := shopee.GenerateProductTaskList(shopee.FetchOrderListOpts{
				Schedule: opts.Schedule,
				Store:    store,
				Service:  s.Repository,
			})
			if err != nil {
				log.Printf("Error while generating product task executions store %v with detail %v", store.MarketplaceCode, err.Error())
				return err
			}
			*opts.SucceedData = append(*opts.SucceedData, inserted)
		}
	}

	return nil
}

func (s *SchedulerTaskRunnerService) UpdateStatusToError(id string) error {
	convert, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	_, err = s.Repository.UpdateStatus(convert, schemas.TaskExecutionStatusFailedToQueue)
	if err != nil {
		return err
	}

	return nil
}

func (s *SchedulerTaskRunnerService) RollbackExecution(data map[constant.MarketplaceCodeEnum][]string) error {
	if len(data[constant.MarketplaceCodeShopee]) > 0 {
		_, err := s.Repository.DeleteMany(data["shopee"])
		if err != nil {
			return err
		}
	}

	return nil
}
