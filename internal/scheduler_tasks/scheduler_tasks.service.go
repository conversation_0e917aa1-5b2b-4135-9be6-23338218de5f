package scheduler_tasks

import (
	"context"
	"errors"
	"time"

	"abs-scheduler-go/internal/scheduler_tasks/schemas"
	"abs-scheduler-go/pkg/util"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Repository struct {
	collection *mongo.Collection
}

func NewSchedulerTaskRepository(db map[string]*mongo.Database) *Repository {
	collection := db["main"].Collection("schedulertasks")

	return &Repository{collection}
}

func (r *Repository) GetRunningScheduler() ([]schemas.SchedulerTask, error) {
	var task []schemas.SchedulerTask

	filter := bson.M{
		"$or": []bson.M{
			{
				"$or": []bson.M{
					{"nextRunTime": bson.M{"$lte": time.Now()}},
					{"nextRunTime": nil},
				},
			},
			{
				"$or": []bson.M{
					{"lastBatch": nil},
					{"lastBatch": ""},
				},
			},
		},
	}
	ctx := context.Background()

	curr, err := r.collection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	err = curr.All(ctx, &task)
	if err != nil {
		return nil, err
	}

	return task, nil
}

func (r *Repository) Update(task *schemas.SchedulerTask) (*schemas.SchedulerTask, error) {
	var data schemas.SchedulerTask
	ctx := context.Background()

	err := r.collection.FindOneAndUpdate(ctx, bson.M{"_id": task.ID}, bson.M{
		"$set": bson.M{
			"name":           task.Name,
			"description":    task.Description,
			"operation":      task.Operation,
			"frequencyUnit":  task.FrequencyUnit,
			"frequencyValue": task.FrequencyValue,
			"frequencyTime":  task.FrequencyTime,
			"frequencyDate":  task.FrequencyDate,
			"nextRunTime":    task.NextRunTime,
			"lastRunTime":    task.LastRunTime,
			"lastBatch":      task.LastBatch,
			"maxRetries":     task.MaxRetries,
			"status":         task.Status,
		},
	}).Decode(&data)
	if err != nil {
		return nil, err
	}

	return &data, nil
}

func (r *Repository) SetDone(id primitive.ObjectID) (*schemas.SchedulerTask, error) {
	var data schemas.SchedulerTask
	ctx := context.Background()

	var exist schemas.SchedulerTask
	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&exist)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, errors.New("task not found")
		}

		return nil, err
	}

	err = r.collection.FindOneAndUpdate(ctx, bson.M{"_id": id}, bson.M{
		"$set": bson.M{
			"nextRunTime": util.AddTime(time.Now(), uint(exist.FrequencyValue), exist.FrequencyTime),
			"lastRunTime": primitive.NewDateTimeFromTime(time.Now()),
			"lastBatch":   nil,
		},
	}).Decode(&data)
	if err != nil {
		return nil, err
	}

	return &data, nil
}
