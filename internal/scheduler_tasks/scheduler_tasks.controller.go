package scheduler_tasks

import (
	"context"
	"errors"
	"log"
	"strings"
	"sync"
	"time"

	schemas2 "abs-scheduler-go/internal/scheduler_tasks/schemas"
	"abs-scheduler-go/internal/stores"
	"abs-scheduler-go/internal/task_executions"
	"abs-scheduler-go/internal/task_executions/schemas"
	"abs-scheduler-go/internal/webhooks"
	"abs-scheduler-go/pkg/constant"
	"abs-scheduler-go/pkg/queue"
	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type SchedulerTaskController struct {
	Db           map[string]*mongo.Database
	QueueServ    *queue.Service
	WebhookServ  *webhooks.WebhookService
	TaskExecServ *task_executions.Repository
	StoreServ    *stores.Repository
	Redis        *redis.Client
}

func NewSchedulerTaskCtrl(opts SchedulerTaskController) *SchedulerTaskController {
	return &opts
}

func (c *SchedulerTaskController) RunScheduler(running *bool) {
	*running = true

	schedulerTaskServ := NewSchedulerTaskRepository(c.Db)

	list, err := schedulerTaskServ.GetRunningScheduler()
	if err != nil {
		panic(err)
	}

	if len(list) == 0 {
		*running = false
		return
	}

	var schedulerTaskIds []primitive.ObjectID
	var schedulerBatches []string
	for _, task := range list {
		schedulerTaskIds = append(schedulerTaskIds, task.ID)

		if task.LastBatch != "" {
			schedulerBatches = append(schedulerBatches, task.LastBatch)
		}
	}

	if len(schedulerBatches) == 0 {
		schedulerBatches = []string{}
	}

	taskExecutionServ := task_executions.NewTaskExecutionRepository(c.Db)
	tasks, err := taskExecutionServ.GetMany(bson.M{
		"schedulerTask": bson.M{"$in": schedulerTaskIds},
		"batch":         bson.M{"$in": schedulerBatches},
		"status":        schemas.TaskExecutionStatusWaiting,
	})
	if err != nil {
		panic(err)
	}

	taskMaps := make(map[string]int)
	for _, task := range tasks {
		if _, exists := taskMaps[task.Batch]; !exists {
			taskMaps[task.Batch] = 0
		}

		taskMaps[task.Batch]++
	}

	runnerServ := NewSchedulerTaskRunnerService(SchedulerTaskRunnerService{Db: c.Db, Repository: taskExecutionServ})
	var wg sync.WaitGroup
	var mu sync.Mutex

	for _, task := range list {
		wg.Add(1)
		go func(wg *sync.WaitGroup) {
			defer wg.Done()

			if task.LastBatch != "" && task.NextRunTime < primitive.NewDateTimeFromTime(time.Now()) {
				err := c.Redis.Get(context.Background(), "scheduler-"+task.ID.Hex()+"-batch-"+task.LastBatch).Err()
				if err != nil {
					if errors.Is(err, redis.Nil) {
						task.LastBatch = ""
					} else {
						log.Printf("error while get job count at job %v: %v", task, err.Error())
					}
				}
			}

			var succeedInsertedTask []string
			task.LastBatch = uuid.NewString()
			task.LastRunTime = primitive.NewDateTimeFromTime(time.Now())
			task.NextRunTime = primitive.NewDateTimeFromTime(time.Now().Add(time.Minute * 5))

			if task.Operation == schemas2.SchedulerTaskOperationFetchOrderList {
				err := runnerServ.FetchOrderList(FetchOrderListOpts{
					Schedule:    task,
					SucceedData: &succeedInsertedTask,
				})
				if err != nil {
					log.Printf("error while running task %v: %v", task, err.Error())

					mu.Lock()
					defer mu.Unlock()
				}
			}
			if task.Operation == schemas2.SchedulerTaskOperationFetchProductList {
				err := runnerServ.FetchProductList(FetchOrderListOpts{
					Schedule:    task,
					SucceedData: &succeedInsertedTask,
				})
				if err != nil {
					log.Printf("error while running task %v: %v", task, err.Error())

					mu.Lock()
					defer mu.Unlock()
				}
			}

			for _, item := range succeedInsertedTask {
				err := c.QueueServ.AddJob(queue.JobStoreKey, queue.Job{
					Key:      "scheduler:task_execution",
					Priority: int(task.Priority),
					Value: queue.JobValue{
						Event:     queue.EventWaiting,
						Id:        item,
						PrevEvent: "",
						Name:      strings.ToLower(string(task.Operation)),
						Platform:  constant.MarketplaceCodeShopee,
						MaxRetry:  int(task.MaxRetries),
					},
				})
				if err != nil {
					log.Printf("error while adding scheduler order list job %v: %v", item, err.Error())
					err := runnerServ.UpdateStatusToError(item)
					if err != nil {
						continue
					}
				}
			}

			key := "scheduler-" + task.ID.Hex() + "-batch-" + task.LastBatch
			ctx := context.Background()
			err = c.Redis.IncrBy(ctx, key, int64(len(succeedInsertedTask))).Err()
			if err != nil {
				log.Printf("error while add job count at job %v: %v", task, err.Error())
			}
			err = c.Redis.Expire(ctx, key, 6*time.Hour).Err()
			if err != nil {
				log.Printf("error while set expire job at job %v: %v", task, err.Error())
			}

			_, err = schedulerTaskServ.Update(&task)
			if err != nil {
				log.Printf("error while update lastBatch execution %v: %v", task.ID, err.Error())
			}
		}(&wg)
	}

	wg.Wait()
	*running = false
}

func (c *SchedulerTaskController) InsertWebhookToQueue(running *bool) {
	*running = true

	list, err := c.WebhookServ.GetAllWebhook(webhooks.FindAllOpts{
		Ctx:    context.Background(),
		Filter: bson.M{"isProcessed": false},
		Limit:  10,
		Skip:   0,
		Sort:   bson.M{"_id": -1},
	})
	if err != nil {
		return
	}

	for _, webhook := range list {
		log.Printf("mpId: %v", webhook.MPStoreId)
		store, err := c.StoreServ.GetByMpId(webhook.MPStoreId, webhook.MarketPlaceCode)
		if err != nil {
			log.Printf("error while get store webhook %v to queue: %v", webhook, err.Error())
		}

		webhook.Data["callbackData"] = true

		insert, err := c.TaskExecServ.Insert(schemas.TaskExecution{
			Batch:           uuid.NewString(),
			Status:          schemas.TaskExecutionStatusWaiting,
			Trigger:         schemas.TaskExecutionTriggerCron,
			Operation:       schemas2.SchedulerTaskOperationTranslateOrder,
			MarketplaceCode: webhook.MarketPlaceCode,
			ErrorDetails:    make([]string, 0),
			RetryCount:      0,
			MaxRetries:      3,
			Company:         store.Company,
			Store:           store.ID,
			RequestParam:    nil,
			RequestHeader:   nil,
			RequestBody:     webhook.Data,
			RequestUrl:      "",
			RequestMethod:   "",
			Priority:        2,
		})
		if err != nil {
			log.Printf("error while insert to db webhook %v: %v", webhook, err.Error())
		}

		err = c.QueueServ.AddJob(queue.JobStoreKey, queue.Job{
			Key:      "scheduler:webhook",
			Priority: 2,
			Value: queue.JobValue{
				Event:     queue.EventWaiting,
				Id:        insert,
				PrevEvent: "",
				Name:      "translate_order",
				Platform:  constant.MarketplaceCodeShopee,
				MaxRetry:  3,
			},
		})
		if err != nil {
			log.Printf("error while insert to queue webhook %v: %v", webhook, err.Error())
		}

		webhook.IsProcessed = true
		_, err = c.WebhookServ.UpdateWebhook(webhook.ID.Hex(), webhook)
		if err != nil {
			log.Printf("error while update webhook %v: %v", webhook, err.Error())
		}
	}

	*running = false
}
