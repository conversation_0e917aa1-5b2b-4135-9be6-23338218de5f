package schemas

import "go.mongodb.org/mongo-driver/bson/primitive"

type SchedulerTaskOperationEnum string

const (
	SchedulerTaskOperationFetchOrderList     SchedulerTaskOperationEnum = "FETCH_ORDERS_LIST"
	SchedulerTaskOperationFetchOrderDetail   SchedulerTaskOperationEnum = "FETCH_ORDERS_DETAILS"
	SchedulerTaskOperationTranslateOrder     SchedulerTaskOperationEnum = "TRANSLATE_ORDER"
	SchedulerTaskOperationSyncStock          SchedulerTaskOperationEnum = "SYNC_STOCK"
	SchedulerTaskOperationFetchProductList   SchedulerTaskOperationEnum = "FETCH_PRODUCTS_LIST"
	SchedulerTaskOperationFetchProductDetail SchedulerTaskOperationEnum = "FETCH_PRODUCTS_DETAILS"
	SchedulerTaskOperationTranslateProduct   SchedulerTaskOperationEnum = "TRANSLATE_PRODUCT"
	SchedulerTaskOperationRefreshToken       SchedulerTaskOperationEnum = "REFRESH_TOKEN"
	SchedulerTaskOperationConfirmOrder       SchedulerTaskOperationEnum = "CONFIRM_ORDER"
	SchedulerTaskOperationFulfilOrder        SchedulerTaskOperationEnum = "FULFIL_ORDER"
	SchedulerTaskOperationStockAdjustment    SchedulerTaskOperationEnum = "STOCK_ADJUSTMENT"
)

type SchedulerTaskFrequencyEnum string

const (
	SchedulerTaskFrequencyMinutes SchedulerTaskFrequencyEnum = "MINUTES"
	SchedulerTaskFrequencyDay     SchedulerTaskFrequencyEnum = "DAY"
	SchedulerTaskFrequencyMonth   SchedulerTaskFrequencyEnum = "MONTH"
)

type SchedulerTask struct {
	ID             primitive.ObjectID         `json:"_id" bson:"_id,omitempty"`
	Name           string                     `json:"name" bson:"name"`
	Description    string                     `json:"description" bson:"description"`
	Operation      SchedulerTaskOperationEnum `json:"operation" bson:"operation"`
	FrequencyUnit  SchedulerTaskFrequencyEnum `json:"frequencyUnit" bson:"frequencyUnit"`
	FrequencyValue uint8                      `json:"frequencyValue" bson:"frequencyValue"`
	FrequencyTime  string                     `json:"frequencyTime" bson:"frequencyTime"`
	FrequencyDate  primitive.DateTime         `json:"frequencyDate" bson:"frequencyDate,omitempty"`
	NextRunTime    primitive.DateTime         `json:"nextRunTime" bson:"nextRunTime"`
	LastRunTime    primitive.DateTime         `json:"lastRunTime" bson:"lastRunTime"`
	LastBatch      string                     `json:"lastBatch" bson:"lastBatch"`
	MaxRetries     uint8                      `json:"maxRetries" bson:"maxRetries"`
	Status         bool                       `bson:"status" json:"status"`
	Priority       uint8                      `bson:"priority" json:"priority"`
}
