package company

import (
	"context"
	"errors"

	"abs-scheduler-go/internal/company/schemas"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type Repository struct {
	collection *mongo.Collection
}

func NewCompanyRepository(db map[string]*mongo.Database) *Repository {
	collection := db["user"].Collection("companies")

	return &Repository{collection}
}

func (r *Repository) FindById(id primitive.ObjectID) (*schemas.Company, error) {
	var result schemas.Company

	err := r.collection.FindOne(context.Background(), bson.M{"_id": id}).Decode(&result)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}

	return &result, nil
}
