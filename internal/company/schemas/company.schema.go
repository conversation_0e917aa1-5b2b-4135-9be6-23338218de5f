package schemas

import "go.mongodb.org/mongo-driver/bson/primitive"

type Company struct {
	ID         string             `json:"_id" bson:"_id,omitempty"`
	Name       string             `json:"name" bson:"name"`
	Status     string             `json:"status" bson:"status"`
	PrivateKey string             `json:"privateKey" bson:"privateKey"`
	ExpiredAt  primitive.DateTime `json:"expiredAt" bson:"expiredAt"`
	CreatedAt  primitive.DateTime `json:"createdAt" bson:"createdAt,omitempty"`
	UpdatedAt  primitive.DateTime `json:"updatedAt" bson:"updatedAt,omitempty"`
}
