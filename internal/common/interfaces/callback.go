package interfaces

import (
	"abs-scheduler-go/internal/callbacks/schemas"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type CallbackRepository interface {
	GetUrlByCompany(company primitive.ObjectID) (*[]schemas.CallbackUrl, error)
	InsertCallback(callback *schemas.Callback) (string, error)
	GetCallbackById(id primitive.ObjectID) (*schemas.Callback, error)
	GetCallbackUrlById(id primitive.ObjectID) (*schemas.CallbackUrl, error)
	UpdateStatus(id primitive.ObjectID, status schemas.CallbackStatus, response map[string]interface{}) error
}
