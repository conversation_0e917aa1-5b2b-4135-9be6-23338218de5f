package interfaces

import (
	"time"

	"abs-scheduler-go/internal/stores/schemas"
	"abs-scheduler-go/pkg/constant"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type UpdateTokenOpts struct {
	Id         string
	Access     string
	Refresh    string
	ExpAccess  time.Time
	ExpRefresh time.Time
}

type StoresRepository interface {
	GetAll(opts *schemas.Store) ([]schemas.Store, error)
	GetByMpId(id string, mpCode constant.MarketplaceCodeEnum) (schemas.Store, error)
	GetById(id primitive.ObjectID) (*schemas.Store, error)
	UpdateToken(opts UpdateTokenOpts) (schemas.Store, error)
}
