package interfaces

import (
	"time"

	"abs-scheduler-go/internal/products/schemas"
	"abs-scheduler-go/pkg/constant"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ProductRepository interface {
	FindProduct(id primitive.ObjectID) (data *schemas.ProductSchema, err error)
	FindProductChildren(id string, marketplaceCode constant.MarketplaceCodeEnum) (data *schemas.ProductChildrenSchema, err error)
	FindProductVariant(productId primitive.ObjectID) (data *schemas.ProductVariantSchema, err error)
	FindProductVariantChildren(id string, marketplaceCode constant.MarketplaceCodeEnum) (data *schemas.ProductVariantChildrenSchema, err error)
	InsertProduct(product schemas.ProductSchema) (id string, err error)
	InsertProductChildren(product schemas.ProductChildrenSchema) (id string, err error)
	InsertProductVariant(product schemas.ProductVariantSchema) (id string, err error)
	InsertProductVariantChildren(product schemas.ProductVariantChildrenSchema) (id string, err error)
	UpdateProduct(product schemas.ProductSchema) (result *schemas.ProductSchema, err error)
	UpdateProductChildren(product schemas.ProductChildrenSchema) (result *schemas.ProductChildrenSchema, err error)
	UpdateProductVariant(product schemas.ProductVariantSchema) (result *schemas.ProductVariantSchema, err error)
	UpdateProductVariantChildren(product schemas.ProductVariantChildrenSchema) (result *schemas.ProductVariantChildrenSchema, err error)
	GetSku(collection string) (seq string, err error)
	DeleteProduct(id primitive.ObjectID) error
	DeleteProductChildren(id primitive.ObjectID, timeRange []time.Time) error
	DeleteProductVariant(id primitive.ObjectID, timeRange []time.Time) error
	DeleteProductVariantChildren(id primitive.ObjectID, timeRange []time.Time) error
	InsertProductStock(product schemas.ProductStockSchema) (id string, err error)
	UpdateProductStock(product schemas.ProductStockSchema) (result *schemas.ProductStockSchema, err error)
	DeleteProductStock(id primitive.ObjectID, timeRange []time.Time) error
	FindProductStock(sku string) (data *schemas.ProductStockSchema, err error)
	AppendProductChildren(id primitive.ObjectID, productChildrenId primitive.ObjectID) (result *schemas.ProductChildrenSchema, err error)
}
