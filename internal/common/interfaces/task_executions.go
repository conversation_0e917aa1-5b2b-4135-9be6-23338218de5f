package interfaces

import (
	"abs-scheduler-go/internal/task_executions/schemas"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type TaskExecutionsRepository interface {
	GetMany(filter bson.M) ([]schemas.TaskExecution, error)
	GetSingle(id primitive.ObjectID) (schemas.TaskExecution, error)
	Insert(data schemas.TaskExecution) (string, error)
	InsertMany(data []schemas.TaskExecution) ([]string, error)
	GetToRun(limit uint16) ([]schemas.TaskExecution, error)
	DeleteMany(ids []string) (interface{}, error)
	UpdateStatus(id primitive.ObjectID, status schemas.TaskExecutionStatusEnum) (interface{}, error)
	UpdateBody(id primitive.ObjectID, params map[string]interface{}) (interface{}, error)
	SetError(id primitive.ObjectID, errorMsg string, retry uint8, maxRetry uint8) (schemas.TaskExecution, error)
}
