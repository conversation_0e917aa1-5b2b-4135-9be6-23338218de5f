package orders

import (
	"context"
	"errors"
	"log"
	"time"

	"abs-scheduler-go/internal/orders/schemas"
	"abs-scheduler-go/pkg/constant"
	"abs-scheduler-go/pkg/util"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Repository struct {
	collection *mongo.Collection
}

func NewOrderRepository(db map[string]*mongo.Database) *Repository {
	collection := db["oms"].Collection("orders")

	return &Repository{collection}
}

func (r *Repository) Upsert(data schemas.Order) (string, error) {
	ctx := context.Background()

	data.CreatedAt = primitive.NewDateTimeFromTime(time.Now())
	data.UpdatedAt = primitive.NewDateTimeFromTime(time.Now())

	var result schemas.Order
	err := r.collection.FindOneAndUpdate(
		ctx,
		bson.M{
			"orderId":         data.OrderId,
			"marketplaceCode": data.MarketplaceCode,
		},
		bson.M{
			"$set": data,
		},
		options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After),
	).Decode(&result)
	if err != nil {
		return "", err
	}

	return result.Id.Hex(), nil
}

func (r *Repository) FindByOrderId(orderId string, marketplaceCode constant.MarketplaceCodeEnum) (*schemas.Order, error) {
	ctx := context.Background()

	var order schemas.Order
	err := r.collection.FindOne(ctx, bson.M{"orderId": orderId, "marketplaceCode": marketplaceCode}).Decode(&order)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}

	return &order, nil
}

func (r *Repository) FindById(id primitive.ObjectID) (*schemas.Order, error) {
	ctx := context.Background()

	var order schemas.Order
	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&order)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}

	return &order, nil
}

func (r *Repository) FindByIds(ids []primitive.ObjectID) (*[]schemas.Order, error) {
	ctx := context.Background()

	orders := make([]schemas.Order, 0)
	curr, err := r.collection.Find(ctx, bson.M{"_id": bson.M{"$in": ids}})
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}

	for curr.Next(ctx) {
		var order schemas.Order
		if err := curr.Decode(&order); err != nil {
			return nil, err
		}
		orders = append(orders, order)
	}

	return &orders, nil
}

func (r *Repository) UpdateByOrderId(orderId string, order map[string]interface{}) (*schemas.Order, error) {
	mock := util.ProcessUpdateFields(order)

	if len(mock) == 0 {
		return nil, nil
	}

	for key, item := range mock {
		log.Printf("item: %v", item)

		// Check if item is already a primitive.DateTime, if so, keep it as is
		if _, ok := item.(primitive.DateTime); ok {
			continue
		}

		// Only try to parse strings as time
		if itemStr, ok := item.(string); ok {
			if valid, err := time.Parse(time.RFC3339, itemStr); err == nil && !valid.IsZero() {
				log.Printf("valid: %v", valid)
				mock[key] = primitive.NewDateTimeFromTime(valid)
			}
		}
	}

	mock["updatedAt"] = primitive.NewDateTimeFromTime(time.Now())

	var result schemas.Order

	err := r.collection.FindOneAndUpdate(
		context.Background(),
		bson.M{"orderId": orderId},
		bson.M{"$set": mock},
		options.FindOneAndUpdate().SetReturnDocument(options.After),
	).Decode(&result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}
