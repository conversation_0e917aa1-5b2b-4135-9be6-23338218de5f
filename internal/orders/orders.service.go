package orders

import (
	"context"
	"errors"
	"log"
	"strings"
	"time"

	"abs-scheduler-go/internal/orders/schemas"
	"abs-scheduler-go/pkg/constant"
	"abs-scheduler-go/pkg/util"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Repository struct {
	collection *mongo.Collection
}

func NewOrderRepository(db map[string]*mongo.Database) *Repository {
	collection := db["oms"].Collection("orders")

	return &Repository{collection}
}

func (r *Repository) Upsert(data schemas.Order) (string, error) {
	ctx := context.Background()

	data.CreatedAt = primitive.NewDateTimeFromTime(time.Now())
	data.UpdatedAt = primitive.NewDateTimeFromTime(time.Now())

	var result schemas.Order
	err := r.collection.FindOneAndUpdate(
		ctx,
		bson.M{
			"orderId":         data.OrderId,
			"marketplaceCode": data.MarketplaceCode,
		},
		bson.M{
			"$set": data,
		},
		options.FindOneAndUpdate().SetUpsert(true).SetReturnDocument(options.After),
	).Decode(&result)
	if err != nil {
		return "", err
	}

	return result.Id.Hex(), nil
}

func (r *Repository) FindByOrderId(orderId string, marketplaceCode constant.MarketplaceCodeEnum) (*schemas.Order, error) {
	ctx := context.Background()

	var order schemas.Order
	err := r.collection.FindOne(ctx, bson.M{"orderId": orderId, "marketplaceCode": marketplaceCode}).Decode(&order)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}

	return &order, nil
}

func (r *Repository) FindById(id primitive.ObjectID) (*schemas.Order, error) {
	ctx := context.Background()

	var order schemas.Order
	err := r.collection.FindOne(ctx, bson.M{"_id": id}).Decode(&order)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}

	return &order, nil
}

func (r *Repository) FindByIds(ids []primitive.ObjectID) (*[]schemas.Order, error) {
	ctx := context.Background()

	orders := make([]schemas.Order, 0)
	curr, err := r.collection.Find(ctx, bson.M{"_id": bson.M{"$in": ids}})
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}

	for curr.Next(ctx) {
		var order schemas.Order
		if err := curr.Decode(&order); err != nil {
			return nil, err
		}
		orders = append(orders, order)
	}

	return &orders, nil
}

func (r *Repository) UpdateByOrderId(orderId string, order map[string]interface{}) (*schemas.Order, error) {
	mock := util.ProcessUpdateFields(order)

	if len(mock) == 0 {
		return nil, nil
	}

	for key, item := range mock {
		log.Printf("Processing field %s with value: %v (type: %T)", key, item, item)

		// Check if item is already a primitive.DateTime, if so, keep it as is
		if _, ok := item.(primitive.DateTime); ok {
			log.Printf("Field %s is already primitive.DateTime, keeping as is", key)
			continue
		}

		// Check if this field should be a DateTime based on field name patterns
		isDateTimeField := strings.Contains(key, "Time") ||
			strings.Contains(key, "Date") ||
			strings.Contains(key, "pickupTime") ||
			strings.Contains(key, "deliveredTime") ||
			strings.Contains(key, "cancellationTime") ||
			strings.Contains(key, "completeTime") ||
			strings.Contains(key, "orderTime") ||
			strings.Contains(key, "payTime") ||
			strings.Contains(key, "shipByDate") ||
			strings.Contains(key, "requestCancellationTime") ||
			strings.Contains(key, "createdAt") ||
			strings.Contains(key, "updatedAt") ||
			strings.Contains(key, "expiredAt")

		if isDateTimeField {
			switch v := item.(type) {
			case string:
				// Try to parse string as RFC3339 time
				if valid, err := time.Parse(time.RFC3339, v); err == nil && !valid.IsZero() {
					log.Printf("Converted string %s to DateTime for field %s", v, key)
					mock[key] = primitive.NewDateTimeFromTime(valid)
				} else {
					log.Printf("Failed to parse string %s as time for field %s: %v", v, key, err)
				}
			case float64:
				// Handle Unix timestamp (seconds or milliseconds)
				if v > 0 {
					var timestamp time.Time
					if v > 1e12 { // Likely milliseconds
						timestamp = time.Unix(0, int64(v)*int64(time.Millisecond))
						log.Printf("Converted Unix timestamp (ms) %f to DateTime for field %s", v, key)
					} else { // Likely seconds
						timestamp = time.Unix(int64(v), 0)
						log.Printf("Converted Unix timestamp (s) %f to DateTime for field %s", v, key)
					}
					mock[key] = primitive.NewDateTimeFromTime(timestamp)
				}
			case int64:
				// Handle Unix timestamp (seconds or milliseconds)
				if v > 0 {
					var timestamp time.Time
					if v > 1e12 { // Likely milliseconds
						timestamp = time.Unix(0, v*int64(time.Millisecond))
						log.Printf("Converted Unix timestamp (ms) %d to DateTime for field %s", v, key)
					} else { // Likely seconds
						timestamp = time.Unix(v, 0)
						log.Printf("Converted Unix timestamp (s) %d to DateTime for field %s", v, key)
					}
					mock[key] = primitive.NewDateTimeFromTime(timestamp)
				}
			case int:
				// Handle Unix timestamp (seconds or milliseconds)
				if v > 0 {
					var timestamp time.Time
					if int64(v) > 1e12 { // Likely milliseconds
						timestamp = time.Unix(0, int64(v)*int64(time.Millisecond))
						log.Printf("Converted Unix timestamp (ms) %d to DateTime for field %s", v, key)
					} else { // Likely seconds
						timestamp = time.Unix(int64(v), 0)
						log.Printf("Converted Unix timestamp (s) %d to DateTime for field %s", v, key)
					}
					mock[key] = primitive.NewDateTimeFromTime(timestamp)
				}
			default:
				log.Printf("Unknown type %T for DateTime field %s, keeping as is", item, key)
			}
		}
	}

	mock["updatedAt"] = primitive.NewDateTimeFromTime(time.Now())

	var result schemas.Order

	err := r.collection.FindOneAndUpdate(
		context.Background(),
		bson.M{"orderId": orderId},
		bson.M{"$set": mock},
		options.FindOneAndUpdate().SetReturnDocument(options.After),
	).Decode(&result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}
