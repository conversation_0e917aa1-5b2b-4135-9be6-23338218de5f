package schemas

import (
	"abs-scheduler-go/pkg/constant"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PackageStatusEnum string

const (
	PackageStatusToFulfil   PackageStatusEnum = "TO_FULFIL"
	PackageStatusProcessing PackageStatusEnum = "PROCESSING"
	PackageStatusFulfilling PackageStatusEnum = "FULFILLING"
	PackageStatusCompleted  PackageStatusEnum = "COMPLETED"
	PackageStatusCancelled  PackageStatusEnum = "CANCELLED"
)

type FeeEnum string

const (
	FeePlatform FeeEnum = "PLATFORM"
	FeeLogistic FeeEnum = "LOGISTIC"
	FeePayment  FeeEnum = "PAYMENT"
)

type DiscountEnum string

const (
	DiscountVoucher  DiscountEnum = "VOUCHER"
	DiscountSeller   DiscountEnum = "SELLER"
	DiscountPlatform DiscountEnum = "PLATFORM"
)

type OrderRecipient struct {
	Name        string `json:"name" bson:"name"`
	Phone       string `json:"phone" bson:"phone"`
	Region      string `json:"region" bson:"region"`
	City        string `json:"city" bson:"city"`
	District    string `json:"district" bson:"district"`
	Zipcode     string `json:"zipcode" bson:"zipcode"`
	FullAddress string `json:"fullAddress" bson:"fullAddress"`
}

type OrderShipment struct {
	LogisticChannelId     string             `json:"logisticChannelId" bson:"logisticChannelId"`
	ShippingCarrier       string             `json:"shippingCarrier" bson:"shippingCarrier"`
	PackageNumber         string             `json:"packageNumber" bson:"packageNumber"`
	TrackingNumber        string             `json:"trackingNumber" bson:"trackingNumber"`
	PickupCode            string             `json:"pickupCode" bson:"pickupCode"`
	ParcelWeight          uint16             `json:"parcelWeight" bson:"parcelWeight"`
	PackageStatus         string             `json:"packageStatus" bson:"packageStatus"`
	DeliveredTime         primitive.DateTime `json:"deliveredTime,omitempty" bson:"deliveredTime,omitempty"`
	EstimatedShippingFee  float32            `json:"estimatedShippingFee" bson:"estimatedShippingFee"`
	ActualShippingFee     float32            `json:"actualShippingFee" bson:"actualShippingFee"`
	ActualDiscShippingFee float32            `json:"actualDiscShippingFee" bson:"actualDiscShippingFee"`
	ShipByDate            primitive.DateTime `json:"shipByDate,omitempty" bson:"shipByDate,omitempty"`
	PickupTime            primitive.DateTime `json:"pickupTime,omitempty" bson:"pickupTime,omitempty"`
	Recipient             OrderRecipient     `json:"recipient" bson:"recipient"`
	Note                  string             `json:"note" bson:"note"`
}

type OrderCancellation struct {
	Reason                  string             `json:"reason" bson:"reason"`
	CancelBy                string             `json:"cancelBy" bson:"cancelBy"`
	RequestCancellationTime primitive.DateTime `json:"requestCancellationTime,omitempty" bson:"requestCancellationTime,omitempty"`
	CancellationTime        primitive.DateTime `json:"cancellationTime,omitempty" bson:"cancellationTime,omitempty"`
}

type OrderTimeline struct {
	OrderTime               primitive.DateTime `json:"orderTime,omitempty" bson:"orderTime,omitempty"`
	ShipByDate              primitive.DateTime `json:"shipByDate,omitempty" bson:"shipByDate,omitempty"`
	PayTime                 primitive.DateTime `json:"payTime,omitempty" bson:"payTime,omitempty"`
	PickupTime              primitive.DateTime `json:"pickupTime,omitempty" bson:"pickupTime,omitempty"`
	DeliveredTime           primitive.DateTime `json:"deliveredTime,omitempty" bson:"deliveredTime,omitempty"`
	RequestCancellationTime primitive.DateTime `json:"requestCancellationTime,omitempty" bson:"requestCancellationTime,omitempty"`
	CancellationTime        primitive.DateTime `json:"cancellationTime,omitempty" bson:"cancellationTime,omitempty"`
	CompleteTime            primitive.DateTime `json:"completeTime,omitempty" bson:"completeTime,omitempty"`
}

type OrderPromotion struct {
	PromotionId string `json:"promotionId" bson:"promotionId"`
	Type        string `json:"type" bson:"type"`
	GroupId     string `json:"groupId" bson:"groupId"`
	AddonDeal   string `json:"addonDeal" bson:"addonDeal"`
	AddonDealId string `json:"addonDealId" bson:"addonDealId"`
}

type OrderFee struct {
	FeeType      FeeEnum `json:"feeType" bson:"feeType"`
	FeeName      string  `json:"feeName" bson:"feeName"`
	Amount       float32 `json:"amount" bson:"amount"`
	Currency     string  `json:"currency" bson:"currency"`
	IsBuyerPaid  bool    `json:"isBuyerPaid" bson:"isBuyerPaid"`
	IsSellerPaid bool    `json:"isSellerPaid" bson:"isSellerPaid"`
}

type OrderDiscount struct {
	DiscountType      DiscountEnum `json:"discountType" bson:"discountType"`
	DiscountName      string       `json:"discountName" bson:"discountName"`
	Code              string       `json:"code" bson:"code"`
	Amount            float32      `json:"amount" bson:"amount"`
	Currency          string       `json:"currency" bson:"currency"`
	IsPlatformSubsidy bool         `json:"isPlatformSubsidy" bson:"isPlatformSubsidy"`
	IsSellerDiscount  bool         `json:"isSellerDiscount" bson:"isSellerDiscount"`
}

type OrderPayment struct {
	Method                  string          `json:"method" bson:"method"`
	TotalAmount             float32         `json:"totalAmount" bson:"totalAmount"`
	DiscountSellerAmount    float32         `json:"discountSellerAmount" bson:"discountSellerAmount"`
	VoucherSellerDiscount   float32         `json:"voucherSellerDiscount" bson:"voucherSellerDiscount"`
	DiscountPlatformAmount  float32         `json:"discountPlatformAmount" bson:"discountPlatformAmount"`
	VoucherPlatformDiscount float32         `json:"voucherPlatformDiscount" bson:"voucherPlatformDiscount"`
	PaymentStatus           string          `json:"paymentStatus" bson:"paymentStatus"`
	TotalFee                float32         `json:"totalFee" bson:"totalFee"`
	ShipmentFee             float32         `json:"shipmentFee" bson:"shipmentFee"`
	ShipmentFeeDiscount     float32         `json:"shipmentFeeDiscount" bson:"shipmentFeeDiscount"`
	Subtotal                float32         `json:"subtotal" bson:"subtotal"`
	FeeBreakdown            []OrderFee      `json:"feeBreakdown" bson:"feeBreakdown"`
	DiscountBreakdown       []OrderDiscount `json:"discountBreakdown" bson:"discountBreakdown"`
}

type DiscountBreakdownEnum string

const (
	DiscountBreakdownVoucher DiscountBreakdownEnum = "VOUCHER"
	DiscountBreakdownPromo   DiscountBreakdownEnum = "PROMO"
)

type DiscountItemBreakdown struct {
	DiscountType DiscountBreakdownEnum `json:"discountType" bson:"discountType"`
	DiscountCode string                `json:"discountCode" bson:"discountCode"`
	Amount       float32               `json:"amount" bson:"amount"`
}

type OrderItems struct {
	ItemName                     string                  `json:"itemName" bson:"itemName"`
	ItemId                       string                  `json:"itemId" bson:"itemId"`
	MpId                         string                  `json:"mpId" bson:"mpId"`
	ItemSku                      string                  `json:"itemSku" bson:"itemSku"`
	MpSku                        string                  `json:"mpSku" bson:"mpSku"`
	VariantName                  string                  `json:"variantName" bson:"variantName"`
	VariantId                    string                  `json:"variantId" bson:"variantId"`
	VariantSku                   string                  `json:"variantSku" bson:"variantSku"`
	Quantity                     uint16                  `json:"quantity" bson:"quantity"`
	OriginalPrice                float32                 `json:"originalPrice" bson:"originalPrice"`
	GrandTotal                   float32                 `json:"grandTotal" bson:"grandTotal"`
	SellerDiscount               float32                 `json:"sellerDiscount" bson:"sellerDiscount"`
	SellerVoucherDiscount        float32                 `json:"sellerVoucherDiscount" bson:"sellerVoucherDiscount"`
	MarketplaceDiscount          float32                 `json:"marketplaceDiscount" bson:"marketplaceDiscount"`
	CoinRebate                   float32                 `json:"coinRebate" bson:"coinRebate"`
	Weight                       float32                 `json:"weight" bson:"weight"`
	Promotion                    OrderPromotion          `json:"promotion" bson:"promotion"`
	PackageStatus                PackageStatusEnum       `json:"packageStatus" bson:"packageStatus"`
	Tax                          float64                 `json:"tax" bson:"tax"`
	IsGift                       bool                    `json:"isGift" bson:"isGift"`
	ImageUrl                     []string                `json:"imageUrl" bson:"imageUrl"`
	SellerDiscountBreakdown      []DiscountItemBreakdown `json:"sellerDiscountBreakdown" bson:"sellerDiscountBreakdown"`
	MarketplaceDiscountBreakdown []DiscountItemBreakdown `json:"marketplaceDiscountBreakdown" bson:"marketplaceDiscountBreakdown"`
}

type IncompleteData struct {
	Property string   `json:"property" bson:"property"`
	Reason   []string `json:"reason" bson:"reason"`
}

type Order struct {
	Id              primitive.ObjectID           `json:"id" bson:"_id"`
	OrderId         string                       `json:"orderId" bson:"orderId"`
	MarketplaceCode constant.MarketplaceCodeEnum `json:"marketplaceCode" bson:"marketplaceCode"`
	Items           []OrderItems                 `json:"items" bson:"items"`
	Currency        string                       `json:"currency" bson:"currency"`
	OrderStatus     string                       `json:"orderStatus" bson:"orderStatus"`
	Payment         OrderPayment                 `json:"payment" bson:"payment"`
	Shipment        []OrderShipment              `json:"shipment" bson:"shipment"`
	NoteUpdateTime  string                       `json:"noteUpdateTime" bson:"noteUpdateTime"`
	SplitUp         bool                         `json:"splitUp" bson:"splitUp"`
	Cancellation    OrderCancellation            `json:"cancellation" bson:"cancellation"`
	Timeline        OrderTimeline                `json:"timeline" bson:"timeline"`
	CompanyId       primitive.ObjectID           `json:"companyId" bson:"companyId"`
	StoreId         primitive.ObjectID           `json:"storeId" bson:"storeId"`
	MpStoreId       string                       `json:"mpStoreId" bson:"mpStoreId"`
	IncompleteData  []IncompleteData             `json:"incompleteData" bson:"incompleteData"`
	CreatedAt       primitive.DateTime           `bson:"createdAt,omitempty" json:"createdAt,omitempty"`
	UpdatedAt       primitive.DateTime           `bson:"updatedAt,omitempty" json:"updatedAt,omitempty"`
}
